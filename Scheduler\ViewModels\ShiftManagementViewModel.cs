// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections
using RecurrenceType = Scheduler.Models.RecurrenceType;  // 重复类型别名 / Recurrence type alias

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 班次管理视图模型 / Shift Management ViewModel
    /// 处理班次创建和管理功能
    /// Handles shift creation and management functionality
    /// </summary>
    public partial class ShiftManagementViewModel : BaseViewModel
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知服务实例 / Notification service instance
        private readonly NotificationService _notificationService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 雇主集合 / Employers collection
        [ObservableProperty]
        private ObservableCollection<Employer> employers = new();

        // 选中的雇主 / Selected employer
        [ObservableProperty]
        private Employer? selectedEmployer;

        // 时薪 / Hourly rate
        [ObservableProperty]
        private decimal hourlyRate;

        // 开始日期 / Start date
        [ObservableProperty]
        private DateTime startDate = DateTime.Today;

        // 开始时间 / Start time
        [ObservableProperty]
        private TimeSpan startTime = new(9, 0, 0);

        // 结束日期 / End date
        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        // 结束时间 / End time
        [ObservableProperty]
        private TimeSpan endTime = new(17, 0, 0);

        // 工作时长（小时） / Work duration in hours
        [ObservableProperty]
        private double workDuration = 0.0;

        // 工作时长文本 / Work duration text
        [ObservableProperty]
        private string workDurationText = "";

        // 工作小时数 / Work hours
        [ObservableProperty]
        private double workHours;

        [ObservableProperty]
        private decimal estimatedEarnings;

        [ObservableProperty]
        private bool isCalculatingFromDuration = false; // 标记是否正在从时长计算

        [ObservableProperty]
        private bool isCalculatingFromEndTime = false; // 标记是否正在从结束时间计算

        [ObservableProperty]
        private string notes = string.Empty;

        [ObservableProperty]
        private ObservableCollection<RecurrenceOption> recurrenceOptions = new();

        [ObservableProperty]
        private RecurrenceOption? selectedRecurrence;

        [ObservableProperty]
        private bool isRecurrenceEnabled = false;

        [ObservableProperty]
        private DateTime recurrenceEndDate = DateTime.Today.AddMonths(1);

        [ObservableProperty]
        private int? maxOccurrences;

        [ObservableProperty]
        private string recurrenceDescription = string.Empty;

        [ObservableProperty]
        private string recurrencePreview = string.Empty;

        [ObservableProperty]
        private int estimatedShiftsCount = 0;

        [ObservableProperty]
        private string timeRangeDisplay = string.Empty;

        [ObservableProperty]
        private bool hasConflicts;

        [ObservableProperty]
        private ObservableCollection<ShiftConflict> conflictingShifts = new();

        #region 通知设置属性 (Notification Settings Properties)

        [ObservableProperty]
        private bool isReminderEnabled = true;

        [ObservableProperty]
        private int selectedReminderMinutes = 15;

        [ObservableProperty]
        private ObservableCollection<ReminderTimeOption> reminderTimeOptions = new();

        [ObservableProperty]
        private ReminderTimeOption? selectedReminderTimeOption;

        #endregion

        public bool IsEmployerSelected => SelectedEmployer != null;
        public bool IsRecurrenceSelected => IsRecurrenceEnabled && SelectedRecurrence != null && SelectedRecurrence.Type != RecurrenceType.None;
        public bool CanCreateShift => SelectedEmployer != null && !HasConflicts && WorkDuration > 0 && WorkDuration <= 24 && !string.IsNullOrWhiteSpace(WorkDurationText);

        /// <summary>
        /// 通知设置是否因重复排班而被禁用 (Whether notification settings are disabled due to recurrence)
        /// </summary>
        public bool IsNotificationDisabledByRecurrence => IsRecurrenceSelected;

        /// <summary>
        /// 通知设置是否可用 (Whether notification settings are available)
        /// </summary>
        public bool IsNotificationSettingsEnabled => !IsNotificationDisabledByRecurrence;

        public ShiftManagementViewModel(DatabaseService databaseService, NotificationService notificationService, NotificationScheduler notificationScheduler, LocalizationService localizationService)
        {
            _databaseService = databaseService;
            _notificationService = notificationService;
            _notificationScheduler = notificationScheduler;
            _localizationService = localizationService;

            Title = "Shift Management";

            InitializeRecurrenceOptions();
            InitializeReminderTimeOptions();

            // Initialize work duration text as empty
            WorkDurationText = "";

            // Update time range display initially
            UpdateTimeRangeDisplay();
        }

        public async Task InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ShiftManagementViewModel: 开始初始化");
                IsBusy = true;

                await LoadEmployersAsync();

                System.Diagnostics.Debug.WriteLine($"ShiftManagementViewModel: 初始化完成，加载了 {Employers.Count} 个雇主");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ShiftManagementViewModel 初始化失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                await ShowErrorAsync($"初始化失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadEmployersAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载雇主列表");

                var employerList = await _databaseService.GetEmployersAsync();

                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {employerList?.Count ?? 0} 个雇主");

                Employers.Clear();

                if (employerList != null)
                {
                    foreach (var employer in employerList)
                    {
                        Employers.Add(employer);
                        System.Diagnostics.Debug.WriteLine($"添加雇主: {employer.Name}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"雇主列表加载完成，共 {Employers.Count} 个雇主");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载雇主列表失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                await ShowErrorAsync($"加载雇主列表失败: {ex.Message}");
            }
        }

        private void InitializeRecurrenceOptions()
        {
            RecurrenceOptions.Clear();
            RecurrenceOptions.Add(new RecurrenceOption
            {
                Type = RecurrenceType.Daily,
                DisplayName = "Daily Repeat",
                Description = "Repeat the same shift every day"
            });
            RecurrenceOptions.Add(new RecurrenceOption
            {
                Type = RecurrenceType.Weekly,
                DisplayName = "Weekly Repeat",
                Description = "Repeat the same shift pattern every week"
            });
            RecurrenceOptions.Add(new RecurrenceOption
            {
                Type = RecurrenceType.Monthly,
                DisplayName = "Monthly Repeat",
                Description = "Repeat the same shift arrangement every month"
            });

            SelectedRecurrence = RecurrenceOptions.First();
            UpdateRecurrenceDescription();
        }

        /// <summary>
        /// 初始化提醒时间选项
        /// Initialize reminder time options
        /// </summary>
        private void InitializeReminderTimeOptions()
        {
            ReminderTimeOptions.Clear();
            var options = ReminderTimeOption.GetDefaultOptions();

            foreach (var option in options)
            {
                ReminderTimeOptions.Add(option);
            }

            // 设置默认选择为15分钟前
            SelectedReminderTimeOption = ReminderTimeOptions.FirstOrDefault(x => x.Minutes == 15);
            if (SelectedReminderTimeOption != null)
            {
                SelectedReminderMinutes = SelectedReminderTimeOption.Minutes;
            }
        }

        partial void OnSelectedEmployerChanged(Employer? value)
        {
            if (value != null)
            {
                HourlyRate = value.HourlyRate;
                CalculateEstimatedEarnings();
            }
            OnPropertyChanged(nameof(IsEmployerSelected));
            OnPropertyChanged(nameof(CanCreateShift));
        }

        partial void OnSelectedReminderTimeOptionChanged(ReminderTimeOption? value)
        {
            if (value != null)
            {
                SelectedReminderMinutes = value.Minutes;
            }
        }

        partial void OnIsReminderEnabledChanged(bool value)
        {
            // 当通知开关变化时，可以在这里添加额外的逻辑
            System.Diagnostics.Debug.WriteLine($"班次提醒通知已{(value ? "启用" : "禁用")}");
        }

        partial void OnStartDateChanged(DateTime value)
        {
            if (EndDate < value)
            {
                EndDate = value;
            }

            if (!IsCalculatingFromEndTime)
            {
                CalculateEndTimeFromDuration();
            }
            else
            {
                CalculateWorkHours();
            }

            CheckForConflictsAsync();
            UpdateRecurrencePreview();
        }

        partial void OnStartTimeChanged(TimeSpan value)
        {
            if (!IsCalculatingFromEndTime)
            {
                CalculateEndTimeFromDuration();
            }
            else
            {
                CalculateWorkHours();
            }

            CheckForConflictsAsync();
        }

        partial void OnEndDateChanged(DateTime value)
        {
            if (value < StartDate)
            {
                StartDate = value;
            }

            if (!IsCalculatingFromDuration)
            {
                IsCalculatingFromEndTime = true;
                CalculateWorkDurationFromEndTime();
                IsCalculatingFromEndTime = false;
            }

            CheckForConflictsAsync();
        }

        partial void OnEndTimeChanged(TimeSpan value)
        {
            if (!IsCalculatingFromDuration)
            {
                IsCalculatingFromEndTime = true;
                CalculateWorkDurationFromEndTime();
                IsCalculatingFromEndTime = false;
            }

            CheckForConflictsAsync();
        }

        partial void OnWorkDurationChanged(double value)
        {
            if (value < 0)
            {
                WorkDuration = 0;
                WorkDurationText = "0";
                OnPropertyChanged(nameof(CanCreateShift));
                return;
            }

            if (value > 24)
            {
                WorkDuration = 24; // 限制最大24小时
                WorkDurationText = "24";
                OnPropertyChanged(nameof(CanCreateShift));
                return;
            }

            // Update text display (avoid circular updates)
            if (!IsCalculatingFromEndTime)
            {
                var currentTextValue = 0.0;
                if (double.TryParse(WorkDurationText ?? "0", out currentTextValue))
                {
                    if (Math.Abs(value - currentTextValue) > 0.01)
                    {
                        WorkDurationText = value > 0 ? value.ToString("F1") : "";
                    }
                }
                else
                {
                    WorkDurationText = value > 0 ? value.ToString("F1") : "";
                }
            }

            if (!IsCalculatingFromEndTime)
            {
                IsCalculatingFromDuration = true;
                CalculateEndTimeFromDuration();
                IsCalculatingFromDuration = false;
            }

            OnPropertyChanged(nameof(CanCreateShift));
        }

        partial void OnWorkDurationTextChanged(string value)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"WorkDurationText changed: '{value}'");

                if (string.IsNullOrWhiteSpace(value))
                {
                    System.Diagnostics.Debug.WriteLine("WorkDurationText is empty, setting WorkDuration to 0");
                    WorkDuration = 0;
                    return;
                }

                // Remove possible non-numeric characters (except decimal point)
                var cleanValue = value.Trim();

                if (double.TryParse(cleanValue, out double duration))
                {
                    System.Diagnostics.Debug.WriteLine($"Parse successful: {duration}, current WorkDuration: {WorkDuration}");

                    if (Math.Abs(duration - WorkDuration) > 0.01) // Avoid circular updates
                    {
                        System.Diagnostics.Debug.WriteLine($"Updating WorkDuration: {WorkDuration} -> {duration}");
                        WorkDuration = duration;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Parse failed: '{cleanValue}'");
                    // If parsing fails, don't do anything, let user continue typing
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"WorkDurationText processing exception: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Exception stack: {ex.StackTrace}");

                // When exception occurs, restore to safe value
                try
                {
                    WorkDurationText = WorkDuration > 0 ? WorkDuration.ToString("F1") : "";
                }
                catch
                {
                    WorkDurationText = "";
                    WorkDuration = 0;
                }
            }
        }

        partial void OnHourlyRateChanged(decimal value)
        {
            CalculateEstimatedEarnings();
        }

        partial void OnSelectedRecurrenceChanged(RecurrenceOption? value)
        {
            UpdateRecurrenceDescription();
            UpdateRecurrencePreview();
            OnPropertyChanged(nameof(IsRecurrenceSelected));
            OnPropertyChanged(nameof(IsNotificationDisabledByRecurrence));
            OnPropertyChanged(nameof(IsNotificationSettingsEnabled));
        }

        partial void OnIsRecurrenceEnabledChanged(bool value)
        {
            if (!value)
            {
                RecurrencePreview = string.Empty;
                EstimatedShiftsCount = 0;
            }
            else
            {
                UpdateRecurrencePreview();

                // 当启用重复排班时，自动禁用通知设置 (When recurring is enabled, automatically disable notification settings)
                if (value && IsReminderEnabled)
                {
                    IsReminderEnabled = false;
                    System.Diagnostics.Debug.WriteLine("重复排班已启用，自动禁用通知设置");
                }
            }
            OnPropertyChanged(nameof(IsRecurrenceSelected));
            OnPropertyChanged(nameof(IsNotificationDisabledByRecurrence)); // 通知UI更新 (Notify UI update)
            OnPropertyChanged(nameof(IsNotificationSettingsEnabled)); // 通知UI更新 (Notify UI update)
        }

        partial void OnRecurrenceEndDateChanged(DateTime value)
        {
            if (value < StartDate)
            {
                RecurrenceEndDate = StartDate.AddDays(1);
            }
            UpdateRecurrencePreview();
        }

        partial void OnMaxOccurrencesChanged(int? value)
        {
            UpdateRecurrencePreview();
        }

        private void CalculateWorkHours()
        {
            var startDateTime = StartDate.Add(StartTime);
            var endDateTime = EndDate.Add(EndTime);

            if (endDateTime > startDateTime)
            {
                WorkHours = (endDateTime - startDateTime).TotalHours;
            }
            else
            {
                WorkHours = 0;
            }

            CalculateEstimatedEarnings();
            UpdateTimeRangeDisplay();
        }

        /// <summary>
        /// 根据工作时长计算结束时间
        /// </summary>
        private void CalculateEndTimeFromDuration()
        {
            try
            {
                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = startDateTime.AddHours(WorkDuration);

                // 更新结束日期和时间
                EndDate = endDateTime.Date;
                EndTime = endDateTime.TimeOfDay;

                // 更新工作小时数（应该等于工作时长）
                WorkHours = WorkDuration;
                CalculateEstimatedEarnings();
                UpdateTimeRangeDisplay();

                System.Diagnostics.Debug.WriteLine($"从时长计算结束时间: {WorkDuration}小时 -> {endDateTime:yyyy-MM-dd HH:mm}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算结束时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据结束时间计算工作时长
        /// </summary>
        private void CalculateWorkDurationFromEndTime()
        {
            try
            {
                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = EndDate.Add(EndTime);

                if (endDateTime > startDateTime)
                {
                    var duration = (endDateTime - startDateTime).TotalHours;
                    WorkDuration = Math.Round(duration, 2); // Keep 2 decimal places
                    WorkDurationText = WorkDuration > 0 ? WorkDuration.ToString("F1") : ""; // Update text display
                    WorkHours = duration;
                }
                else
                {
                    WorkDuration = 0;
                    WorkDurationText = "";
                    WorkHours = 0;
                }

                CalculateEstimatedEarnings();
                UpdateTimeRangeDisplay();

                System.Diagnostics.Debug.WriteLine($"从结束时间计算时长: {endDateTime:yyyy-MM-dd HH:mm} -> {WorkDuration}小时");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算工作时长失败: {ex.Message}");
            }
        }

        /// <summary>
        /// Update time range display
        /// </summary>
        private void UpdateTimeRangeDisplay()
        {
            try
            {
                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = EndDate.Add(EndTime);

                if (WorkDuration > 0)
                {
                    var isSameDay = StartDate.Date == EndDate.Date;

                    if (isSameDay)
                    {
                        TimeRangeDisplay = $"📅 {startDateTime:MMMM dd, yyyy} {startDateTime:HH:mm} - {endDateTime:HH:mm}";
                    }
                    else
                    {
                        TimeRangeDisplay = $"📅 {startDateTime:MMMM dd, yyyy HH:mm} - {endDateTime:MMMM dd, yyyy HH:mm} (Multi-day)";
                    }

                    // Add work duration warnings
                    if (WorkDuration >= 24)
                    {
                        TimeRangeDisplay += " ⚠️ Work duration exceeds 24 hours";
                    }
                    else if (WorkDuration >= 12)
                    {
                        TimeRangeDisplay += " ⚠️ Long work duration";
                    }
                }
                else
                {
                    TimeRangeDisplay = "Please set valid work time";
                }
            }
            catch (Exception ex)
            {
                TimeRangeDisplay = "Time calculation error";
                System.Diagnostics.Debug.WriteLine($"Failed to update time range display: {ex.Message}");
            }
        }

        private void CalculateEstimatedEarnings()
        {
            EstimatedEarnings = (decimal)WorkHours * HourlyRate;
        }

        private void UpdateRecurrenceDescription()
        {
            if (SelectedRecurrence == null)
            {
                RecurrenceDescription = string.Empty;
                return;
            }

            RecurrenceDescription = SelectedRecurrence.Description ?? string.Empty;
        }

        private void UpdateRecurrencePreview()
        {
            if (!IsRecurrenceEnabled || SelectedRecurrence == null)
            {
                RecurrencePreview = string.Empty;
                EstimatedShiftsCount = 0;
                return;
            }

            var shiftsCount = CalculateEstimatedShiftsCount();
            EstimatedShiftsCount = shiftsCount;

            var nextShiftDate = GetNextShiftDate(StartDate);
            var previewText = $"Next shift: {nextShiftDate:MMMM dd, yyyy}";

            if (shiftsCount > 1)
            {
                var lastShiftDate = GetLastShiftDate();
                previewText += $"\nLast shift: {lastShiftDate:MMMM dd, yyyy}";
            }

            RecurrencePreview = previewText;
        }

        private int CalculateEstimatedShiftsCount()
        {
            if (!IsRecurrenceEnabled || SelectedRecurrence == null)
                return 0;

            var count = 1; // 包括初始班次
            var currentDate = StartDate;

            while (true)
            {
                currentDate = GetNextShiftDate(currentDate);

                if (currentDate > RecurrenceEndDate)
                    break;

                if (MaxOccurrences.HasValue && count >= MaxOccurrences.Value)
                    break;

                count++;

                // 防止无限循环
                if (count > 1000)
                    break;
            }

            return count;
        }

        private DateTime GetNextShiftDate(DateTime currentDate)
        {
            if (SelectedRecurrence == null)
                return currentDate;

            return SelectedRecurrence.Type switch
            {
                RecurrenceType.Daily => currentDate.AddDays(1),
                RecurrenceType.Weekly => currentDate.AddDays(7),
                RecurrenceType.Monthly => currentDate.AddMonths(1),
                _ => currentDate
            };
        }

        private DateTime GetLastShiftDate()
        {
            if (!IsRecurrenceEnabled || SelectedRecurrence == null)
                return StartDate;

            var currentDate = StartDate;
            var count = 1;

            while (true)
            {
                var nextDate = GetNextShiftDate(currentDate);

                if (nextDate > RecurrenceEndDate)
                    break;

                if (MaxOccurrences.HasValue && count >= MaxOccurrences.Value)
                    break;

                currentDate = nextDate;
                count++;

                // 防止无限循环
                if (count > 1000)
                    break;
            }

            return currentDate;
        }

        private async void CheckForConflictsAsync()
        {
            if (SelectedEmployer == null) return;

            try
            {
                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = EndDate.Add(EndTime);
                
                var existingShifts = await _databaseService.GetShiftsByDateRangeAsync(StartDate, EndDate);
                var conflicts = existingShifts.Where(s => 
                    s.StartTime < endDateTime && s.EndTime > startDateTime).ToList();

                ConflictingShifts.Clear();
                foreach (var conflict in conflicts)
                {
                    ConflictingShifts.Add(new ShiftConflict
                    {
                        ConflictingShift = conflict,
                        ConflictDescription = $"Time overlap with {conflict.EmployerName} shift"
                    });
                }

                HasConflicts = ConflictingShifts.Any();
                OnPropertyChanged(nameof(CanCreateShift));
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"Error checking conflicts: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task CreateShiftAsync()
        {
            if (SelectedEmployer == null)
            {
                await ShowErrorAsync("Please select an employer");
                return;
            }

            // 检查是否可以创建班次（包括冲突检测）
            if (!CanCreateShift)
            {
                if (HasConflicts)
                {
                    await ShowErrorAsync("Cannot create shift due to time conflicts with existing shifts");
                }
                else
                {
                    await ShowErrorAsync("Please check all required fields and ensure no conflicts exist");
                }
                return;
            }

            try
            {
                IsBusy = true;

                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = EndDate.Add(EndTime);

                var newShift = new Shift
                {
                    EmployerId = SelectedEmployer.Id,
                    StartTime = startDateTime,
                    EndTime = endDateTime,
                    HourlyRate = HourlyRate,
                    Status = ShiftStatus.Scheduled,
                    Notes = Notes,
                    CreatedAt = DateTime.Now,
                    // 设置通知相关属性
                    IsReminderEnabled = IsReminderEnabled,
                    ReminderMinutes = SelectedReminderMinutes
                };

                // Handle recurring shifts first to set properties
                if (IsRecurrenceEnabled && SelectedRecurrence?.Type != RecurrenceType.None)
                {
                    newShift.IsRecurring = true;
                    newShift.RecurrenceType = SelectedRecurrence.Type;
                    newShift.RecurrenceEndDate = RecurrenceEndDate;
                    // 重复排班不支持通知功能，强制禁用 (Recurring shifts don't support notifications, force disable)
                    newShift.IsReminderEnabled = false;
                    newShift.ReminderMinutes = 0;
                }

                // 使用安全保存方法（包含冲突检测）
                var (success, shiftId, errorMessage) = await _databaseService.SaveShiftWithConflictCheckAsync(newShift);

                if (!success)
                {
                    await ShowErrorAsync($"Cannot create shift: {errorMessage}");
                    return;
                }

                newShift.Id = shiftId;

                // 只有在启用提醒时才调度通知
                if (newShift.IsReminderEnabled)
                {
                    await _notificationScheduler.ScheduleShiftNotificationsAsync(newShift);
                    await _notificationService.ScheduleShiftReminderAsync(newShift);
                }

                // Create additional recurring shifts (not including the base shift)
                if (IsRecurrenceEnabled && SelectedRecurrence?.Type != RecurrenceType.None)
                {
                    await CreateRecurringShiftsAsync(newShift);
                }

                // Send message to notify other modules
                WeakReferenceMessenger.Default.Send(new ShiftCreatedMessage(newShift));

                await ShowSuccessAsync("Shift created successfully!");
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"Failed to create shift: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task CreateRecurringShiftsAsync(Shift baseShift)
        {
            if (!IsRecurrenceEnabled || SelectedRecurrence == null || SelectedRecurrence.Type == RecurrenceType.None)
                return;

            try
            {
                var recurringShifts = new List<Shift>();
                var currentDate = StartDate;
                var occurrenceCount = 1; // 基础班次已经保存，从第二个开始创建

                System.Diagnostics.Debug.WriteLine($"Creating recurring shifts. Base shift already saved with ID: {baseShift.Id}");

                while (occurrenceCount < EstimatedShiftsCount)
                {
                    currentDate = GetNextShiftDate(currentDate);

                    if (currentDate > RecurrenceEndDate)
                        break;

                    if (MaxOccurrences.HasValue && occurrenceCount >= MaxOccurrences.Value)
                        break;

                    var recurringShift = new Shift
                    {
                        EmployerId = baseShift.EmployerId,
                        StartTime = currentDate.Add(StartTime),
                        EndTime = currentDate.Add(EndTime),
                        HourlyRate = baseShift.HourlyRate,
                        Status = ShiftStatus.Scheduled,
                        Notes = baseShift.Notes,
                        Location = baseShift.Location,
                        Description = baseShift.Description,
                        IsRecurring = true,
                        RecurrenceType = SelectedRecurrence.Type,
                        RecurrenceEndDate = RecurrenceEndDate,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        // 重复排班不支持通知功能，强制禁用 (Recurring shifts don't support notifications, force disable)
                        IsReminderEnabled = false,
                        ReminderMinutes = 0
                    };

                    recurringShifts.Add(recurringShift);
                    occurrenceCount++;

                    // 防止无限循环
                    if (occurrenceCount > 1000)
                        break;
                }

                // 批量保存循环班次（包含冲突检测）
                var savedCount = 0;
                var skippedCount = 0;

                foreach (var shift in recurringShifts)
                {
                    // 检查冲突
                    var conflicts = await _databaseService.GetConflictingShiftsAsync(shift);
                    if (conflicts.Any())
                    {
                        System.Diagnostics.Debug.WriteLine($"Skipping recurring shift due to conflict: {shift.StartTime:yyyy-MM-dd HH:mm}");
                        skippedCount++;
                        continue;
                    }

                    await _databaseService.SaveShiftAsync(shift);
                    savedCount++;

                    // 只有在启用提醒时才安排通知
                    if (shift.IsReminderEnabled)
                    {
                        await _notificationScheduler.ScheduleShiftNotificationsAsync(shift);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Recurring shifts created: {savedCount} saved, {skippedCount} skipped due to conflicts");

                System.Diagnostics.Debug.WriteLine($"成功创建 {recurringShifts.Count + 1} 个循环班次");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建循环班次失败: {ex.Message}");
                throw;
            }
        }



        [RelayCommand]
        private async Task CancelAsync()
        {
            try
            {
                // Navigate directly to Home page for better user experience
                await Shell.Current.GoToAsync("//Main/Home");
            }
            catch (Exception ex)
            {
                // Fallback to previous page navigation if direct navigation fails
                System.Diagnostics.Debug.WriteLine($"ShiftManagementViewModel: Direct navigation to Home failed: {ex.Message}");
                try
                {
                    await Shell.Current.GoToAsync("..");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"ShiftManagementViewModel: Fallback navigation also failed: {fallbackEx.Message}");
                    await ShowErrorAsync("Navigation failed. Please try again.");
                }
            }
        }

        [RelayCommand]
        private async Task ViewShiftsAsync()
        {
            await Shell.Current.GoToAsync("ShiftList");
        }

        [RelayCommand]
        private async Task ManageTemplatesAsync()
        {
            await ShowSuccessAsync("Shift templates feature coming soon!");
        }

        #region Localization

        /// <summary>
        /// Localized texts for UI binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new(_localizationService);

        #endregion
    }

    // Helper classes
    public class RecurrenceOption
    {
        public RecurrenceType Type { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class ShiftConflict
    {
        public Shift ConflictingShift { get; set; } = new();
        public string ConflictDescription { get; set; } = string.Empty;
    }


}
