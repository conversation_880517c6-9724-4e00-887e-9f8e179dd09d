// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 雇主信息模型 / Employer Information Model
    /// 存储雇主的基本信息和工作相关配置
    /// Stores basic information and work-related configurations of employers
    /// </summary>
    [Table("Employers")]
    public class Employer
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 雇主名称 / Employer Name
        /// 雇主或公司的名称
        /// Name of the employer or company
        /// </summary>
        [DataAnnotations.Required, SQLite.MaxLength(100)]
        [Indexed]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 联系信息 / Contact Information
        /// 雇主的联系方式（电话、邮箱等）
        /// Contact details of the employer (phone, email, etc.)
        /// </summary>
        [SQLite.MaxLength(200)]
        public string? ContactInfo { get; set; }

        /// <summary>
        /// 时薪 / Hourly Rate
        /// 每小时的工资金额
        /// Wage amount per hour
        /// </summary>
        [DataAnnotations.Required]
        public decimal HourlyRate { get; set; }

        /// <summary>
        /// 支付周期天数 / Payment Cycle in Days
        /// 工资支付的周期（以天为单位）
        /// Payment cycle for wages (in days)
        /// </summary>
        public int PaymentCycleDays { get; set; } = 30;

        /// <summary>
        /// 界面标识颜色 / UI Identifier Color
        /// 用于界面显示的十六进制颜色值
        /// Hexadecimal color value for UI display
        /// </summary>
        [SQLite.MaxLength(7)]
        public string Color { get; set; } = "#007ACC";

        /// <summary>
        /// 工作地点 / Work Location
        /// 工作的具体地址或位置
        /// Specific address or location of work
        /// </summary>
        [SQLite.MaxLength(200)]
        public string? WorkLocation { get; set; }

        /// <summary>
        /// 备注和附加信息 / Notes and Additional Information
        /// 关于雇主的额外说明和备注
        /// Additional notes and remarks about the employer
        /// </summary>
        [SQLite.MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the employer is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
