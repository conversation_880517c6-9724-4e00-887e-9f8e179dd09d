using Microsoft.EntityFrameworkCore;
using DishMeAPP.Models;

namespace DishMeAPP.Data
{
    /// <summary>
    /// 菜谱数据库上下文 - SQLite数据库
    /// Recipe Database Context - SQLite Database
    /// 负责管理Recipe实体的数据库操作，包括连接配置、实体映射和数据库初始化
    /// </summary>
    public class RecipeDbContext : DbContext
    {
        /// <summary>
        /// 菜谱数据集 - 对应数据库中的Recipes表
        /// Recipe dataset - corresponds to Recipes table in database
        /// </summary>
        public DbSet<Recipe> Recipes { get; set; }

        /// <summary>
        /// 配置数据库连接和选项
        /// Configure database connection and options
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            try
            {
                // 数据库文件存储在应用数据目录，确保数据持久化
                // Database file stored in app data directory to ensure data persistence
                var dbPath = Path.Combine(FileSystem.AppDataDirectory, "recipes.db");
                
                // 确保目录存在
                // Ensure directory exists
                var directory = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 配置SQLite连接字符串
                // Configure SQLite connection string
                optionsBuilder.UseSqlite($"Data Source={dbPath}");

                // 开发模式下启用详细日志记录
                // Enable detailed logging in development mode
#if DEBUG
                optionsBuilder.EnableSensitiveDataLogging();
                optionsBuilder.LogTo(message => System.Diagnostics.Debug.WriteLine($"[EF Core] {message}"));
#endif

                System.Diagnostics.Debug.WriteLine($"数据库配置完成，路径: {dbPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库配置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 配置数据模型和实体映射
        /// Configure data model and entity mapping
        /// </summary>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {
                // 配置Recipe实体映射
                // Configure Recipe entity mapping
                modelBuilder.Entity<Recipe>(entity =>
                {
                    // 主键配置 Primary key configuration
                    entity.HasKey(e => e.Id);
                    entity.Property(e => e.Id).ValueGeneratedOnAdd();

                    // 必填字段配置 Required field configuration
                    entity.Property(e => e.Name)
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasComment("菜谱名称");

                    // 字符串字段长度限制 String field length limits
                    entity.Property(e => e.Category)
                        .HasMaxLength(100)
                        .HasComment("菜谱分类");

                    entity.Property(e => e.ImagePath)
                        .HasMaxLength(500)
                        .HasComment("图片路径");

                    entity.Property(e => e.Tags)
                        .HasMaxLength(500)
                        .HasComment("标签");

                    // 长文本字段配置 Long text field configuration
                    entity.Property(e => e.Steps)
                        .HasColumnType("TEXT")
                        .HasComment("制作步骤");

                    entity.Property(e => e.Ingredients)
                        .HasColumnType("TEXT")
                        .HasComment("食材清单");

                    entity.Property(e => e.NutritionInfo)
                        .HasColumnType("TEXT")
                        .HasComment("营养信息");

                    // 数值字段配置 Numeric field configuration
                    entity.Property(e => e.DifficultyLevel)
                        .HasDefaultValue(1)
                        .HasComment("难度等级(1-5)");

                    entity.Property(e => e.Servings)
                        .HasDefaultValue(1)
                        .HasComment("份数");

                    entity.Property(e => e.CookingTimeMinutes)
                        .HasComment("制作时间(分钟)");

                    // 布尔字段配置 Boolean field configuration
                    entity.Property(e => e.IsFavorite)
                        .HasDefaultValue(false)
                        .HasComment("是否收藏");

                    // 时间字段配置 DateTime field configuration
                    entity.Property(e => e.CreatedAt)
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("创建时间");

                    entity.Property(e => e.UpdatedAt)
                        .HasDefaultValueSql("datetime('now')")
                        .HasComment("更新时间");

                    // 索引配置 Index configuration
                    entity.HasIndex(e => e.Name).HasDatabaseName("IX_Recipe_Name");
                    entity.HasIndex(e => e.Category).HasDatabaseName("IX_Recipe_Category");
                    entity.HasIndex(e => e.CreatedAt).HasDatabaseName("IX_Recipe_CreatedAt");
                    entity.HasIndex(e => e.IsFavorite).HasDatabaseName("IX_Recipe_IsFavorite");
                });

                System.Diagnostics.Debug.WriteLine("数据模型配置完成");
                base.OnModelCreating(modelBuilder);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据模型配置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 确保数据库已创建并初始化
        /// Ensure database is created and initialized
        /// </summary>
        public async Task EnsureDatabaseCreatedAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化数据库...");
                
                // 创建数据库（如果不存在）
                // Create database if it doesn't exist
                var created = await Database.EnsureCreatedAsync();
                
                if (created)
                {
                    System.Diagnostics.Debug.WriteLine("数据库创建成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("数据库已存在，跳过创建");
                }

                // 获取数据库信息
                // Get database information
                var dbPath = Database.GetDbConnection().DataSource;
                var recipeCount = await Recipes.CountAsync();
                
                System.Diagnostics.Debug.WriteLine($"数据库初始化完成:");
                System.Diagnostics.Debug.WriteLine($"- 数据库路径: {dbPath}");
                System.Diagnostics.Debug.WriteLine($"- 菜谱数量: {recipeCount}");
                
                // 检查数据库文件大小
                // Check database file size
                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    System.Diagnostics.Debug.WriteLine($"- 文件大小: {fileInfo.Length / 1024.0:F2} KB");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库初始化失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 获取数据库统计信息
        /// Get database statistics
        /// </summary>
        public async Task<(int RecipeCount, long DatabaseSize, string DatabasePath)> GetDatabaseStatsAsync()
        {
            try
            {
                var recipeCount = await Recipes.CountAsync();
                var dbPath = Database.GetDbConnection().DataSource;
                var databaseSize = File.Exists(dbPath) ? new FileInfo(dbPath).Length : 0;

                return (recipeCount, databaseSize, dbPath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取数据库统计信息失败: {ex.Message}");
                return (0, 0, "Unknown");
            }
        }

        /// <summary>
        /// 重写SaveChanges以自动更新UpdatedAt字段
        /// Override SaveChanges to automatically update UpdatedAt field
        /// </summary>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // 自动设置UpdatedAt时间戳
                // Automatically set UpdatedAt timestamp
                var entries = ChangeTracker.Entries<Recipe>()
                    .Where(e => e.State == EntityState.Modified);

                foreach (var entry in entries)
                {
                    entry.Entity.UpdatedAt = DateTime.Now;
                }

                var result = await base.SaveChangesAsync(cancellationToken);
                System.Diagnostics.Debug.WriteLine($"数据库保存成功，影响行数: {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库保存失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// Dispose resources
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                System.Diagnostics.Debug.WriteLine("数据库上下文已释放");
            }
            base.Dispose(disposing);
        }
    }
}
