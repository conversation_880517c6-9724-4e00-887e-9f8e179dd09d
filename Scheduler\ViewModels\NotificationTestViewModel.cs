// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using static Scheduler.ViewModels.SetViewModel;  // 设置视图模型静态成员 / Settings view model static members

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 通知测试页面视图模型 / Notification Test Page ViewModel
    /// 用于测试通知功能和权限管理
    /// Used for testing notification functionality and permission management
    /// </summary>
    public partial class NotificationTestViewModel : ObservableObject
    {
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 状态消息 / Status message
        [ObservableProperty]
        private string _statusMessage = string.Empty;

        // 加载状态 / Loading state
        [ObservableProperty]
        private bool _isLoading;

        // 测试标题 / Test title
        [ObservableProperty]
        private string _testTitle = string.Empty;

        // 测试消息 / Test message
        [ObservableProperty]
        private string _testMessage = string.Empty;

        [ObservableProperty]
        private int _delaySeconds = 5;

        public NotificationTestViewModel(
            INotificationService notificationService,
            DatabaseService databaseService,
            NotificationScheduler notificationScheduler,
            LocalizationService localizationService)
        {
            _notificationService = notificationService;
            _databaseService = databaseService;
            _notificationScheduler = notificationScheduler;
            _localizationService = localizationService;

            // 初始化本地化文本
            var localizedTexts = new LocalizedTexts(_localizationService);
            TestTitle = localizedTexts.TestNotificationTitle;
            TestMessage = localizedTexts.TestNotificationMessage;
        }

        /// <summary>
        /// 本地化文本属性
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);

        /// <summary>
        /// 测试即时通知
        /// </summary>
        [RelayCommand]
        private async Task TestInstantNotificationAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.SendingInstantNotification;

                var hasPermission = await _notificationService.CheckNotificationPermissionAsync();
                if (!hasPermission)
                {
                    var granted = await _notificationService.RequestNotificationPermissionAsync();
                    if (!granted)
                    {
                        StatusMessage = LocalizedTexts.NotificationPermissionDeniedCannotSendTest;
                        return;
                    }
                }

                await _notificationService.SendBreakReminderAsync($"{TestTitle}\n{TestMessage}");
                StatusMessage = LocalizedTexts.InstantNotificationSent;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.SendInstantNotificationFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"发送即时通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试延迟通知
        /// </summary>
        [RelayCommand]
        private async Task TestDelayedNotificationAsync()
        {
            try
            {
                StatusMessage = string.Format(LocalizedTexts.SchedulingDelayedNotificationFormat, DelaySeconds);

                var hasPermission = await _notificationService.CheckNotificationPermissionAsync();
                if (!hasPermission)
                {
                    var granted = await _notificationService.RequestNotificationPermissionAsync();
                    if (!granted)
                    {
                        StatusMessage = LocalizedTexts.NotificationPermissionDeniedCannotSendTest;
                        return;
                    }
                }

                // 创建一个测试班次来触发延迟通知
                var testShift = new Shift
                {
                    Id = 999999, // 使用特殊ID避免冲突
                    EmployerId = 1,
                    StartTime = DateTime.Now.AddSeconds(DelaySeconds),
                    EndTime = DateTime.Now.AddSeconds(DelaySeconds).AddHours(1),
                    Location = LocalizedTexts.TestLocation,
                    HourlyRate = 15.0m
                };

                await _notificationService.ScheduleShiftReminderAsync(testShift);
                StatusMessage = string.Format(LocalizedTexts.DelayedNotificationScheduledFormat, DelaySeconds);
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.ScheduleDelayedNotificationFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"安排延迟通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试班次提醒
        /// </summary>
        [RelayCommand]
        private async Task TestShiftReminderAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.TestingShiftReminder;

                // 获取第一个雇主
                var employers = await _databaseService.GetEmployersAsync();
                if (!employers.Any())
                {
                    StatusMessage = LocalizedTexts.NoEmployerFoundCannotTestShiftReminder;
                    return;
                }

                var employer = employers.First();
                var testShift = new Shift
                {
                    Id = 999998,
                    EmployerId = employer.Id,
                    StartTime = DateTime.Now.AddMinutes(1), // 1分钟后开始
                    EndTime = DateTime.Now.AddMinutes(61), // 1小时后结束
                    Location = LocalizedTexts.TestShiftLocation,
                    HourlyRate = 20.0m
                };

                await _notificationService.ScheduleShiftReminderAsync(testShift);
                StatusMessage = LocalizedTexts.ShiftReminderTestScheduledOneMinute;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.TestShiftReminderFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"测试班次提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试待办事项提醒
        /// </summary>
        [RelayCommand]
        private async Task TestTodoReminderAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.TestingTodoReminder;

                var testTodo = new TodoItem
                {
                    Id = 999997,
                    Title = LocalizedTexts.TestTodoTitle,
                    Description = LocalizedTexts.TestTodoDescription,
                    DueDate = DateTime.Now.AddMinutes(2), // 2分钟后到期
                    Priority = TodoPriority.High,
                    IsReminderEnabled = true,
                    Type = TodoItemType.Reminder
                };

                await _notificationService.ScheduleTodoDeadlineReminderAsync(testTodo);
                StatusMessage = LocalizedTexts.TodoReminderTestScheduledTwoMinutes;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.TestTodoReminderFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"测试待办事项提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试支付提醒
        /// </summary>
        [RelayCommand]
        private async Task TestPaymentReminderAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.TestingPaymentReminder;

                // 获取第一个雇主
                var employers = await _databaseService.GetEmployersAsync();
                if (!employers.Any())
                {
                    StatusMessage = LocalizedTexts.NoEmployerFoundCannotTestPaymentReminder;
                    return;
                }

                var employer = employers.First();
                var testPayment = new PaymentRecord
                {
                    Id = 999996,
                    EmployerId = employer.Id,
                    PeriodStart = DateTime.Today.AddDays(-7),
                    PeriodEnd = DateTime.Today,
                    TotalAmount = 500.0m,
                    Status = PaymentStatus.Pending
                };

                await _notificationService.SendPaymentDueNotificationAsync(testPayment);
                StatusMessage = LocalizedTexts.PaymentReminderTestSent;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.TestPaymentReminderFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"测试支付提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试休息提醒
        /// </summary>
        [RelayCommand]
        private async Task TestBreakReminderAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.TestingBreakReminder;

                await _notificationService.SendBreakReminderAsync(LocalizedTexts.TestBreakReminderMessage);
                StatusMessage = LocalizedTexts.BreakReminderTestSent;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.TestBreakReminderFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"测试休息提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重新安排所有通知
        /// </summary>
        [RelayCommand]
        private async Task RescheduleAllNotificationsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.ReschedulingAllNotifications;

                await _notificationScheduler.RescheduleAllNotificationsAsync();
                StatusMessage = LocalizedTexts.AllNotificationsRescheduled;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.RescheduleNotificationsFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"重新安排通知失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 检查通知权限
        /// </summary>
        [RelayCommand]
        private async Task CheckPermissionAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.CheckingNotificationPermission;

                var hasPermission = await _notificationService.CheckNotificationPermissionAsync();
                StatusMessage = hasPermission ? LocalizedTexts.NotificationPermissionGranted : LocalizedTexts.NotificationPermissionNotGranted;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.CheckNotificationPermissionFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"检查通知权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 请求通知权限
        /// </summary>
        [RelayCommand]
        private async Task RequestPermissionAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.RequestingNotificationPermission;

                var granted = await _notificationService.RequestNotificationPermissionAsync();
                StatusMessage = granted ? LocalizedTexts.NotificationPermissionGranted : LocalizedTexts.NotificationPermissionDenied;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.RequestNotificationPermissionFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"请求通知权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除状态消息
        /// </summary>
        [RelayCommand]
        private void ClearStatusMessage()
        {
            StatusMessage = string.Empty;
        }
    }
}
