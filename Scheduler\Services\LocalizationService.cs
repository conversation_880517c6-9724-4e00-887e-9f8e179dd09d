using System.ComponentModel;
using System.Globalization;
using System.Resources;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Messaging;

namespace Scheduler.Services
{
    /// <summary>
    /// 本地化服务 - 提供应用程序的多语言支持和文化信息管理
    /// Localization service - provides multi-language support and culture information management
    /// </summary>
    /// <remarks>
    /// 该服务采用单例模式，负责管理应用程序的本地化功能，包括：
    /// - 动态语言切换（中文/英文）
    /// - 资源字符串的获取和管理
    /// - 文化信息的设置和同步
    /// - 本地化变更的通知机制
    /// - 与XAML数据绑定的集成支持
    /// </remarks>
    public partial class LocalizationService : ObservableObject
    {
        private static LocalizationService? _instance;
        private readonly ResourceManager _resourceManager;
        private CultureInfo _currentCulture;

        /// <summary>
        /// 获取本地化服务的单例实例
        /// Get singleton instance of localization service
        /// </summary>
        public static LocalizationService Instance => _instance ??= new LocalizationService();

        /// <summary>
        /// 构造函数，初始化资源管理器和当前文化信息
        /// Constructor, initializes resource manager and current culture
        /// </summary>
        /// <remarks>
        /// 初始化时会设置资源管理器指向AppResources.resx文件，
        /// 并使用系统当前的文化信息作为默认设置
        /// 支持依赖注入和单例模式两种使用方式
        /// </remarks>
        public LocalizationService()
        {
            _resourceManager = new ResourceManager("Scheduler.Resources.Strings.AppResources", typeof(LocalizationService).Assembly);
            _currentCulture = new CultureInfo("en"); // 默认设置为英文
        }

        /// <summary>
        /// 当前文化信息 - 控制应用程序的语言和地区设置
        /// Current culture information - controls application language and regional settings
        /// </summary>
        /// <remarks>
        /// 设置此属性时会同时更新系统的CurrentCulture和CurrentUICulture，
        /// 并触发PropertyChanged事件通知所有绑定的UI元素更新显示
        /// </remarks>
        public CultureInfo CurrentCulture
        {
            get => _currentCulture;
            set
            {
                if (SetProperty(ref _currentCulture, value))
                {
                    CultureInfo.CurrentCulture = value;
                    CultureInfo.CurrentUICulture = value;
                    OnPropertyChanged("LocalizedStrings"); // 通知所有本地化字符串更新
                }
            }
        }

        /// <summary>
        /// 获取本地化字符串
        /// Get localized string by resource key
        /// </summary>
        /// <param name="key">资源键名，对应.resx文件中的键</param>
        /// <returns>本地化后的字符串，如果找不到则返回键名本身</returns>
        /// <remarks>
        /// 该方法会根据当前的文化信息从相应的资源文件中获取字符串，
        /// 支持中文（zh-CN）和英文（en-US）两种语言
        /// </remarks>
        public string GetLocalizedString(string key)
        {
            try
            {
                var value = _resourceManager.GetString(key, _currentCulture);
                return value ?? key;
            }
            catch
            {
                return key;
            }
        }

        /// <summary>
        /// 获取格式化的本地化字符串
        /// </summary>
        /// <param name="key">资源键</param>
        /// <param name="args">格式化参数</param>
        /// <returns>格式化的本地化字符串</returns>
        public string GetString(string key, params object[] args)
        {
            try
            {
                var format = _resourceManager.GetString(key, _currentCulture);
                if (format == null) return key;
                return string.Format(format, args);
            }
            catch
            {
                return key;
            }
        }

        /// <summary>
        /// 设置语言
        /// </summary>
        /// <param name="cultureCode">文化代码（如 "en", "zh-CN"）</param>
        public void SetLanguage(string cultureCode)
        {
            try
            {
                var culture = new CultureInfo(cultureCode);
                CurrentCulture = culture;

                // 发送语言更改消息
                WeakReferenceMessenger.Default.Send(new LanguageChangedMessage(cultureCode));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置语言失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        /// <returns>支持的语言列表</returns>
        public List<LanguageOption> GetSupportedLanguages()
        {
            return new List<LanguageOption>
            {
                new LanguageOption { Code = "en", Name = "English", NativeName = "English" },
                new LanguageOption { Code = "zh-CN", Name = "Chinese", NativeName = "中文" }
            };
        }

        /// <summary>
        /// 获取当前语言选项
        /// </summary>
        /// <returns>当前语言选项</returns>
        public LanguageOption GetCurrentLanguage()
        {
            var supportedLanguages = GetSupportedLanguages();
            return supportedLanguages.FirstOrDefault(l => l.Code == _currentCulture.Name) 
                   ?? supportedLanguages.First();
        }
    }

    /// <summary>
    /// 语言选项
    /// </summary>
    public class LanguageOption
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NativeName { get; set; } = string.Empty;
        public string DisplayName => $"{NativeName} ({Name})";
    }



    /// <summary>
    /// 本地化字符串类（支持动态更新）
    /// </summary>
    public class LocalizedString : INotifyPropertyChanged
    {
        private readonly string _key;

        public LocalizedString(string key)
        {
            _key = key;
            LocalizationService.Instance.PropertyChanged += OnLocalizationChanged;
        }

        public string Value => LocalizationService.Instance.GetLocalizedString(_key);

        public event PropertyChangedEventHandler? PropertyChanged;

        private void OnLocalizationChanged(object? sender, PropertyChangedEventArgs e)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Value)));
        }

        public static implicit operator string(LocalizedString localizedString)
        {
            return localizedString.Value;
        }

        public override string ToString()
        {
            return Value;
        }
    }

    /// <summary>
    /// 语言更改消息
    /// </summary>
    public class LanguageChangedMessage
    {
        public string CultureCode { get; }

        public LanguageChangedMessage(string cultureCode)
        {
            CultureCode = cultureCode;
        }
    }
}
