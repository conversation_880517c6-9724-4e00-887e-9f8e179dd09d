<?xml version="1.0" encoding="utf-8" ?>
<!--
    日历视图 / Calendar View
    显示工作班次的日历界面，支持月视图和周视图切换
    Calendar interface for displaying work shifts, supports monthly and weekly view switching
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:system="clr-namespace:System;assembly=netstandard"
             x:Class="Scheduler.Views.CalendarView"
             Title="{Binding Title}">

    <!-- 下拉刷新容器 / Pull-to-refresh container -->
    <!-- 修复：优化RefreshView和ScrollView结构，避免Android平台触摸冲突 -->
    <!-- Fix: Optimize RefreshView and ScrollView structure to avoid touch conflicts on Android -->
    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <VerticalStackLayout Padding="16" Spacing="16">

                <!-- 日历控制面板 / Calendar control panel -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12" Padding="12">
                    <!-- 两行按钮布局 / Two-row button layout -->
                    <Grid RowDefinitions="Auto,Auto"
                          RowSpacing="8">

                        <!-- 第一行按钮区域 / First row button area -->
                        <Grid Grid.Row="0"
                              ColumnDefinitions="Auto,*,Auto,Auto"
                              ColumnSpacing="8">

                            <!-- 上一页按钮 / Previous page button -->
                            <Button Grid.Column="0"
                                    Text="&lt;"
                                    BackgroundColor="#6B7280"
                                    TextColor="White"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    FontSize="16"
                                    Command="{Binding NavigatePreviousCommand}"/>

                            <!-- 视图模式切换按钮 / View mode switch button -->
                            <Button Grid.Column="1"
                                    Text="{Binding ViewMode, StringFormat='{0} View'}"
                                    BackgroundColor="#3B82F6"
                                    TextColor="White"
                                    FontSize="14"
                                    Command="{Binding SwitchViewModeCommand}"/>

                            <!-- 下一页按钮 / Next page button -->
                            <Button Grid.Column="2"
                                    Text="&gt;"
                                    BackgroundColor="#6B7280"
                                    TextColor="White"
                                    WidthRequest="40"
                                    HeightRequest="40"
                    FontSize="16"
                    Command="{Binding NavigateNextCommand}"/>

                            <!-- 今天按钮 -->
                            <Button Grid.Column="3"
                    Text="Today"
                    BackgroundColor="#3B82F6"
                    TextColor="White"
                    FontSize="14"
                    Command="{Binding GoToTodayCommand}"/>
                        </Grid>

                        <!-- 第二行按钮区域 - 显示3个按钮 -->
                        <Grid Grid.Row="1"
              ColumnDefinitions="*,Auto,Auto"
              ColumnSpacing="8">

                            <!-- 添加班次按钮 -->
                            <Button Grid.Column="0"
                    Text="Add Shift"
                    BackgroundColor="#10B981"
                    TextColor="White"
                    FontSize="14"
                    Command="{Binding AddShiftCommand}"/>

                            <!-- 添加雇主按钮 -->
                            <Button Grid.Column="1"
                    Text="Add Employer"
                    BackgroundColor="#8B5CF6"
                    TextColor="White"
                    FontSize="14"
                    Command="{Binding AddEmployerCommand}"/>

                            <!-- 占位按钮，用于保持与第一行对齐 -->
                            <Button Grid.Column="2"
                    Text=""
                    BackgroundColor="Transparent"
                    IsEnabled="False"
                    WidthRequest="40" />
                        </Grid>
                    </Grid>
                </Frame>

                <!-- Date Title -->
                <Label Text="{Binding DisplayTitle}"
                       FontSize="24"
                       FontAttributes="Bold"
                       HorizontalOptions="Center"
                       TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                <!-- Month View -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True"
                       CornerRadius="12"
                       IsVisible="{Binding IsMonthView}">
                    <VerticalStackLayout Spacing="8">

                        <!-- Day Headers -->
                        <Grid ColumnDefinitions="*,*,*,*,*,*,*" ColumnSpacing="4">
                            <Label Grid.Column="0" Text="Sun" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="1" Text="Mon" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="2" Text="Tue" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="3" Text="Wed" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="4" Text="Thu" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="5" Text="Fri" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="6" Text="Sat" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        </Grid>

                        <!-- Calendar Days Grid -->
                        <CollectionView ItemsSource="{Binding CalendarDays}">
                            <CollectionView.ItemsLayout>
                                <GridItemsLayout Orientation="Vertical" Span="7" />
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Border Stroke="{AppThemeBinding Light=#E5E7EB, Dark=#4B5563}"
                                            StrokeThickness="1"
                                            HeightRequest="60">
                                        <VerticalStackLayout Padding="4" Spacing="2">
                                            <!-- Day Number -->
                                            <Label Text="{Binding DayNumber}"
                                                   FontSize="14"
                                                   HorizontalOptions="Start"
                                                   TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                                            <!-- Shift Indicators -->
                                            <FlexLayout Direction="Row" Wrap="Wrap" JustifyContent="Start"
                                                        BindableLayout.ItemsSource="{Binding Shifts}">
                                                <BindableLayout.ItemTemplate>
                                                    <DataTemplate>
                                                        <Ellipse Fill="#3B82F6"
                                                                 WidthRequest="6"
                                                                 HeightRequest="6"
                                                                 Margin="1"/>
                                                    </DataTemplate>
                                                </BindableLayout.ItemTemplate>
                                            </FlexLayout>

                                            <!-- Conflict Indicator -->
                                            <Label Text="⚠️"
                                                   FontSize="12"
                                                   HorizontalOptions="End"
                                                   VerticalOptions="End"
                                                   IsVisible="{Binding HasConflicts}"/>
                                        </VerticalStackLayout>

                                        <Border.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.AddShiftCommand}"/>
                                        </Border.GestureRecognizers>
                                    </Border>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- Week View -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True"
                       CornerRadius="12"
                       IsVisible="{Binding IsWeekView}">
                    <VerticalStackLayout Spacing="12">

                        <!-- Week Day Headers -->
                        <Grid ColumnDefinitions="60,*,*,*,*,*,*,*" ColumnSpacing="4">
                            <Label Grid.Column="0" Text="Time" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="1" Text="Sun" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="2" Text="Mon" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="3" Text="Tue" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="4" Text="Wed" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="5" Text="Thu" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="6" Text="Fri" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Grid.Column="7" Text="Sat" FontAttributes="Bold" HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        </Grid>

                        <!-- Week Shifts List -->
                        <CollectionView ItemsSource="{Binding WeekShifts}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Frame Margin="0,4"
                                           Padding="12"
                                           CornerRadius="8"
                                           BackgroundColor="#3B82F6"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="*,Auto,Auto" ColumnSpacing="12">
                                            <VerticalStackLayout Grid.Column="0" Spacing="4">
                                                <Label FontAttributes="Bold"
                                                       FontSize="16"
                                                       TextColor="White">
                                                    <Label.Text>
                                                        <MultiBinding StringFormat="{}{0:ddd MMM dd} - {1:HH:mm} to {2:HH:mm}">
                                                            <Binding Path="StartTime"/>
                                                            <Binding Path="StartTime"/>
                                                            <Binding Path="EndTime"/>
                                                        </MultiBinding>
                                                    </Label.Text>
                                                </Label>
                                                <Label Text="{Binding Description}"
                                                       FontSize="14"
                                                       TextColor="White"/>
                                                <Label Text="{Binding Location}"
                                                       FontSize="12"
                                                       TextColor="#E5E7EB"/>
                                            </VerticalStackLayout>

                                            <Button Grid.Column="1"
                                                    Text="Edit"
                                                    BackgroundColor="#10B981"
                                                    TextColor="White"
                                                    FontSize="12"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.EditShiftCommand}"
                                                    CommandParameter="{Binding .}"/>

                                            <Button Grid.Column="2"
                                                    Text="Delete"
                                                    BackgroundColor="#EF4444"
                                                    TextColor="White"
                                                    FontSize="12"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.DeleteShiftCommand}"
                                                    CommandParameter="{Binding .}"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <Label Text="No shifts scheduled for this week"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                               IsVisible="{Binding WeekShifts.Count, Converter={StaticResource CountToBoolConverter}}"/>
                    </VerticalStackLayout>
                </Frame>

                <!-- Day View -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True"
                       CornerRadius="12"
                       IsVisible="{Binding IsDayView}">
                    <VerticalStackLayout Spacing="8">

                        <!-- Current Time Indicator -->
                        <Frame BackgroundColor="#3B82F6"
                               Padding="8"
                               CornerRadius="6"
                               HasShadow="False">
                            <Label FontAttributes="Bold"
                                   FontSize="16"
                                   TextColor="White"
                                   HorizontalOptions="Center">
                                <Label.Text>
                                    <MultiBinding StringFormat="Current Time: {0:HH:mm}">
                                        <Binding Source="{x:Static system:DateTime.Now}"/>
                                    </MultiBinding>
                                </Label.Text>
                            </Label>
                        </Frame>

                        <!-- 24-Hour Timeline with Continuous Shift Blocks -->
                        <ScrollView>
                            <CollectionView ItemsSource="{Binding DayTimeline}">
                                <CollectionView.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Padding="4,2" ColumnDefinitions="80,*" ColumnSpacing="8" HeightRequest="60">

                                            <!-- Time Label Column -->
                                            <VerticalStackLayout Grid.Column="0"
                                                                 VerticalOptions="Start"
                                                                 HorizontalOptions="Center">
                                                <Label Text="{Binding TimeLabel}"
                                                       FontSize="14"
                                                       FontAttributes="{Binding IsCurrentHour, Converter={StaticResource BoolToFontAttributesConverter}}"
                                                       TextColor="{Binding IsCurrentHour, Converter={StaticResource BoolToCurrentHourColorConverter}}"
                                                       HorizontalOptions="Center"/>

                                                <!-- Current Hour Indicator -->
                                                <BoxView BackgroundColor="#3B82F6"
                                                         WidthRequest="40"
                                                         HeightRequest="2"
                                                         HorizontalOptions="Center"
                                                         IsVisible="{Binding IsCurrentHour}"/>

                                                <!-- Conflict Indicator -->
                                                <BoxView BackgroundColor="{Binding ConflictColor}"
                                                         WidthRequest="6"
                                                         HeightRequest="6"
                                                         CornerRadius="3"
                                                         HorizontalOptions="Center"
                                                         IsVisible="{Binding HasConflicts}"
                                                         Margin="0,2,0,0"/>
                                            </VerticalStackLayout>

                                            <!-- Hour Content with Continuous Shift Blocks -->
                                            <Grid Grid.Column="1" HeightRequest="56">

                                                <!-- Hour Separator Line -->
                                                <BoxView BackgroundColor="{AppThemeBinding Light=#E5E7EB, Dark=#4B5563}"
                                                         HeightRequest="1"
                                                         HorizontalOptions="Fill"
                                                         VerticalOptions="Start"/>

                                                <!-- Shift Blocks Container -->
                                                <CollectionView ItemsSource="{Binding ShiftBlocks}"
                                                               VerticalOptions="Fill">
                                                    <CollectionView.ItemTemplate>
                                                        <DataTemplate>
                                                            <Grid>
                                                                <!-- Continuous Background Block -->
                                                                <Frame BackgroundColor="{Binding StatusColor}"
                                                                       CornerRadius="4"
                                                                       HasShadow="False"
                                                                       Padding="0"
                                                                       Opacity="0.7"
                                                                       HorizontalOptions="Fill"
                                                                       VerticalOptions="Fill"
                                                                       Margin="2,0"/>

                                                                <!-- Content (only shown in start hour) -->
                                                                <Frame Padding="8"
                                                                       BackgroundColor="Transparent"
                                                                       HasShadow="False"
                                                                       IsVisible="{Binding IsStartHour}"
                                                                       HorizontalOptions="Fill"
                                                                       VerticalOptions="Fill">
                                                                    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="8">
                                                                        <VerticalStackLayout Grid.Column="0" Spacing="1">
                                                                            <Label Text="{Binding TimeText}"
                                                                                   FontAttributes="Bold"
                                                                                   FontSize="12"
                                                                                   TextColor="White"/>
                                                                            <Label Text="{Binding Description}"
                                                                                   FontSize="11"
                                                                                   TextColor="White"
                                                                                   LineBreakMode="TailTruncation"/>
                                                                            <Label Text="{Binding Location}"
                                                                                   FontSize="10"
                                                                                   TextColor="#E5E7EB"
                                                                                   LineBreakMode="TailTruncation"/>
                                                                            <Label Text="{Binding DurationText}"
                                                                                   FontSize="9"
                                                                                   TextColor="#D1D5DB"/>
                                                                        </VerticalStackLayout>

                                                                        <Button Grid.Column="1"
                                                                                Text="{Binding LocalizedTexts.Edit}"
                                                                                BackgroundColor="#3B82F6"
                                                                                TextColor="White"
                                                                                FontSize="9"
                                                                                WidthRequest="45"
                                                                                HeightRequest="25"
                                                                                CornerRadius="4"
                                                                                Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.EditShiftCommand}"
                                                                                CommandParameter="{Binding Shift}"/>
                                                                    </Grid>
                                                                </Frame>

                                                                <!-- Middle/End hour indicator -->
                                                                <Label Text="⋮"
                                                                       FontSize="16"
                                                                       TextColor="White"
                                                                       HorizontalOptions="Center"
                                                                       VerticalOptions="Center"
                                                                       IsVisible="{Binding IsMiddleHour}"
                                                                       Opacity="0.8"/>
                                                            </Grid>
                                                        </DataTemplate>
                                                    </CollectionView.ItemTemplate>
                                                </CollectionView>

                                                <!-- Empty hour space -->
                                                <BoxView HeightRequest="40"
                                                         BackgroundColor="Transparent"
                                                         IsVisible="{Binding HasShifts, Converter={StaticResource InverseBoolConverter}}"/>
                                            </Grid>
                                        </Grid>
                                    </DataTemplate>
                                </CollectionView.ItemTemplate>
                            </CollectionView>
                        </ScrollView>
                    </VerticalStackLayout>
                </Frame>

                <!-- Bottom Action Bar -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12" Padding="12">
                    <Grid ColumnDefinitions="*,*,*,*" ColumnSpacing="12">

                        <!-- Conflicts Button -->
                        <Button Grid.Column="0"
                                BackgroundColor="#F59E0B"
                                TextColor="White"
                                FontSize="14">
                            <Button.Text>
                                <MultiBinding StringFormat="Conflicts: {0}">
                                    <Binding Path="ConflictCount"/>
                                </MultiBinding>
                            </Button.Text>
                            <Button.Command>
                                <Binding Path="ResolveConflictCommand"/>
                            </Button.Command>
                        </Button>

                        <!-- Filter Button -->
                        <Button Grid.Column="1"
                                Text="Filter"
                                BackgroundColor="#6B7280"
                                TextColor="White"
                                FontSize="14"
                                Command="{Binding FilterByEmployerCommand}"/>

                        <!-- Sync Button -->
                        <Button Grid.Column="2"
                                Text="Sync"
                                BackgroundColor="#3B82F6"
                                TextColor="White"
                                FontSize="14"
                                Command="{Binding RefreshCommand}"/>

                        <!-- Export Button -->
                        <Button Grid.Column="3"
                                Text="Export"
                                BackgroundColor="#8B5CF6"
                                TextColor="White"
                                FontSize="14"/>
                    </Grid>
                </Frame>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>