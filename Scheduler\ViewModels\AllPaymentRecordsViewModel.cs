// 引入必要的命名空间 / Import necessary namespaces
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services

namespace Scheduler.ViewModels;

/// <summary>
/// 所有支付记录页面视图模型 / All Payment Records Page ViewModel
/// 管理支付记录的显示、筛选和批量操作
/// Manages payment records display, filtering and batch operations
/// </summary>
public partial class AllPaymentRecordsViewModel : BaseViewModel
{
    #region Services / 服务实例
    // 数据库服务实例 / Database service instance
    private readonly DatabaseService _databaseService;
    // 待办事项服务实例 / Todo service instance
    private readonly TodoService _todoService;
    // 本地化服务实例 / Localization service instance
    private readonly LocalizationService _localizationService;
    #endregion

    #region Properties

    /// <summary>
    /// 支付记录列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<PaymentRecordWrapper> paymentRecords = new();

    /// <summary>
    /// 是否全选
    /// </summary>
    [ObservableProperty]
    private bool isAllSelected;

    /// <summary>
    /// 是否正在刷新
    /// </summary>
    [ObservableProperty]
    private bool isRefreshing;

    /// <summary>
    /// 总记录数
    /// </summary>
    [ObservableProperty]
    private int totalRecordsCount;

    /// <summary>
    /// 待确认记录数
    /// </summary>
    [ObservableProperty]
    private int pendingRecordsCount;

    /// <summary>
    /// 已确认记录数
    /// </summary>
    [ObservableProperty]
    private int confirmedRecordsCount;

    /// <summary>
    /// 已选择记录数
    /// </summary>
    [ObservableProperty]
    private int selectedRecordsCount;

    /// <summary>
    /// 是否有选中的已确认记录
    /// </summary>
    [ObservableProperty]
    private bool hasSelectedConfirmedRecords;

    /// <summary>
    /// 选中的已确认记录文本
    /// </summary>
    [ObservableProperty]
    private string selectedConfirmedRecordsText = string.Empty;

    #endregion

    #region Constructor
    public AllPaymentRecordsViewModel(DatabaseService databaseService, TodoService todoService, LocalizationService localizationService)
    {
        _databaseService = databaseService;
        _todoService = todoService;
        _localizationService = localizationService;

        // 监听全选状态变化
        PropertyChanged += OnPropertyChanged;
    }
    #endregion

    #region Localization
    /// <summary>
    /// 本地化文本
    /// </summary>
    public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);
    #endregion

    #region Commands

    /// <summary>
    /// 页面加载命令
    /// </summary>
    [RelayCommand]
    private async Task LoadDataAsync()
    {
        await SafeExecuteAsync(async () =>
        {
            System.Diagnostics.Debug.WriteLine("AllPaymentRecordsViewModel: 开始加载支付记录");

            // 获取所有支付记录
            var allPayments = await _databaseService.GetPaymentRecordsAsync();

            // 获取所有雇主信息
            var allEmployers = await _databaseService.GetEmployersAsync();
            var employerDict = allEmployers.ToDictionary(e => e.Id, e => e.Name);

            // 创建包装对象
            var wrappers = allPayments.Select(p =>
            {
                var employerName = employerDict.TryGetValue(p.EmployerId, out var name) ? name : "未知雇主";
                return new PaymentRecordWrapper(p, employerName);
            }).ToList();
            
            // 为每个包装对象订阅选择状态变化事件
            foreach (var wrapper in wrappers)
            {
                wrapper.PropertyChanged += OnWrapperPropertyChanged;
            }

            PaymentRecords.Clear();
            foreach (var wrapper in wrappers)
            {
                PaymentRecords.Add(wrapper);
            }

            // 更新统计信息
            UpdateStatistics();

            System.Diagnostics.Debug.WriteLine($"AllPaymentRecordsViewModel: 加载完成，共{PaymentRecords.Count}条记录");
        });
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        IsRefreshing = true;
        await LoadDataAsync();
        IsRefreshing = false;
    }

    /// <summary>
    /// 取消确认命令（批量操作）
    /// </summary>
    [RelayCommand]
    private async Task CancelConfirmationAsync()
    {
        await SafeExecuteAsync(async () =>
        {
            var selectedConfirmedRecords = PaymentRecords
                .Where(w => w.IsSelected && w.IsConfirmed)
                .ToList();

            if (!selectedConfirmedRecords.Any())
            {
                await ShowErrorAsync("请选择要取消确认的记录");
                return;
            }

            var result = await Application.Current.MainPage.DisplayAlert(
                "确认操作",
                $"确定要取消确认选中的 {selectedConfirmedRecords.Count} 条记录吗？",
                "确定",
                "取消");

            if (!result) return;

            await CancelConfirmationForRecordsAsync(selectedConfirmedRecords);
        });
    }

    /// <summary>
    /// 单个记录取消确认命令
    /// </summary>
    [RelayCommand]
    private async Task CancelSingleConfirmationAsync(PaymentRecordWrapper wrapper)
    {
        if (wrapper == null || !wrapper.IsConfirmed)
        {
            await ShowErrorAsync("该记录未确认或不存在");
            return;
        }

        var result = await Application.Current.MainPage.DisplayAlert(
            "确认操作",
            $"确定要取消确认雇主 {wrapper.EmployerName} 的工资记录吗？",
            "确定",
            "取消");

        if (!result) return;

        await CancelConfirmationForRecordsAsync(new List<PaymentRecordWrapper> { wrapper });
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(IsAllSelected))
        {
            // 全选/取消全选
            foreach (var wrapper in PaymentRecords)
            {
                wrapper.IsSelected = IsAllSelected;
            }
        }
    }

    /// <summary>
    /// 包装对象属性变化处理
    /// </summary>
    private void OnWrapperPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(PaymentRecordWrapper.IsSelected))
        {
            UpdateSelectionStatistics();
        }
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        TotalRecordsCount = PaymentRecords.Count;
        PendingRecordsCount = PaymentRecords.Count(w => !w.IsConfirmed);
        ConfirmedRecordsCount = PaymentRecords.Count(w => w.IsConfirmed);
        
        UpdateSelectionStatistics();
    }

    /// <summary>
    /// 更新选择统计信息
    /// </summary>
    private void UpdateSelectionStatistics()
    {
        SelectedRecordsCount = PaymentRecords.Count(w => w.IsSelected);
        
        var selectedConfirmedCount = PaymentRecords.Count(w => w.IsSelected && w.IsConfirmed);
        HasSelectedConfirmedRecords = selectedConfirmedCount > 0;
        SelectedConfirmedRecordsText = $"已选择 {selectedConfirmedCount} 条已确认记录";

        // 更新全选状态
        if (PaymentRecords.Count > 0)
        {
            var allSelected = PaymentRecords.All(w => w.IsSelected);
            var noneSelected = PaymentRecords.All(w => !w.IsSelected);
            
            if (allSelected)
            {
                IsAllSelected = true;
            }
            else if (noneSelected)
            {
                IsAllSelected = false;
            }
        }
    }

    /// <summary>
    /// 执行取消确认操作的共享逻辑
    /// </summary>
    private async Task CancelConfirmationForRecordsAsync(List<PaymentRecordWrapper> recordsToCancel)
    {
        await SafeExecuteAsync(async () =>
        {
            System.Diagnostics.Debug.WriteLine($"AllPaymentRecordsViewModel: 开始取消确认{recordsToCancel.Count}条记录");

            // 批量更新状态
            foreach (var wrapper in recordsToCancel)
            {
                wrapper.PaymentRecord.Status = PaymentStatus.Pending;
                wrapper.PaymentRecord.ActualPaymentDate = null;

                await _databaseService.UpdatePaymentRecordAsync(wrapper.PaymentRecord);

                // 同步更新相关的TodoItem状态
                await SyncRelatedTodoItemAsync(wrapper.PaymentRecord.Id, false);

                // 刷新UI属性
                wrapper.RefreshProperties();
                wrapper.IsSelected = false;
            }

            // 更新统计信息
            UpdateStatistics();

            // 通知其他ViewModel刷新数据 - 发送综合状态变更消息
            WeakReferenceMessenger.Default.Send(new PaymentStatusChangedMessage());

            // 发送详细的支付记录更新消息，确保实时同步
            foreach (var wrapper in recordsToCancel)
            {
                WeakReferenceMessenger.Default.Send(new PaymentRecordUpdatedMessage
                {
                    PaymentRecordId = wrapper.PaymentRecord.Id,
                    Action = "Cancelled",
                    EmployerId = wrapper.PaymentRecord.EmployerId,
                    Amount = wrapper.PaymentRecord.TotalAmount,
                    UpdateTime = DateTime.Now
                });
            }

            await ShowSuccessAsync($"已取消确认 {recordsToCancel.Count} 条记录");

            System.Diagnostics.Debug.WriteLine("AllPaymentRecordsViewModel: 取消确认操作完成，已发送同步消息");
        });
    }

    /// <summary>
    /// 同步相关的TodoItem状态
    /// </summary>
    private async Task SyncRelatedTodoItemAsync(int paymentRecordId, bool isCompleted)
    {
        try
        {
            var todoService = _todoService;
            if (todoService != null)
            {
                // 查找与此支付记录相关的TodoItem
                var allTodos = await todoService.GetAllTodoItemsAsync();
                var relatedTodos = allTodos.Where(t =>
                    t.Type == TodoItemType.Payment &&
                    t.RelatedEntityId == paymentRecordId &&
                    t.RelatedEntityType == "PaymentRecord").ToList();

                foreach (var todo in relatedTodos)
                {
                    if (isCompleted && !todo.IsCompleted)
                    {
                        // 标记为完成
                        todo.MarkAsCompleted();
                        await todoService.SaveTodoItemAsync(todo);
                        System.Diagnostics.Debug.WriteLine($"AllPaymentRecordsViewModel: 标记TodoItem {todo.Id} 为已完成");
                    }
                    else if (!isCompleted && todo.IsCompleted)
                    {
                        // 标记为未完成
                        todo.MarkAsIncomplete();
                        await todoService.SaveTodoItemAsync(todo);
                        System.Diagnostics.Debug.WriteLine($"AllPaymentRecordsViewModel: 标记TodoItem {todo.Id} 为未完成");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"AllPaymentRecordsViewModel: 同步TodoItem状态失败 - {ex.Message}");
        }
    }

    #endregion

    #region Lifecycle

    public async Task InitializeAsync()
    {
        await LoadDataAsync();
    }

    #endregion
}

/// <summary>
/// 支付状态变更消息
/// </summary>
public class PaymentStatusChangedMessage
{
}
