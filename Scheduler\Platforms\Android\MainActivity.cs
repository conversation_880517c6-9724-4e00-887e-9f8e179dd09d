﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Views;
using Android.Views.InputMethods;

namespace Scheduler
{
    [Activity(
        Theme = "@style/Maui.SplashTheme",
        MainLauncher = true,
        ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density,
        WindowSoftInputMode = SoftInput.AdjustResize | SoftInput.StateHidden)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

#if !DESIGN
            // 初始化Android性能优化器，防止ANR
            // Initialize Android performance optimizer to prevent ANR
            Scheduler.Platforms.Android.PerformanceOptimizer.Initialize(this);

            // 确保支持硬件键盘输入，但避免在设计时执行
            ConfigureKeyboardSupport();

            System.Diagnostics.Debug.WriteLine("MainActivity OnCreate 完成，性能优化已启用");
#endif
        }

        /// <summary>
        /// 配置键盘支持 - 优化触摸事件处理
        /// Configure keyboard support - Optimize touch event handling
        /// </summary>
        private void ConfigureKeyboardSupport()
        {
            try
            {
#if !DESIGN
                // 优化软键盘配置，避免与滚动事件冲突
                // Optimize soft keyboard configuration to avoid conflicts with scroll events
                if (Window != null)
                {
                    // 使用AdjustResize避免布局问题，StateHidden避免自动弹出键盘
                    // Use AdjustResize to avoid layout issues, StateHidden to prevent auto keyboard popup
                    Window.SetSoftInputMode(SoftInput.AdjustResize | SoftInput.StateHidden);
                    System.Diagnostics.Debug.WriteLine("MainActivity: 使用优化的键盘配置，避免滚动冲突");
                }
#endif
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MainActivity: 配置键盘支持失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 强制显示软键盘的方法
        /// Force show soft keyboard method
        /// </summary>
        public void ForceShowSoftKeyboard()
        {
            try
            {
#if !DESIGN
                var inputMethodManager = (InputMethodManager?)GetSystemService(InputMethodService);
                if (inputMethodManager != null && CurrentFocus != null)
                {
                    inputMethodManager.ShowSoftInput(CurrentFocus, ShowFlags.Forced);
                    System.Diagnostics.Debug.WriteLine("MainActivity: 强制显示软键盘");
                }
#endif
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MainActivity: 强制显示软键盘失败 - {ex.Message}");
            }
        }

        public override bool OnKeyDown(Keycode keyCode, KeyEvent? e)
        {
            // 记录键盘事件用于调试
            System.Diagnostics.Debug.WriteLine($"MainActivity: 键盘按下 - {keyCode}");
            return base.OnKeyDown(keyCode, e);
        }

        public override bool OnKeyUp(Keycode keyCode, KeyEvent? e)
        {
            // 记录键盘事件用于调试
            System.Diagnostics.Debug.WriteLine($"MainActivity: 键盘释放 - {keyCode}");
            return base.OnKeyUp(keyCode, e);
        }
    }
}
