using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Scheduler.Models;
using Scheduler.Services;
using System.Collections.ObjectModel;

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 主页视图模型 - 显示今日工作概览和快捷操作
    /// Home page ViewModel - displays today's work overview and quick actions
    /// </summary>
    /// <remarks>
    /// 该ViewModel负责管理主页的所有业务逻辑，包括：
    /// - 今日班次和即将到来的班次显示
    /// - 收入统计（日、周、月）
    /// - 工作时间统计
    /// - 快捷操作按钮的命令处理
    /// - 实时数据更新和消息监听
    /// </remarks>
    public partial class HomeViewModel : BaseViewModel, IRecipient<ShiftCreatedMessage>, IRecipient<DataResetMessage>
    {
        private readonly DatabaseService _databaseService;
        private readonly DemoDataService _demoDataService;
        private bool _hasCleanedDuplicates = false; // 标记是否已清理重复数据

        /// <summary>
        /// 初始化主页视图模型
        /// Initialize HomeViewModel with required services
        /// </summary>
        /// <param name="databaseService">数据库服务实例</param>
        /// <param name="demoDataService">演示数据服务实例</param>
        /// <remarks>
        /// 构造函数中会初始化所有必要的集合和属性，
        /// 设置集合变化监听器，注册消息监听器
        /// </remarks>
        public HomeViewModel(DatabaseService databaseService, DemoDataService demoDataService)
        {
            _databaseService = databaseService;
            _demoDataService = demoDataService;
            Title = "Dashboard";
            TodayShifts = new ObservableCollection<Shift>();
            UpcomingShifts = new ObservableCollection<Shift>();

            // 监听集合变化，自动更新相关属性
            TodayShifts.CollectionChanged += (sender, e) =>
            {
                OnPropertyChanged(nameof(HasTodayTasks));
                OnPropertyChanged(nameof(TodayTasksCount));
            };

            UpcomingShifts.CollectionChanged += (sender, e) =>
            {
                OnPropertyChanged(nameof(HasUpcomingShifts));
                OnPropertyChanged(nameof(UpcomingShiftsCount));
            };

            // TEMPORARILY DISABLED: Log Confirmation Functionality
            /*
            // 监听属性变化以更新Mark按钮可见性
            PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(IsLogConfirmationEnabled))
                {
                    OnPropertyChanged(nameof(IsMarkButtonVisible));
                }
            };

            // 注册消息监听，接收设置变化通知
            WeakReferenceMessenger.Default.Register<LogConfirmationSettingChangedMessage>(this, (r, m) =>
            {
                IsLogConfirmationEnabled = m.IsEnabled;
            });
            */

            // 注册班次创建消息监听
            WeakReferenceMessenger.Default.Register<ShiftCreatedMessage>(this);

            // 注册数据重置消息监听
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
        }

        #region Properties - 数据绑定属性

        /// <summary>
        /// 今日班次集合 - 用于显示当天的所有工作班次
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Shift> todayShifts;

        /// <summary>
        /// 即将到来的班次集合 - 用于显示未来几天的工作安排
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Shift> upcomingShifts;

        /// <summary>
        /// 当前活跃班次 - 正在进行中的工作班次
        /// </summary>
        [ObservableProperty]
        private Shift? currentShift;

        /// <summary>
        /// 今日收入 - 格式化的今日总收入字符串
        /// </summary>
        [ObservableProperty]
        private string todayEarnings = "$0.00";

        /// <summary>
        /// 本周收入 - 格式化的本周总收入字符串
        /// </summary>
        [ObservableProperty]
        private string weeklyEarnings = "$0.00";

        /// <summary>
        /// 本月收入 - 格式化的本月总收入字符串
        /// </summary>
        [ObservableProperty]
        private string monthlyEarnings = "$0.00";

        /// <summary>
        /// 今日工作小时数 - 当天已工作的总小时数
        /// </summary>
        [ObservableProperty]
        private int todayHours;

        /// <summary>
        /// 本周工作小时数 - 本周已工作的总小时数
        /// </summary>
        [ObservableProperty]
        private int weeklyHours;

        /// <summary>
        /// 当前时间 - 实时更新的时间显示（HH:mm格式）
        /// </summary>
        [ObservableProperty]
        private string currentTime = DateTime.Now.ToString("HH:mm");

        /// <summary>
        /// 当前日期 - 格式化的日期显示（英文格式）
        /// </summary>
        [ObservableProperty]
        private string currentDate = DateTime.Now.ToString("dddd, MMMM dd, yyyy", System.Globalization.CultureInfo.InvariantCulture);

        /// <summary>
        /// 是否有活跃班次 - 标识当前是否有正在进行的工作
        /// </summary>
        [ObservableProperty]
        private bool hasActiveShift;

        /// <summary>
        /// 今日任务数量
        /// </summary>
        [ObservableProperty]
        private int todayTasksCount;

        /// <summary>
        /// 即将到来的班次数量
        /// </summary>
        [ObservableProperty]
        private int upcomingShiftsCount;

        /// <summary>
        /// 数据加载状态指示器
        /// </summary>
        [ObservableProperty]
        private bool isLoadingData;

        // TEMPORARILY DISABLED: Log Confirmation Properties
        /*
        /// <summary>
        /// Log确认功能是否启用（与SetView联动）
        /// </summary>
        [ObservableProperty]
        private bool isLogConfirmationEnabled = true;

        /// <summary>
        /// Mark按钮是否可见（基于Log确认设置）
        /// </summary>
        public bool IsMarkButtonVisible => IsLogConfirmationEnabled;
        */

        /// <summary>
        /// Mark按钮是否可见（临时禁用Work Log时始终显示）
        /// </summary>
        public bool IsMarkButtonVisible => true;

        /// <summary>
        /// 是否有今日任务
        /// </summary>
        public bool HasTodayTasks
        {
            get
            {
                try
                {
                    return TodayShifts?.Count > 0;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HasTodayTasks 属性访问异常: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 是否有即将到来的班次
        /// </summary>
        public bool HasUpcomingShifts
        {
            get
            {
                try
                {
                    return UpcomingShifts?.Count > 0;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HasUpcomingShifts 属性访问异常: {ex.Message}");
                    return false;
                }
            }
        }

        #endregion

        #region Commands - 命令方法

        /// <summary>
        /// 加载数据命令 - 加载主页所需的所有数据
        /// Load data command - loads all data required for the home page
        /// </summary>
        /// <returns>异步任务</returns>
        /// <remarks>
        /// 该命令执行以下操作：
        /// 1. 清理重复的班次数据（仅首次执行）
        /// 2. 加载今日班次和即将到来的班次
        /// 3. 更新收入统计和计数器
        /// 4. 检查当前活跃班次状态
        /// </remarks>
        [RelayCommand]
        private async Task LoadDataAsync()
        {
            IsLoadingData = true;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            System.Diagnostics.Debug.WriteLine("开始加载HomeView数据...");

            try
            {
                // 添加超时机制，防止ANR
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // 30秒超时

                await SafeExecuteAsync(async () =>
                {
                    // 首次加载时清理重复数据
                    if (!_hasCleanedDuplicates)
                    {
                        try
                        {
                            var duplicatesRemoved = await _databaseService.CleanupDuplicateShiftsAsync().ConfigureAwait(false);
                            if (duplicatesRemoved > 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"Cleaned up {duplicatesRemoved} duplicate shifts on startup");
                            }
                            _hasCleanedDuplicates = true;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"清理重复数据失败: {ex.Message}");
                            // 继续执行，不让清理失败阻止数据加载
                        }
                    }

                    // 加载今日班次（任务）并关联雇主信息
                    System.Diagnostics.Debug.WriteLine("加载今日班次...");
                    await LoadTodayTasksAsync().ConfigureAwait(false);

                    // 检查是否有当前活跃的班次 - 在UI线程上更新UI属性
                    await MainThread.InvokeOnMainThreadAsync(() =>
                    {
                        System.Diagnostics.Debug.WriteLine("检查活跃班次...");
                        CurrentShift = TodayShifts?.FirstOrDefault(s => s.Status == ShiftStatus.InProgress);
                        HasActiveShift = CurrentShift != null;
                        System.Diagnostics.Debug.WriteLine($"当前活跃班次: {(HasActiveShift ? CurrentShift?.Description : "无")}");
                    });

                    // 加载即将到来的班次
                    System.Diagnostics.Debug.WriteLine("加载即将到来的班次...");
                    await LoadUpcomingShiftsAsync().ConfigureAwait(false);

                    // 计算收入统计
                    System.Diagnostics.Debug.WriteLine("计算收入统计...");
                    await CalculateEarningsAsync().ConfigureAwait(false);

                    // 更新计数器（集合变化会自动触发相关属性通知）- 在UI线程上执行
                    await MainThread.InvokeOnMainThreadAsync(() =>
                    {
                        TodayTasksCount = TodayShifts?.Count ?? 0;
                        UpcomingShiftsCount = UpcomingShifts?.Count ?? 0;
                        System.Diagnostics.Debug.WriteLine($"数据加载完成 - 今日班次: {TodayTasksCount}, 即将到来: {UpcomingShiftsCount}");
                    });
                }, showLoading: false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadDataAsync 发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 确保UI状态正确
                TodayTasksCount = 0;
                UpcomingShiftsCount = 0;
                HasActiveShift = false;
                CurrentShift = null;

                // 尝试显示错误信息
                try
                {
                    await ShowErrorAsync($"数据加载失败: {ex.Message}");
                }
                catch
                {
                    System.Diagnostics.Debug.WriteLine("无法显示错误消息");
                }
            }
            finally
            {
                IsLoadingData = false;
                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"LoadDataAsync 执行完成，耗时: {stopwatch.ElapsedMilliseconds}ms");

                // 如果加载时间过长，记录警告
                if (stopwatch.ElapsedMilliseconds > 5000) // 超过5秒
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 数据加载耗时过长 ({stopwatch.ElapsedMilliseconds}ms)，可能影响用户体验");
                }
            }
        }

        [RelayCommand]
        private async Task AddShiftAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // 导航到班次管理页面
                await Shell.Current.GoToAsync("SimpleShift");
            });
        }

        [RelayCommand]
        private async Task ManageEmployersAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                await Shell.Current.GoToAsync("EmployerView");
            });
        }

        [RelayCommand]
        private async Task NavigateToScheduleAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine("HomeViewModel: 导航到 ShiftManagement 页面");
                await Shell.Current.GoToAsync("ShiftManagement");
                System.Diagnostics.Debug.WriteLine("HomeViewModel: 导航到 ShiftManagement 完成");
            });
        }

        [RelayCommand]
        private async Task QuickScheduleAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // TODO: Show quick scheduling interface
                await ShowSuccessAsync("Quick Schedule feature coming soon!");
            });
        }

        [RelayCommand]
        private async Task NavigateToCalendarAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                await Shell.Current.GoToAsync("//Calendar");
            });
        }

        // TEMPORARILY DISABLED: Work Log Navigation
        /*
        [RelayCommand]
        private async Task NavigateToWorkLogAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                await Shell.Current.GoToAsync("//Log");
            });
        }
        */

        [RelayCommand]
        private async Task NavigateToPayrollAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                await Shell.Current.GoToAsync("//Pay");
            });
        }

        [RelayCommand]
        private async Task DebugDatabaseAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine("=== 数据库重置请求 ===");

                // 获取当前数据库摘要
                var summary = await _databaseService.GetCurrentDataSummaryAsync();
                System.Diagnostics.Debug.WriteLine("当前数据库内容:");
                System.Diagnostics.Debug.WriteLine(summary);

                // 检查是否有数据
                var employersCount = await _databaseService.GetEmployersAsync();
                if (employersCount.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("数据库已为空，无需重置");
                    await Application.Current.MainPage.DisplayAlert("Info", "Database is already empty", "OK");
                    return;
                }

                // 第一次确认
                var firstConfirm = await Application.Current.MainPage.DisplayAlert(
                    "Reset All Data",
                    $"This will permanently delete ALL data including:\n\n" +
                    $"• {employersCount.Count} employers\n" +
                    $"• All shifts and work records\n" +
                    $"• All payment records\n\n" +
                    $"Are you sure you want to continue?",
                    "Continue",
                    "Cancel");

                if (!firstConfirm)
                {
                    System.Diagnostics.Debug.WriteLine("用户取消了第一次确认");
                    return;
                }

                // 第二次确认（更严格）
                var secondConfirm = await Application.Current.MainPage.DisplayAlert(
                    "⚠️ FINAL WARNING",
                    "This action CANNOT be undone!\n\n" +
                    "All your work data will be permanently lost.\n\n" +
                    "Are you absolutely certain you want to reset everything?",
                    "YES, DELETE ALL",
                    "NO, KEEP DATA");

                if (!secondConfirm)
                {
                    System.Diagnostics.Debug.WriteLine("用户取消了第二次确认");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("用户确认重置，开始清理数据...");

                // 设置用户手动重置标记，防止自动创建演示数据
                _demoDataService.SetUserManualResetFlag();
                System.Diagnostics.Debug.WriteLine("已设置手动重置标记，防止自动创建演示数据");

                // 执行强制清理
                var clearSuccess = await _databaseService.ForceCompleteDataClearAsync();
                System.Diagnostics.Debug.WriteLine($"数据清理结果: {(clearSuccess ? "成功" : "失败")}");

                if (clearSuccess)
                {
                    // 重新加载数据
                    await LoadDataAsync();

                    // 发送数据重置消息
                    WeakReferenceMessenger.Default.Send(new DataResetMessage());

                    // 显示成功消息
                    await Application.Current.MainPage.DisplayAlert("Success", "All data has been reset successfully!\n\n注意：已禁用自动演示数据创建。", "OK");

                    System.Diagnostics.Debug.WriteLine("数据重置完成，界面已更新");
                }
                else
                {
                    await Application.Current.MainPage.DisplayAlert("Error", "Failed to reset data. Please try again.", "OK");
                }

                System.Diagnostics.Debug.WriteLine("=== 数据库重置完成 ===");
            });
        }

        [RelayCommand]
        private async Task NavigateToAllShiftsAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                await Shell.Current.GoToAsync("ShiftList");
            });
        }

        /// <summary>
        /// 重新启用演示数据创建（清除手动重置标记）
        /// </summary>
        [RelayCommand]
        private async Task EnableDemoDataAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                var hasResetFlag = _demoDataService.HasUserManualResetFlag();

                if (!hasResetFlag)
                {
                    await Application.Current.MainPage.DisplayAlert("信息", "演示数据创建功能当前已启用。", "确定");
                    return;
                }

                var confirm = await Application.Current.MainPage.DisplayAlert(
                    "重新启用演示数据",
                    "这将允许应用在数据库为空时自动创建演示数据。\n\n是否要重新启用演示数据创建功能？",
                    "是的，启用",
                    "取消");

                if (confirm)
                {
                    _demoDataService.ClearUserManualResetFlag();
                    await Application.Current.MainPage.DisplayAlert("成功", "演示数据创建功能已重新启用。\n\n下次数据库为空时将自动创建演示数据。", "确定");
                    System.Diagnostics.Debug.WriteLine("用户重新启用了演示数据创建功能");
                }
            });
        }

        [RelayCommand]
        private async Task StartShiftAsync(Shift shift)
        {
            await SafeExecuteAsync(async () =>
            {
                if (shift.Status == ShiftStatus.Scheduled)
                {
                    shift.Status = ShiftStatus.InProgress;
                    await _databaseService.SaveShiftAsync(shift);
                    await LoadDataAsync();
                    await ShowSuccessAsync($"Started shift: {shift.Description}");
                }
                else
                {
                    await ShowErrorAsync("This shift has already started or completed");
                }
            });
        }

        [RelayCommand]
        private async Task ViewTaskDetailsAsync(Shift shift)
        {
            await SafeExecuteAsync(async () =>
            {
                var details = $"Task Details:\n\n" +
                             $"Time: {shift.StartTime:MM/dd HH:mm} - {shift.EndTime:HH:mm}\n" +
                             $"Employer: {shift.EmployerName ?? "Unknown"}\n" +
                             $"Location: {shift.Location ?? "Not specified"}\n" +
                             $"Description: {shift.Description ?? "No description"}\n" +
                             $"Duration: {shift.DurationHours:F1} hours\n" +
                             $"Hourly Rate: ${shift.HourlyRate:F2}\n" +
                             $"Expected Earnings: ${(decimal)shift.DurationHours * shift.HourlyRate:F2}\n" +
                             $"Status: {GetShiftStatusText(shift.Status)}";

                await ShowSuccessAsync(details, "Task Details");
            });
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsRefreshing = true;
            try
            {
                await LoadDataAsync();
                System.Diagnostics.Debug.WriteLine("首页数据刷新完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新数据失败: {ex.Message}");
                var refreshDataFailedText = _localizationService.GetLocalizedString("RefreshDataFailed");
                await ShowErrorAsync($"{refreshDataFailedText}: {ex.Message}");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 加载今日任务（班次）
        /// </summary>
        private async Task LoadTodayTasksAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载今日班次...");

                // 确保集合已初始化
                if (TodayShifts == null)
                {
                    TodayShifts = new ObservableCollection<Shift>();
                    System.Diagnostics.Debug.WriteLine("TodayShifts 集合已重新初始化");
                }

                // 获取今日班次 - 在后台线程执行
                var todayShifts = await _databaseService.GetTodayShiftsAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {todayShifts?.Count ?? 0} 个今日班次");

                if (todayShifts == null)
                {
                    System.Diagnostics.Debug.WriteLine("警告: GetTodayShiftsAsync 返回 null");
                    todayShifts = new List<Shift>();
                }

                // 获取雇主信息并关联 - 在后台线程执行
                var employers = await _databaseService.GetEmployersAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {employers?.Count ?? 0} 个雇主");

                var employerDict = employers?.ToDictionary(e => e.Id, e => e) ?? new Dictionary<int, Employer>();

                // 准备班次数据（在后台线程处理）
                var processedShifts = new List<Shift>();
                foreach (var shift in todayShifts.OrderBy(s => s.StartTime))
                {
                    try
                    {
                        if (employerDict.TryGetValue(shift.EmployerId, out var employer))
                        {
                            shift.EmployerName = employer.Name;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"警告: 班次 {shift.Id} 的雇主 {shift.EmployerId} 未找到");
                            shift.EmployerName = "未知雇主";
                        }
                        processedShifts.Add(shift);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理班次 {shift.Id} 时发生异常: {ex.Message}");
                        // 继续处理其他班次
                    }
                }

                // 在UI线程上更新集合
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    TodayShifts.Clear();
                    foreach (var shift in processedShifts)
                    {
                        TodayShifts.Add(shift);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"成功加载 {TodayShifts.Count} 个今日班次");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadTodayTasksAsync 异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 确保集合处于安全状态
                if (TodayShifts == null)
                {
                    TodayShifts = new ObservableCollection<Shift>();
                }
                else
                {
                    TodayShifts.Clear();
                }

                await ShowErrorAsync($"加载今日班次失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载即将到来的班次
        /// </summary>
        private async Task LoadUpcomingShiftsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载即将到来的班次...");

                // 确保集合已初始化
                if (UpcomingShifts == null)
                {
                    UpcomingShifts = new ObservableCollection<Shift>();
                    System.Diagnostics.Debug.WriteLine("UpcomingShifts 集合已重新初始化");
                }

                // 使用专门的方法获取即将到来的班次（排除已取消的班次）- 在后台线程执行
                var upcoming = await _databaseService.GetUpcomingShiftsAsync(7).ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {upcoming?.Count ?? 0} 个即将到来的班次");

                if (upcoming == null)
                {
                    System.Diagnostics.Debug.WriteLine("警告: GetUpcomingShiftsAsync 返回 null");
                    upcoming = new List<Shift>();
                }

                // 获取雇主信息并关联 - 在后台线程执行
                var employers = await _databaseService.GetEmployersAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"从数据库获取到 {employers?.Count ?? 0} 个雇主");

                var employerDict = employers?.ToDictionary(e => e.Id, e => e) ?? new Dictionary<int, Employer>();

                // 准备班次数据（在后台线程处理），限制显示数量为8个
                var processedShifts = new List<Shift>();
                foreach (var shift in upcoming.OrderBy(s => s.StartTime).Take(8))
                {
                    try
                    {
                        if (employerDict.TryGetValue(shift.EmployerId, out var employer))
                        {
                            shift.EmployerName = employer.Name;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"警告: 班次 {shift.Id} 的雇主 {shift.EmployerId} 未找到");
                            shift.EmployerName = "未知雇主";
                        }
                        processedShifts.Add(shift);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理即将到来的班次 {shift.Id} 时发生异常: {ex.Message}");
                        // 继续处理其他班次
                    }
                }

                // 在UI线程上更新集合
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    UpcomingShifts.Clear();
                    foreach (var shift in processedShifts)
                    {
                        UpcomingShifts.Add(shift);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"成功加载 {UpcomingShifts.Count} 个即将到来的班次");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadUpcomingShiftsAsync 异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 确保集合处于安全状态
                if (UpcomingShifts == null)
                {
                    UpcomingShifts = new ObservableCollection<Shift>();
                }
                else
                {
                    UpcomingShifts.Clear();
                }

                await ShowErrorAsync($"加载即将到来的班次失败: {ex.Message}");
            }
        }

        private async Task CalculateEarningsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(today.Year, today.Month, 1);

                // Calculate today's expected income (based on today's shifts)
                decimal todayExpectedEarnings = 0;
                foreach (var shift in TodayShifts)
                {
                    todayExpectedEarnings += (decimal)shift.DurationHours * shift.HourlyRate;
                }

                // 获取实际收入数据 - 在后台线程执行
                var todayActualEarnings = await _databaseService.GetConfirmedIncomeByPeriodAsync(today, today.AddDays(1)).ConfigureAwait(false);
                var weeklyEarnings = await _databaseService.GetConfirmedIncomeByPeriodAsync(weekStart, weekStart.AddDays(7)).ConfigureAwait(false);
                var monthlyEarnings = await _databaseService.GetConfirmedIncomeByPeriodAsync(monthStart, monthStart.AddMonths(1)).ConfigureAwait(false);

                // 计算本周工作时长（从数据库获取实际数据）- 在后台线程执行
                var weeklyShifts = await _databaseService.GetShiftsAsync(weekStart, weekStart.AddDays(7)).ConfigureAwait(false);
                var weeklyHours = (int)weeklyShifts.Where(s => s.Status == ShiftStatus.Completed).Sum(s => s.DurationHours);
                var todayHours = (int)TodayShifts.Sum(s => s.DurationHours);

                // 在UI线程上更新UI属性
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    // If no actual income data, use expected income
                    TodayEarnings = todayActualEarnings > 0 ?
                        $"${todayActualEarnings:F2}" :
                        $"${todayExpectedEarnings:F2} (Expected)";

                    WeeklyEarnings = $"${weeklyEarnings:F2}";
                    MonthlyEarnings = $"${monthlyEarnings:F2}";

                    // 计算工作时长
                    TodayHours = todayHours;
                    WeeklyHours = weeklyHours;
                });

                System.Diagnostics.Debug.WriteLine($"收入统计 - 今日: {TodayEarnings}, 本周: {WeeklyEarnings}, 本月: {MonthlyEarnings}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"计算收入统计失败: {ex.Message}");
                // 使用默认值，不显示错误给用户
                TodayEarnings = "$0.00";
                WeeklyEarnings = "$0.00";
                MonthlyEarnings = "$0.00";
            }
        }

        /// <summary>
        /// Get shift status text in English
        /// </summary>
        private string GetShiftStatusText(ShiftStatus status)
        {
            return status switch
            {
                ShiftStatus.Scheduled => "Scheduled",
                ShiftStatus.InProgress => "In Progress",
                ShiftStatus.Completed => "Completed",
                ShiftStatus.Cancelled => "Cancelled",
                _ => "Unknown Status"
            };
        }

        #endregion

        #region 消息接收

        /// <summary>
        /// 接收班次创建消息
        /// </summary>
        public void Receive(ShiftCreatedMessage message)
        {
            // 当有新班次创建时，刷新首页数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"HomeViewModel: 接收到班次创建消息，班次ID: {message.Shift.Id}");
                await LoadDataAsync();
            });
        }

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            // 当数据被重置时，刷新首页数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"HomeViewModel: Received data reset message at {message.ResetTime}");

                // 重置清理标志，允许重新检查重复数据
                _hasCleanedDuplicates = false;

                // 清空当前数据
                TodayShifts.Clear();
                UpcomingShifts.Clear();

                // 重置统计数据
                TodayTasksCount = 0;
                UpcomingShiftsCount = 0;
                TodayEarnings = "$0.00";
                WeeklyEarnings = "$0.00";
                MonthlyEarnings = "$0.00";
                TodayHours = 0;
                WeeklyHours = 0;
                CurrentShift = null;
                HasActiveShift = false;

                // 集合清空会自动触发相关属性通知

                // 重新加载数据（应该为空）
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine($"HomeViewModel: Data refreshed after reset - TodayShifts: {TodayShifts.Count}, UpcomingShifts: {UpcomingShifts.Count}");
            });
        }

        #endregion

        /// <summary>
        /// Called when page appears
        /// </summary>
        public async Task OnAppearingAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("HomeView 页面出现，开始加载数据");

                // 检查是否需要创建演示数据 - 在后台线程执行以避免ANR
                var shouldCreateDemo = await _demoDataService.ShouldCreateDemoDataAsync().ConfigureAwait(false);
                if (shouldCreateDemo)
                {
                    System.Diagnostics.Debug.WriteLine("数据库为空，在后台创建演示数据...");

                    // 在后台线程创建演示数据，避免阻塞UI线程
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("开始在后台线程创建演示数据...");
                            var demoCreated = await _demoDataService.CreateDemoDataAsync().ConfigureAwait(false);
                            if (demoCreated)
                            {
                                System.Diagnostics.Debug.WriteLine("演示数据创建成功");
                                var demoSummary = await _demoDataService.GetDemoDataSummaryAsync().ConfigureAwait(false);
                                System.Diagnostics.Debug.WriteLine(demoSummary);

                                // 在UI线程上刷新数据
                                await MainThread.InvokeOnMainThreadAsync(async () =>
                                {
                                    try
                                    {
                                        await LoadDataAsync();
                                        System.Diagnostics.Debug.WriteLine("演示数据创建完成，UI已刷新");
                                    }
                                    catch (Exception refreshEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"刷新UI数据失败: {refreshEx.Message}");
                                    }
                                });
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("演示数据创建失败");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"后台创建演示数据时发生异常: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                        }
                    });

                    // 不等待演示数据创建完成，继续加载页面
                    System.Diagnostics.Debug.WriteLine("演示数据创建已在后台启动，继续加载页面...");
                }

                // 加载数据 - 使用ConfigureAwait(false)避免UI线程阻塞
                await LoadDataAsync().ConfigureAwait(false);
                // TEMPORARILY DISABLED: Log Confirmation Loading
                // await LoadLogConfirmationSettingAsync();

                // 启动时间更新定时器 - 在UI线程上执行
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    StartTimeUpdateTimer();
                });

                System.Diagnostics.Debug.WriteLine("HomeView 页面数据加载完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HomeView OnAppearingAsync 发生严重异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 尝试显示错误信息给用户
                try
                {
                    await ShowErrorAsync($"页面加载失败: {ex.Message}");
                }
                catch
                {
                    // 如果连错误显示都失败了，至少记录日志
                    System.Diagnostics.Debug.WriteLine("无法显示错误消息给用户");
                }
            }
        }

        /// <summary>
        /// 启动时间更新定时器
        /// </summary>
        private void StartTimeUpdateTimer()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("启动时间更新定时器...");

                if (Application.Current?.Dispatcher == null)
                {
                    System.Diagnostics.Debug.WriteLine("警告: Application.Current.Dispatcher 为 null，无法启动定时器");
                    return;
                }

                Application.Current.Dispatcher.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    try
                    {
                        var now = DateTime.Now;
                        CurrentTime = now.ToString("HH:mm");
                        CurrentDate = now.ToString("dddd, MMMM dd, yyyy", System.Globalization.CultureInfo.InvariantCulture);
                        return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"更新时间失败: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                        return false; // 停止定时器
                    }
                });

                System.Diagnostics.Debug.WriteLine("时间更新定时器启动成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动时间定时器失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        // TEMPORARILY DISABLED: Log Confirmation Setting Loading
        /*
        /// <summary>
        /// 从本地存储加载Log确认设置
        /// </summary>
        private async Task LoadLogConfirmationSettingAsync()
        {
            try
            {
                var setting = await SecureStorage.GetAsync("LogConfirmationEnabled");
                IsLogConfirmationEnabled = bool.Parse(setting ?? "true");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading log confirmation setting: {ex.Message}");
                IsLogConfirmationEnabled = true; // 默认启用
            }
        }
        */
    }
}
