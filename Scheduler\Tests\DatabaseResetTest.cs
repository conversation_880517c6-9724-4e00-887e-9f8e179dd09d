using System;
using System.Threading.Tasks;
using Scheduler.Services;

namespace Scheduler.Tests
{
    /// <summary>
    /// 数据库重置功能测试
    /// </summary>
    public class DatabaseResetTest
    {
        private readonly DatabaseService _databaseService;
        private readonly DemoDataService _demoDataService;

        public DatabaseResetTest(DatabaseService databaseService, DemoDataService demoDataService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _demoDataService = demoDataService ?? throw new ArgumentNullException(nameof(demoDataService));
        }

        /// <summary>
        /// 测试数据库重置后演示数据不会自动创建
        /// </summary>
        public async Task<bool> TestDatabaseResetPreventsAutoDemo()
        {
            try
            {
                Console.WriteLine("=== 数据库重置测试开始 ===");

                // 1. 清除任何现有的重置标记
                _demoDataService.ClearUserManualResetFlag();
                Console.WriteLine("1. 已清除现有重置标记");

                // 2. 检查是否应该创建演示数据（应该返回true，因为数据库为空且没有重置标记）
                var shouldCreateBefore = await _demoDataService.ShouldCreateDemoDataAsync();
                Console.WriteLine($"2. 重置前是否应创建演示数据: {shouldCreateBefore}");

                // 3. 设置用户手动重置标记
                _demoDataService.SetUserManualResetFlag();
                Console.WriteLine("3. 已设置用户手动重置标记");

                // 4. 再次检查是否应该创建演示数据（应该返回false，因为设置了重置标记）
                var shouldCreateAfter = await _demoDataService.ShouldCreateDemoDataAsync();
                Console.WriteLine($"4. 重置后是否应创建演示数据: {shouldCreateAfter}");

                // 5. 验证重置标记状态
                var hasResetFlag = _demoDataService.HasUserManualResetFlag();
                Console.WriteLine($"5. 是否存在重置标记: {hasResetFlag}");

                // 6. 测试结果验证
                bool testPassed = shouldCreateBefore == true && shouldCreateAfter == false && hasResetFlag == true;
                
                Console.WriteLine($"=== 测试结果: {(testPassed ? "通过" : "失败")} ===");
                
                if (testPassed)
                {
                    Console.WriteLine("✅ 数据库重置功能正常工作");
                    Console.WriteLine("   - 重置前：系统会自动创建演示数据");
                    Console.WriteLine("   - 重置后：系统不会自动创建演示数据");
                    Console.WriteLine("   - 重置标记：正确设置和检测");
                }
                else
                {
                    Console.WriteLine("❌ 数据库重置功能存在问题");
                    Console.WriteLine($"   - 重置前应创建演示数据: 期望=true, 实际={shouldCreateBefore}");
                    Console.WriteLine($"   - 重置后不应创建演示数据: 期望=false, 实际={shouldCreateAfter}");
                    Console.WriteLine($"   - 重置标记应存在: 期望=true, 实际={hasResetFlag}");
                }

                return testPassed;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试执行失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试清除重置标记后演示数据可以重新创建
        /// </summary>
        public async Task<bool> TestClearResetFlagAllowsDemo()
        {
            try
            {
                Console.WriteLine("\n=== 清除重置标记测试开始 ===");

                // 1. 设置重置标记
                _demoDataService.SetUserManualResetFlag();
                Console.WriteLine("1. 已设置重置标记");

                // 2. 验证不会创建演示数据
                var shouldCreateWithFlag = await _demoDataService.ShouldCreateDemoDataAsync();
                Console.WriteLine($"2. 有重置标记时是否应创建演示数据: {shouldCreateWithFlag}");

                // 3. 清除重置标记
                _demoDataService.ClearUserManualResetFlag();
                Console.WriteLine("3. 已清除重置标记");

                // 4. 验证可以创建演示数据
                var shouldCreateAfterClear = await _demoDataService.ShouldCreateDemoDataAsync();
                Console.WriteLine($"4. 清除标记后是否应创建演示数据: {shouldCreateAfterClear}");

                // 5. 验证标记已清除
                var hasResetFlag = _demoDataService.HasUserManualResetFlag();
                Console.WriteLine($"5. 是否还存在重置标记: {hasResetFlag}");

                // 6. 测试结果验证
                bool testPassed = shouldCreateWithFlag == false && shouldCreateAfterClear == true && hasResetFlag == false;
                
                Console.WriteLine($"=== 测试结果: {(testPassed ? "通过" : "失败")} ===");
                
                if (testPassed)
                {
                    Console.WriteLine("✅ 清除重置标记功能正常工作");
                    Console.WriteLine("   - 有标记时：不创建演示数据");
                    Console.WriteLine("   - 清除后：可以创建演示数据");
                    Console.WriteLine("   - 标记状态：正确清除");
                }
                else
                {
                    Console.WriteLine("❌ 清除重置标记功能存在问题");
                    Console.WriteLine($"   - 有标记时不应创建: 期望=false, 实际={shouldCreateWithFlag}");
                    Console.WriteLine($"   - 清除后应可创建: 期望=true, 实际={shouldCreateAfterClear}");
                    Console.WriteLine($"   - 标记应已清除: 期望=false, 实际={hasResetFlag}");
                }

                return testPassed;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试执行失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task<bool> RunAllTests()
        {
            Console.WriteLine("🧪 开始数据库重置功能测试...\n");

            var test1 = await TestDatabaseResetPreventsAutoDemo();
            var test2 = await TestClearResetFlagAllowsDemo();

            bool allTestsPassed = test1 && test2;

            Console.WriteLine($"\n🏁 所有测试完成: {(allTestsPassed ? "全部通过" : "存在失败")}");
            
            if (allTestsPassed)
            {
                Console.WriteLine("🎉 数据库重置功能修复成功！");
                Console.WriteLine("   用户执行数据库重置后，首页将不再显示演示数据");
                Console.WriteLine("   用户可以通过设置重新启用演示数据创建");
            }
            else
            {
                Console.WriteLine("⚠️  数据库重置功能仍需进一步调试");
            }

            return allTestsPassed;
        }
    }
}
