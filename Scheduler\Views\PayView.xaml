<?xml version="1.0" encoding="utf-8" ?>
<!--
    工资视图 / Pay View
    显示收入统计、雇主信息和工资计算结果
    Display income statistics, employer information and salary calculation results
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             x:Class="Scheduler.Views.PayView"
             x:DataType="viewmodels:PayViewModel"
             Title="Pay">

    <!-- 下拉刷新容器 / Pull-to-refresh container -->
    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <VerticalStackLayout Spacing="15" Margin="15">

                <!-- 雇主信息模块 / Employer information module -->
                <Border BackgroundColor="#007ACC"
                        StrokeShape="RoundRectangle 12"
                        Padding="20"
                        HeightRequest="220">
                    <Border.Shadow>
                        <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
                    </Border.Shadow>
                    <Grid RowDefinitions="Auto,Auto,*">
                        <!-- 标题和管理按钮 / Title and management button -->
                        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,15">
                            <Label Grid.Column="0"
                                   Text="{Binding EmployerDirectoryText}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   VerticalOptions="Center"/>

                            <Button Grid.Column="1"
                                    Text="{Binding ManageEmployersText}"
                                    Command="{Binding NavigateToAllEmployersCommand}"
                                    BackgroundColor="White"
                                    TextColor="#007ACC"
                                    FontSize="12"
                                    FontAttributes="Bold"
                                    WidthRequest="80"
                                    HeightRequest="32"
                                    CornerRadius="16"
                                    Padding="8,4"/>
                        </Grid>

                        <!-- 雇主列表 / Employer list -->
                        <CollectionView Grid.Row="2"
                                        ItemsSource="{Binding Employers}"
                                        BackgroundColor="Transparent"
                                        EmptyView="{Binding NoEmployersFoundText}">
                            <CollectionView.EmptyViewTemplate>
                                <DataTemplate>
                                    <Label Text="{Binding NoEmployersFoundText}"
                                           TextColor="White"
                                           FontSize="14"
                                           HorizontalOptions="Center"
                                           VerticalOptions="Center"/>
                                </DataTemplate>
                            </CollectionView.EmptyViewTemplate>
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:Employer">
                                    <Frame BackgroundColor="White"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="Auto,*,Auto">
                                            <!-- 颜色标识 -->
                                            <Frame Grid.Column="0"
                                                   BackgroundColor="{Binding Color}"
                                                   WidthRequest="30"
                                                   HeightRequest="30"
                                                   CornerRadius="15"
                                                   HasShadow="False"
                                                   VerticalOptions="Center"
                                                   Margin="0,0,10,0"/>

                                            <!-- 雇主信息 -->
                                            <StackLayout Grid.Column="1" VerticalOptions="Center">
                                                <Label Text="{Binding Name}"
                                                       FontSize="14"
                                                       FontAttributes="Bold"
                                                       TextColor="#007ACC"/>
                                                <Label Text="{Binding HourlyRate, StringFormat='${0:F2}/hour'}"
                                                       FontSize="12"
                                                       TextColor="Gray"/>
                                                <Label Text="{Binding ContactInfo}"
                                                       FontSize="10"
                                                       TextColor="Gray"/>
                                            </StackLayout>

                                            <!-- 支付周期 -->
                                            <Label Grid.Column="2"
                                                   Text="{Binding PaymentCycleDays, StringFormat='{0}d'}"
                                                   FontSize="12"
                                                   TextColor="#007ACC"
                                                   VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </Grid>
                </Border>

                <!-- 模块2: 周/月收入切换 -->
                <Frame BackgroundColor="#007ACC"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20"
                       HeightRequest="180">
                    <Grid RowDefinitions="Auto,*,Auto">
                        <!-- 标题和切换按钮 -->
                        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,10">
                            <Label Grid.Column="0"
                                   Text="{Binding CurrentPeriodText, StringFormat='{0} Income'}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"/>
                            <Button Grid.Column="1"
                                    Text="{Binding ToggleText}"
                                    BackgroundColor="White"
                                    TextColor="#007ACC"
                                    CornerRadius="15"
                                    FontSize="10"
                                    WidthRequest="60"
                                    HeightRequest="30"
                                    Command="{Binding TogglePeriodViewCommand}"/>
                        </Grid>

                        <!-- 收入信息 -->
                        <StackLayout Grid.Row="1" VerticalOptions="Center">
                            <!-- 时间段范围标签 -->
                            <Label Text="{Binding CurrentPeriodRangeText}"
                                   TextColor="#E0E0E0"
                                   FontSize="12"
                                   HorizontalOptions="Center"
                                   Margin="0,0,0,8"/>
                            <Label Text="{Binding CurrentPeriodIncome, StringFormat='${0:F2}'}"
                                   TextColor="White"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   HorizontalOptions="Center"/>
                            <Label Text="{Binding CurrentPeriodHours, StringFormat='{0:F1} hours worked'}"
                                   TextColor="White"
                                   FontSize="14"
                                   HorizontalOptions="Center"/>
                        </StackLayout>
                    </Grid>
                </Frame>

                <!-- 模块3: 5年总收入 -->
                <Frame BackgroundColor="#007ACC"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20"
                       HeightRequest="200">
                    <Grid RowDefinitions="Auto,*">
                        <!-- 标题 -->
                        <Label Grid.Row="0"
                               Text="{Binding FiveYearIncomeHistoryText}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="White"
                               Margin="0,0,0,15"/>

                        <!-- 年度收入列表 -->
                        <CollectionView Grid.Row="1"
                                        ItemsSource="{Binding YearlyIncomes}"
                                        BackgroundColor="Transparent">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="viewmodels:YearlyIncomeItem">
                                    <Grid ColumnDefinitions="*,Auto" Margin="0,2">
                                        <Label Grid.Column="0"
                                               Text="{Binding Year}"
                                               TextColor="White"
                                               FontSize="14"/>
                                        <Label Grid.Column="1"
                                               Text="{Binding Income, StringFormat='${0:F2}'}"
                                               TextColor="White"
                                               FontSize="14"
                                               FontAttributes="Bold"/>
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </Grid>
                </Frame>

                <!-- 模块4: 工资确认Todo List -->
                <Frame BackgroundColor="#4CAF50"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20"
                       HeightRequest="300">
                    <Grid RowDefinitions="Auto,Auto,*">
                        <!-- 标题和View All按钮 -->
                        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,10">
                            <Label Grid.Column="0"
                                   Text="{Binding PaymentTodoListText}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   VerticalOptions="Center"/>
                            <Button Grid.Column="1"
                                    Text="View All"
                                    FontSize="12"
                                    TextColor="#4CAF50"
                                    BackgroundColor="White"
                                    CornerRadius="15"
                                    HeightRequest="30"
                                    WidthRequest="70"
                                    Padding="5,0"
                                    Command="{Binding ViewAllPaymentRecordsCommand}"/>
                        </Grid>

                        <!-- 统计信息 -->
                        <Grid Grid.Row="1" ColumnDefinitions="*,*,*" Margin="0,0,0,15">
                            <StackLayout Grid.Column="0">
                                <Label Text="{Binding PendingText}"
                                       TextColor="White"
                                       FontSize="11"/>
                                <Label Text="{Binding PendingCount}"
                                       TextColor="White"
                                       FontSize="16"
                                       FontAttributes="Bold"/>
                            </StackLayout>
                            <StackLayout Grid.Column="1">
                                <Label Text="{Binding ConfirmedText}"
                                       TextColor="White"
                                       FontSize="11"/>
                                <Label Text="{Binding ConfirmedCount}"
                                       TextColor="White"
                                       FontSize="16"
                                       FontAttributes="Bold"/>
                            </StackLayout>
                            <StackLayout Grid.Column="2">
                                <Label Text="{Binding TotalIncomeText}"
                                       TextColor="White"
                                       FontSize="11"/>
                                <Label Text="{Binding TotalConfirmedIncome, StringFormat='${0:F0}'}"
                                       TextColor="White"
                                       FontSize="14"
                                       FontAttributes="Bold"/>
                            </StackLayout>
                        </Grid>

                        <!-- Todo List 容器 -->
                        <ScrollView Grid.Row="2">
                            <StackLayout Spacing="10">
                                <!-- 待确认支付记录 -->
                                <StackLayout IsVisible="{Binding PaymentTodoItems.Count, Converter={StaticResource IntToBoolConverter}}">
                                    <Label Text="{Binding PendingPaymentsText}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="White"
                                           Margin="0,0,0,5"/>

                                    <CollectionView ItemsSource="{Binding PaymentTodoItems}"
                                                    BackgroundColor="Transparent">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate x:DataType="viewmodels:PaymentTodoItem">
                                                <Frame BackgroundColor="{Binding BackgroundColor}"
                                                       CornerRadius="8"
                                                       Margin="0,2"
                                                       Padding="12"
                                                       HasShadow="False">
                                                    <Grid ColumnDefinitions="Auto,*,Auto">
                                                        <!-- 复选框 -->
                                                        <CheckBox Grid.Column="0"
                                                                  IsChecked="{Binding IsConfirmed}"
                                                                  IsEnabled="{Binding CanConfirm}"
                                                                  Color="#4CAF50"
                                                                  VerticalOptions="Center">
                                                            <CheckBox.GestureRecognizers>
                                                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PayViewModel}}, Path=TogglePaymentConfirmationCommand}"
                                                                                      CommandParameter="{Binding .}"/>
                                                            </CheckBox.GestureRecognizers>
                                                        </CheckBox>

                                                        <!-- 支付信息 -->
                                                        <StackLayout Grid.Column="1" VerticalOptions="Center" Margin="10,0,0,0">
                                                            <Label Text="{Binding DisplayText}"
                                                                   FontSize="14"
                                                                   FontAttributes="Bold"
                                                                   TextColor="{Binding TextColor}"/>
                                                            <Label Text="{Binding PeriodText}"
                                                                   FontSize="12"
                                                                   TextColor="Gray"/>
                                                            <Label Text="{Binding HoursText}"
                                                                   FontSize="11"
                                                                   TextColor="Gray"/>
                                                        </StackLayout>

                                                        <!-- 状态指示器 -->
                                                        <Label Grid.Column="2"
                                                               Text="{Binding StatusText}"
                                                               FontSize="12"
                                                               TextColor="{Binding TextColor}"
                                                               VerticalOptions="Center"/>
                                                    </Grid>
                                                </Frame>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>
                                </StackLayout>

                                <!-- 最近已确认记录 -->
                                <StackLayout IsVisible="{Binding RecentConfirmedItems.Count, Converter={StaticResource IntToBoolConverter}}">
                                    <Label Text="{Binding RecentlyConfirmedText}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="White"
                                           Margin="0,10,0,5"/>

                                    <CollectionView ItemsSource="{Binding RecentConfirmedItems}"
                                                    BackgroundColor="Transparent">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate x:DataType="viewmodels:PaymentTodoItem">
                                                <Frame BackgroundColor="{Binding BackgroundColor}"
                                                       CornerRadius="8"
                                                       Margin="0,2"
                                                       Padding="12"
                                                       HasShadow="False"
                                                       Opacity="0.8">
                                                    <Grid ColumnDefinitions="Auto,*,Auto">
                                                        <!-- 已确认图标 -->
                                                        <Label Grid.Column="0"
                                                               Text="✓"
                                                               FontSize="16"
                                                               TextColor="#4CAF50"
                                                               VerticalOptions="Center"
                                                               HorizontalOptions="Center"
                                                               WidthRequest="30"/>

                                                        <!-- 支付信息（已确认样式） -->
                                                        <StackLayout Grid.Column="1" VerticalOptions="Center" Margin="10,0,0,0">
                                                            <Label Text="{Binding DisplayText}"
                                                                   FontSize="14"
                                                                   FontAttributes="Bold"
                                                                   TextColor="{Binding TextColor}"
                                                                   TextDecorations="{Binding TextDecoration}"/>
                                                            <Label Text="{Binding PeriodText}"
                                                                   FontSize="12"
                                                                   TextColor="Gray"
                                                                   TextDecorations="{Binding TextDecoration}"/>
                                                            <Label Text="{Binding HoursText}"
                                                                   FontSize="11"
                                                                   TextColor="Gray"
                                                                   TextDecorations="{Binding TextDecoration}"/>
                                                        </StackLayout>

                                                        <!-- 状态指示器 -->
                                                        <Label Grid.Column="2"
                                                               Text="{Binding StatusText}"
                                                               FontSize="12"
                                                               TextColor="{Binding TextColor}"
                                                               VerticalOptions="Center"/>
                                                    </Grid>
                                                </Frame>
                                            </DataTemplate>
                                        </CollectionView.ItemTemplate>
                                    </CollectionView>
                                </StackLayout>

                                <!-- 空状态提示 -->
                                <StackLayout IsVisible="{Binding HasNoPaymentRecords}"
                                             VerticalOptions="Center"
                                             HorizontalOptions="Center"
                                             Margin="20">
                                    <Label Text="{Binding NoPaymentRecordsText}"
                                           TextColor="White"
                                           FontSize="14"
                                           HorizontalOptions="Center"/>
                                </StackLayout>
                            </StackLayout>
                        </ScrollView>
                    </Grid>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsVisible="{Binding IsBusy}"
                                   IsRunning="{Binding IsBusy}"
                                   Color="#007ACC"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   Margin="20"/>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>