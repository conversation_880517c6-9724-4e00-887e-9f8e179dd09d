<?xml version="1.0" encoding="utf-8" ?>
<!--
    主页视图 / Home View
    应用程序的主要仪表板页面，显示工作概览和快捷操作
    Main dashboard page of the application, showing work overview and quick actions
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Scheduler.Views.HomeView"
             Title="Home">

    <!-- 共享样式和模板定义 / Shared styles and template definitions -->
    <ContentPage.Resources>
        <ResourceDictionary>
            <!-- 卡片边框样式 / Card border style -->
            <Style x:Key="CardBorderStyle" TargetType="Border">
                <Setter Property="BackgroundColor" Value="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"/>
                <Setter Property="Shadow">
                    <Setter.Value>
                        <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="StrokeShape" Value="RoundRectangle 12"/>
                <Setter Property="Padding" Value="16"/>
            </Style>

            <!-- 时间显示卡片样式 / Time display card style -->
            <Style x:Key="TimeCardBorderStyle" TargetType="Border">
                <Setter Property="BackgroundColor" Value="{AppThemeBinding Light=#F8F9FA, Dark=#2D3748}"/>
                <Setter Property="Shadow">
                    <Setter.Value>
                        <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="StrokeShape" Value="RoundRectangle 12"/>
                <Setter Property="Padding" Value="16"/>
            </Style>

            <!-- 标题样式 / Section title style -->
            <Style x:Key="SectionTitleStyle" TargetType="Label">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontAttributes" Value="Bold"/>
                <Setter Property="TextColor" Value="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
            </Style>

            <!-- 快捷按钮样式 / Quick action button style -->
            <Style x:Key="QuickActionButtonStyle" TargetType="Button">
                <Setter Property="TextColor" Value="White"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="12,8"/>
            </Style>

            <!-- 班次项目模板 / Shift item template -->
            <DataTemplate x:Key="ShiftItemTemplate">
                <!-- 修复：使用Grid替代Button，避免Content属性冲突 -->
                <!-- Fix: Use Grid instead of Button to avoid Content property conflicts -->
                <Grid Padding="0,8" ColumnDefinitions="Auto,*" ColumnSpacing="12"
                      AutomationProperties.IsInAccessibleTree="True">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.ViewTaskDetailsCommand}"
                                              CommandParameter="{Binding .}"/>
                    </Grid.GestureRecognizers>

                    <!-- 左侧状态指示器：根据班次状态显示不同颜色 -->
                    <BoxView Grid.Column="0" WidthRequest="4" HeightRequest="50"
                             BackgroundColor="{Binding StatusColor}" VerticalOptions="Center"/>

                    <!-- 右侧内容区域：班次详细信息 -->
                    <Grid Grid.Column="1" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="4">
                        <!-- 第一行：时间范围显示（加粗，主要信息） -->
                        <Label Grid.Row="0" Text="{Binding TimeRangeDisplay}"
                               FontAttributes="Bold" FontSize="16"
                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                        <!-- 第二行：班次描述（次要信息） -->
                        <Label Grid.Row="1" Text="{Binding Description}" FontSize="14"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        <!-- 第三行：雇主名称（绿色，条件显示） -->
                        <Label Grid.Row="2" Text="{Binding EmployerName}" FontSize="12"
                               TextColor="{AppThemeBinding Light=#059669, Dark=#34D399}"
                               IsVisible="{Binding HasEmployer}"/>
                        <!-- 第四行：工作详情（紫色，包含地点和收入信息） -->
                        <Label Grid.Row="3" Text="{Binding WorkDetailsDisplay}" FontSize="12"
                               TextColor="{AppThemeBinding Light=#8B5CF6, Dark=#A78BFA}"/>
                    </Grid>
                </Grid>
            </DataTemplate>

            <!-- 即将到来班次的模板 - 用于显示未来班次列表项 -->
            <!-- Upcoming shift item template - for displaying future shift list items -->
            <DataTemplate x:Key="UpcomingShiftItemTemplate">
                <!-- 修复：使用Grid替代Button，避免Content属性冲突 -->
                <!-- Fix: Use Grid instead of Button to avoid Content property conflicts -->
                <Grid Padding="0,8" ColumnDefinitions="Auto,*" ColumnSpacing="12"
                      AutomationProperties.IsInAccessibleTree="True">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.ViewTaskDetailsCommand}"
                                              CommandParameter="{Binding .}"/>
                    </Grid.GestureRecognizers>

                    <BoxView Grid.Column="0" WidthRequest="4" HeightRequest="50"
                             BackgroundColor="#F59E0B" VerticalOptions="Center"/>

                    <Grid Grid.Column="1" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="4">
                        <!-- 优化：使用预计算的日期时间显示 -->
                        <Label Grid.Row="0" Text="{Binding DateTimeRangeDisplay}"
                               FontAttributes="Bold" FontSize="16"
                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                        <Label Grid.Row="1" Text="{Binding Description}" FontSize="14"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        <Label Grid.Row="2" Text="{Binding EmployerName}" FontSize="12"
                               TextColor="{AppThemeBinding Light=#059669, Dark=#34D399}"
                               IsVisible="{Binding HasEmployer}"/>
                        <Label Grid.Row="3" Text="{Binding Location}" FontSize="12"
                               TextColor="{AppThemeBinding Light=#8B5CF6, Dark=#A78BFA}"
                               IsVisible="{Binding HasLocation}"/>
                    </Grid>
                </Grid>
            </DataTemplate>
        </ResourceDictionary>
    </ContentPage.Resources>

    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <!-- 优化：使用 Grid 减少嵌套，提升性能 -->
            <Grid Padding="16" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="16">

                <!-- 时间显示与今日任务合并区域 -->
                <Border Grid.Row="0" Style="{StaticResource TimeCardBorderStyle}">
                    <!-- 优化：使用 Grid 替代嵌套的 VerticalStackLayout -->
                    <Grid RowDefinitions="Auto,Auto,Auto" RowSpacing="16">
                        <!-- 时间显示部分 -->
                        <Grid Grid.Row="0" RowDefinitions="Auto,Auto" RowSpacing="8">
                            <Label Grid.Row="0" Text="{Binding CurrentTime}"
                                   FontSize="28" FontAttributes="Bold"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#2D3748, Dark=#F8F9FA}"
                                   AutomationProperties.Name="当前时间"
                                   AutomationProperties.HelpText="显示当前时间"/>
                            <Label Grid.Row="1" Text="{Binding CurrentDate}"
                                   FontSize="25"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                   AutomationProperties.Name="当前日期"
                                   AutomationProperties.HelpText="显示当前日期"/>
                        </Grid>

                        <!-- 分隔线 -->
                        <BoxView Grid.Row="1" HeightRequest="1"
                                 BackgroundColor="{AppThemeBinding Light=#E5E7EB, Dark=#4B5563}"
                                 HorizontalOptions="FillAndExpand"/>

                        <!-- 今日任务部分 -->
                        <Grid Grid.Row="2" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="12">
                            <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto" ColumnSpacing="8">
                                <Label Grid.Column="0" Text="Today's Shift"
                                       Style="{StaticResource SectionTitleStyle}"
                                       AutomationProperties.Name="今日班次标题"
                                       AutomationProperties.HelpText="显示今天的工作班次"/>
                                <Label Grid.Column="1" Text="{Binding TodayTasksCount, StringFormat='({0})'}"
                                       FontSize="14" FontAttributes="Bold"
                                       TextColor="{AppThemeBinding Light=#3B82F6, Dark=#60A5FA}"
                                       VerticalOptions="Center"
                                       AutomationProperties.Name="今日班次数量"/>
                                <Button Grid.Column="2"
                                        Text="View All"
                                        BackgroundColor="#F59E0B"
                                        TextColor="White"
                                        FontSize="12"
                                        Padding="8,4"
                                        CornerRadius="6"
                                        Command="{Binding NavigateToAllShiftsCommand}"
                                        VerticalOptions="Center"
                                        AutomationProperties.Name="查看所有班次"
                                        AutomationProperties.HelpText="点击查看所有班次列表"/>
                            </Grid>

                            <!-- 加载状态指示器 -->
                            <ActivityIndicator Grid.Row="1"
                                               IsVisible="{Binding IsLoadingData}"
                                               IsRunning="{Binding IsLoadingData}"
                                               Color="{AppThemeBinding Light=#3B82F6, Dark=#60A5FA}"
                                               HeightRequest="30"
                                               AutomationProperties.Name="数据加载中"
                                               AutomationProperties.HelpText="正在加载班次数据"/>

                            <!-- 优化：使用共享的 DataTemplate -->
                            <CollectionView Grid.Row="2"
                                            ItemsSource="{Binding TodayShifts}"
                                            IsVisible="{Binding HasTodayTasks}"
                                            AutomationProperties.Name="今日班次列表"
                                            AutomationProperties.HelpText="显示今天安排的所有工作班次"
                                            RemainingItemsThreshold="5"
                                            ItemTemplate="{StaticResource ShiftItemTemplate}"/>

                            <Label Grid.Row="3" Text="No tasks scheduled for today"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                   IsVisible="{Binding HasTodayTasks, Converter={StaticResource InverseBoolConverter}}"
                                   Margin="0,20"
                                   AutomationProperties.Name="无班次提示"
                                   AutomationProperties.HelpText="今天没有安排工作班次"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Quick Actions Area -->
                <Border Grid.Row="1" Style="{StaticResource CardBorderStyle}">
                    <!-- 优化：使用 Grid 替代 VerticalStackLayout -->
                    <Grid RowDefinitions="Auto,Auto" RowSpacing="16">
                        <Label Grid.Row="0" Text="Quick Actions"
                               Style="{StaticResource SectionTitleStyle}"
                               AutomationProperties.Name="快捷操作标题"
                               AutomationProperties.HelpText="快捷操作按钮区域"/>

                        <Grid Grid.Row="1" RowDefinitions="Auto,Auto" ColumnDefinitions="*,*,*" RowSpacing="12" ColumnSpacing="12">
                            <!-- Row 1 -->
                            <Button Grid.Row="0" Grid.Column="0"
                                    Text="Employer"
                                    BackgroundColor="#10B981"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding ManageEmployersCommand}"
                                    AutomationProperties.Name="雇主管理"
                                    AutomationProperties.HelpText="点击管理雇主信息"
                                    SemanticProperties.Hint="导航到雇主管理页面"/>

                            <Button Grid.Row="0" Grid.Column="1"
                                    Text="Schedule"
                                    BackgroundColor="#3B82F6"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding NavigateToScheduleCommand}"
                                    AutomationProperties.Name="排班管理"
                                    AutomationProperties.HelpText="点击创建新的工作班次"
                                    SemanticProperties.Hint="导航到排班页面"/>

                            <Button Grid.Row="0" Grid.Column="2"
                                    Text="Notification"
                                    BackgroundColor="#10B981"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding QuickScheduleCommand}"
                                    AutomationProperties.Name="通知设置"
                                    AutomationProperties.HelpText="点击设置工作提醒"
                                    SemanticProperties.Hint="快速设置通知"/>

                            <!-- Row 2 -->
                            <Button Grid.Row="1" Grid.Column="0"
                                    Text="Calendar"
                                    BackgroundColor="#8B5CF6"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding NavigateToCalendarCommand}"
                                    AutomationProperties.Name="日历视图"
                                    AutomationProperties.HelpText="点击查看日历"
                                    SemanticProperties.Hint="导航到日历页面"/>

                            <!-- 数据重置按钮 -->
                            <Button Grid.Row="1" Grid.Column="1"
                                    Text="Reset Data"
                                    BackgroundColor="#DC2626"
                                    TextColor="White"
                                    FontSize="11"
                                    FontAttributes="Bold"
                                    Padding="8,4"
                                    CornerRadius="8"
                                    Command="{Binding DebugDatabaseCommand}"
                                    AutomationProperties.Name="重置所有数据"
                                    AutomationProperties.HelpText="清空所有数据并重置应用"/>

                            <Button Grid.Row="1" Grid.Column="2"
                                    Text="Payroll"
                                    BackgroundColor="#8B5CF6"
                                    Style="{StaticResource QuickActionButtonStyle}"
                                    Command="{Binding NavigateToPayrollCommand}"
                                    AutomationProperties.Name="薪资管理"
                                    AutomationProperties.HelpText="点击查看薪资信息"
                                    SemanticProperties.Hint="导航到薪资页面"/>

                        </Grid>
                    </Grid>
                </Border>

                <!-- Earnings Statistics Area -->
                <Border Grid.Row="2" Style="{StaticResource CardBorderStyle}">
                    <!-- 优化：使用 Grid 替代 VerticalStackLayout -->
                    <Grid RowDefinitions="Auto,Auto" RowSpacing="12">
                        <Label Grid.Row="0" Text="Earnings Overview"
                               Style="{StaticResource SectionTitleStyle}"
                               AutomationProperties.Name="收入概览标题"
                               AutomationProperties.HelpText="显示收入统计信息"/>

                        <Grid Grid.Row="1" RowDefinitions="Auto,Auto,Auto" ColumnDefinitions="*,Auto" RowSpacing="8">
                            <Label Grid.Row="0" Grid.Column="0" Text="Today's Earnings"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                   AutomationProperties.Name="今日收入标签"/>
                            <Label Grid.Row="0" Grid.Column="1" Text="{Binding TodayEarnings}"
                                   FontAttributes="Bold" HorizontalOptions="End"
                                   TextColor="{AppThemeBinding Light=#059669, Dark=#34D399}"
                                   AutomationProperties.Name="今日收入金额"/>

                            <Label Grid.Row="1" Grid.Column="0" Text="Weekly Earnings"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                   AutomationProperties.Name="本周收入标签"/>
                            <Label Grid.Row="1" Grid.Column="1" Text="{Binding WeeklyEarnings}"
                                   FontAttributes="Bold" HorizontalOptions="End"
                                   TextColor="{AppThemeBinding Light=#059669, Dark=#34D399}"
                                   AutomationProperties.Name="本周收入金额"/>

                            <Label Grid.Row="2" Grid.Column="0" Text="Monthly Earnings"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                   AutomationProperties.Name="本月收入标签"/>
                            <Label Grid.Row="2" Grid.Column="1" Text="{Binding MonthlyEarnings}"
                                   FontAttributes="Bold" HorizontalOptions="End"
                                   TextColor="{AppThemeBinding Light=#059669, Dark=#34D399}"
                                   AutomationProperties.Name="本月收入金额"/>
                        </Grid>
                    </Grid>
                </Border>

                <!-- Upcoming Shifts -->
                <Border Grid.Row="3" Style="{StaticResource CardBorderStyle}">
                    <!-- 优化：使用 Grid 替代 VerticalStackLayout -->
                    <Grid RowDefinitions="Auto,Auto,Auto" RowSpacing="12">
                        <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto" ColumnSpacing="8">
                            <Label Grid.Column="0" Text="Upcoming Shift"
                                   Style="{StaticResource SectionTitleStyle}"
                                   AutomationProperties.Name="即将到来的班次标题"
                                   AutomationProperties.HelpText="显示即将到来的工作班次"/>
                            <Label Grid.Column="1" Text="{Binding UpcomingShiftsCount, StringFormat='({0})'}"
                                   FontSize="14" FontAttributes="Bold"
                                   TextColor="{AppThemeBinding Light=#F59E0B, Dark=#FBBF24}"
                                   VerticalOptions="Center"
                                   AutomationProperties.Name="即将到来的班次数量"/>
                            <Button Grid.Column="2"
                                    Text="View All"
                                    BackgroundColor="#F59E0B"
                                    TextColor="White"
                                    FontSize="12"
                                    Padding="8,4"
                                    CornerRadius="6"
                                    Command="{Binding NavigateToAllShiftsCommand}"
                                    VerticalOptions="Center"
                                    AutomationProperties.Name="查看所有即将到来的班次"
                                    AutomationProperties.HelpText="点击查看所有即将到来的班次"/>
                        </Grid>

                        <!-- 优化：使用共享的 DataTemplate -->
                        <CollectionView Grid.Row="1"
                                        ItemsSource="{Binding UpcomingShifts}"
                                        IsVisible="{Binding HasUpcomingShifts}"
                                        AutomationProperties.Name="即将到来的班次列表"
                                        AutomationProperties.HelpText="显示即将到来的工作班次"
                                        RemainingItemsThreshold="5"
                                        ItemTemplate="{StaticResource UpcomingShiftItemTemplate}"/>

                        <Label Grid.Row="2" Text="No upcoming shift schedules"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                               IsVisible="{Binding HasUpcomingShifts, Converter={StaticResource InverseBoolConverter}}"
                               Margin="0,20"
                               AutomationProperties.Name="无即将到来班次提示"
                               AutomationProperties.HelpText="没有即将到来的工作班次"/>
                    </Grid>
                </Border>

            </Grid>
        </ScrollView>
    </RefreshView>

</ContentPage>