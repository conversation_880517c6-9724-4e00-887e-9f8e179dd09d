// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Models;                // 数据模型 / Data models
using System.Text.RegularExpressions; // 正则表达式 / Regular expressions

namespace Scheduler.Services
{
    /// <summary>
    /// 数据验证服务 / Data Validation Service
    /// 负责验证用户输入数据的有效性和完整性
    /// Responsible for validating the validity and completeness of user input data
    /// </summary>
    public class DataValidationService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化数据验证服务 / Initialize data validation service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public DataValidationService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 验证雇主数据 / Validate employer data
        /// 检查雇主信息的完整性和有效性
        /// Check completeness and validity of employer information
        /// </summary>
        /// <param name="employer">雇主对象 / Employer object</param>
        /// <returns>验证结果 / Validation result</returns>
        public async Task<ValidationResult> ValidateEmployerAsync(Employer employer)
        {
            var result = new ValidationResult();

            // 必填字段验证 / Required field validation
            if (string.IsNullOrWhiteSpace(employer.Name))
            {
                result.AddError("雇主名称不能为空 / Employer name cannot be empty");
            }
            else if (employer.Name.Length > 100)
            {
                result.AddError("雇主名称不能超过100个字符 / Employer name cannot exceed 100 characters");
            }

            // 时薪验证 / Hourly rate validation
            if (employer.HourlyRate <= 0)
            {
                result.AddError("时薪必须大于0 / Hourly rate must be greater than 0");
            }
            else if (employer.HourlyRate > 1000)
            {
                result.AddWarning("时薪超过1000，请确认是否正确 / Hourly rate exceeds 1000, please confirm if correct");
            }

            // 加班倍率验证已移除 / Overtime multiplier validation removed

            // 支付周期验证 / Payment cycle validation
            if (employer.PaymentCycleDays <= 0)
            {
                result.AddError("支付周期必须大于0天 / Payment cycle must be greater than 0 days");
            }
            else if (employer.PaymentCycleDays > 365)
            {
                result.AddWarning("支付周期超过365天，请确认是否正确");
            }

            // 颜色格式验证
            if (!string.IsNullOrEmpty(employer.Color) && !IsValidHexColor(employer.Color))
            {
                result.AddError("颜色格式无效，请使用十六进制格式（如#FF0000）");
            }

            // 联系信息验证
            if (!string.IsNullOrEmpty(employer.ContactInfo) && employer.ContactInfo.Length > 200)
            {
                result.AddError("联系信息不能超过200个字符");
            }

            // 检查名称重复
            if (!string.IsNullOrWhiteSpace(employer.Name))
            {
                var existingEmployer = await _databaseService.GetEmployersAsync();
                var duplicate = existingEmployer.FirstOrDefault(e => 
                    e.Id != employer.Id && 
                    e.Name.Equals(employer.Name, StringComparison.OrdinalIgnoreCase));
                
                if (duplicate != null)
                {
                    result.AddError($"雇主名称 '{employer.Name}' 已存在");
                }
            }

            return result;
        }

        /// <summary>
        /// 验证班次数据
        /// </summary>
        public async Task<ValidationResult> ValidateShiftAsync(Shift shift)
        {
            var result = new ValidationResult();

            // 时间验证
            if (shift.StartTime >= shift.EndTime)
            {
                result.AddError("班次结束时间必须晚于开始时间");
            }

            // 班次时长验证
            var duration = shift.EndTime - shift.StartTime;
            if (duration.TotalHours > 24)
            {
                result.AddError("班次时长不能超过24小时");
            }
            else if (duration.TotalMinutes < 30)
            {
                result.AddWarning("班次时长少于30分钟，请确认是否正确");
            }

            // 过去时间验证
            if (shift.StartTime < DateTime.Now.AddHours(-1))
            {
                result.AddWarning("班次开始时间已过期");
            }

            // 雇主验证
            var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
            if (employer == null)
            {
                result.AddError($"雇主ID {shift.EmployerId} 不存在");
            }
            else if (!employer.IsActive)
            {
                result.AddError($"雇主 '{employer.Name}' 已被禁用");
            }

            // 地点验证
            if (!string.IsNullOrEmpty(shift.Location) && shift.Location.Length > 200)
            {
                result.AddError("工作地点不能超过200个字符");
            }

            // 描述验证
            if (!string.IsNullOrEmpty(shift.Description) && shift.Description.Length > 500)
            {
                result.AddError("班次描述不能超过500个字符");
            }

            // 预估工时验证
            if (shift.EstimatedHours > 0)
            {
                if (shift.EstimatedHours <= 0)
                {
                    result.AddError("预估工时必须大于0");
                }
                else if (shift.EstimatedHours > 24)
                {
                    result.AddError("预估工时不能超过24小时");
                }
                else if (Math.Abs(shift.EstimatedHours - duration.TotalHours) > 2)
                {
                    result.AddWarning("预估工时与班次时长差异较大，请确认");
                }
            }

            return result;
        }

        /// <summary>
        /// 验证工作记录数据
        /// </summary>
        public async Task<ValidationResult> ValidateWorkRecordAsync(WorkRecord record)
        {
            var result = new ValidationResult();

            // 班次验证
            var shift = await _databaseService.GetShiftByIdAsync(record.ShiftId);
            if (shift == null)
            {
                result.AddError($"班次ID {record.ShiftId} 不存在");
                return result;
            }

            // 打卡时间验证
            if (record.ClockInTime.HasValue && record.ClockOutTime.HasValue)
            {
                if (record.ClockInTime.Value >= record.ClockOutTime.Value)
                {
                    result.AddError("下班时间必须晚于上班时间");
                }

                var workDuration = record.ClockOutTime.Value - record.ClockInTime.Value;
                if (workDuration.TotalHours > 24)
                {
                    result.AddError("工作时长不能超过24小时");
                }

                // 检查打卡时间是否在班次时间范围内（允许一定偏差）
                var allowedVariance = TimeSpan.FromHours(2);
                if (record.ClockInTime.Value < shift.StartTime.Subtract(allowedVariance) ||
                    record.ClockInTime.Value > shift.StartTime.Add(allowedVariance))
                {
                    result.AddWarning("上班打卡时间与班次开始时间差异较大");
                }

                if (record.ClockOutTime.Value < shift.EndTime.Subtract(allowedVariance) ||
                    record.ClockOutTime.Value > shift.EndTime.Add(allowedVariance))
                {
                    result.AddWarning("下班打卡时间与班次结束时间差异较大");
                }
            }

            // 实际工时验证
            if (record.ActualHours.HasValue)
            {
                if (record.ActualHours.Value < 0)
                {
                    result.AddError("实际工时不能为负数");
                }
                else if (record.ActualHours.Value > 24)
                {
                    result.AddError("实际工时不能超过24小时");
                }

                // 与计算工时比较
                var calculatedHours = record.CalculatedHours;
                if (calculatedHours.HasValue && Math.Abs(record.ActualHours.Value - calculatedHours.Value) > 2)
                {
                    result.AddWarning("手动输入的工时与计算工时差异较大");
                }
            }

            // 迟到和早退验证
            if (record.LateMinutes < 0)
            {
                result.AddError("迟到时间不能为负数");
            }
            else if (record.LateMinutes > 480) // 8小时
            {
                result.AddWarning("迟到时间超过8小时，请确认");
            }

            if (record.EarlyLeaveMinutes < 0)
            {
                result.AddError("早退时间不能为负数");
            }

            // 休息时间验证
            if (record.BreakMinutes < 0)
            {
                result.AddError("休息时间不能为负数");
            }
            else if (record.BreakMinutes > 480) // 8小时
            {
                result.AddWarning("休息时间超过8小时，请确认");
            }

            // 加班时间验证
            if (record.OvertimeMinutes < 0)
            {
                result.AddError("加班时间不能为负数");
            }

            // 质量评分验证
            if (record.QualityRating.HasValue && (record.QualityRating.Value < 1 || record.QualityRating.Value > 5))
            {
                result.AddError("质量评分必须在1-5之间");
            }

            // 文本字段长度验证
            if (!string.IsNullOrEmpty(record.Notes) && record.Notes.Length > 1000)
            {
                result.AddError("工作笔记不能超过1000个字符");
            }

            if (!string.IsNullOrEmpty(record.TasksCompleted) && record.TasksCompleted.Length > 1000)
            {
                result.AddError("完成任务描述不能超过1000个字符");
            }

            if (!string.IsNullOrEmpty(record.ActualLocation) && record.ActualLocation.Length > 200)
            {
                result.AddError("实际工作地点不能超过200个字符");
            }

            return result;
        }

        /// <summary>
        /// 验证支付记录数据
        /// </summary>
        public async Task<ValidationResult> ValidatePaymentRecordAsync(PaymentRecord payment)
        {
            var result = new ValidationResult();

            // 雇主验证
            var employer = await _databaseService.GetEmployerAsync(payment.EmployerId);
            if (employer == null)
            {
                result.AddError($"雇主ID {payment.EmployerId} 不存在");
            }

            // 时间段验证
            if (payment.PeriodStart >= payment.PeriodEnd)
            {
                result.AddError("支付期间结束日期必须晚于开始日期");
            }

            // 工时验证
            if (payment.TotalHours < 0)
            {
                result.AddError("总工时不能为负数");
            }

            if (payment.RegularHours < 0)
            {
                result.AddError("常规工时不能为负数");
            }

            if (payment.OvertimeHours < 0)
            {
                result.AddError("加班工时不能为负数");
            }

            if (Math.Abs(payment.TotalHours - (payment.RegularHours + payment.OvertimeHours)) > 0.1)
            {
                result.AddError("总工时应等于常规工时加加班工时");
            }

            // 金额验证
            if (payment.BaseAmount < 0)
            {
                result.AddError("基础工资不能为负数");
            }

            if (payment.OvertimeAmount < 0)
            {
                result.AddError("加班费不能为负数");
            }

            if (payment.TotalAmount < 0)
            {
                result.AddError("总金额不能为负数");
            }

            var calculatedTotal = payment.BaseAmount + payment.OvertimeAmount + payment.BonusAmount - payment.DeductionAmount;
            if (Math.Abs(payment.TotalAmount - calculatedTotal) > 0.01m)
            {
                result.AddError("总金额计算错误");
            }

            // 支付日期验证
            if (payment.ExpectedPaymentDate.HasValue && payment.ExpectedPaymentDate.Value < payment.PeriodEnd)
            {
                result.AddWarning("预期支付日期早于支付期间结束日期");
            }

            if (payment.ActualPaymentDate.HasValue && payment.ActualPaymentDate.Value < payment.PeriodStart)
            {
                result.AddWarning("实际支付日期早于支付期间开始日期");
            }

            return result;
        }

        /// <summary>
        /// 验证十六进制颜色格式
        /// </summary>
        private bool IsValidHexColor(string color)
        {
            return Regex.IsMatch(color, @"^#[0-9A-Fa-f]{6}$");
        }
    }


}
