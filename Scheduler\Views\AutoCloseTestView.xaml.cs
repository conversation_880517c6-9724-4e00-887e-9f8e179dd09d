// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 自动关闭测试视图 / Auto Close Test View
/// 用于测试Toast和Snackbar自动关闭功能的调试页面
/// Debug page for testing Toast and Snackbar auto-close functionality
/// </summary>
public partial class AutoCloseTestView : ContentPage
{
    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化自动关闭测试视图并配置数据绑定 / Initialize auto close test view and configure data binding
    /// </summary>
    /// <param name="viewModel">自动关闭测试视图模型 / Auto close test view model</param>
    public AutoCloseTestView(AutoCloseTestViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        BindingContext = viewModel;
    }
}
