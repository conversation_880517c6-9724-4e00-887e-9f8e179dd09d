// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 设置视图 / Settings View
/// 应用程序设置和配置管理页面
/// Application settings and configuration management page
/// </summary>
public partial class SetView : ContentPage
{
    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化设置视图并配置数据绑定 / Initialize settings view and configure data binding
    /// </summary>
    /// <param name="viewModel">设置视图模型 / Settings view model</param>
    public SetView(SetViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        BindingContext = viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 设置页面通常不需要重新加载数据 / Settings page usually doesn't need to reload data
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is SetViewModel viewModel)
        {
            // 设置页面通常不需要重新加载数据，因为设置是本地存储的
            // Settings page usually doesn't need to reload data as settings are stored locally
            // 但可以在这里添加任何需要的初始化逻辑
            // But can add any needed initialization logic here
        }
    }

    /// <summary>
    /// Pay确认开关状态变化事件处理 / Pay confirmation toggle event handler
    /// </summary>
    private void OnPayConfirmationToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsPayConfirmationEnabled = e.Value;
        }
    }

    /// <summary>
    /// 工作提醒开关状态变化事件处理 / Work reminder toggle event handler
    /// </summary>
    private void OnWorkReminderToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsWorkReminderEnabled = e.Value;
        }
    }

    /// <summary>
    /// 休息提醒开关状态变化事件处理 / Break reminder toggle event handler
    /// </summary>
    private void OnBreakReminderToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsBreakReminderEnabled = e.Value;
        }
    }

    /// <summary>
    /// 工资确认提醒开关状态变化事件处理 / Payment reminder toggle event handler
    /// </summary>
    private void OnPaymentReminderToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsPaymentReminderEnabled = e.Value;
        }
    }

    /// <summary>
    /// 每日工作安排通知开关状态变化事件处理 / Daily schedule toggle event handler
    /// </summary>
    private void OnDailyScheduleToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsDailyScheduleEnabled = e.Value;
        }
    }

    /// <summary>
    /// 声音开关状态变化事件处理 / Sound toggle event handler
    /// </summary>
    private void OnSoundToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsSoundEnabled = e.Value;
        }
    }

    /// <summary>
    /// 振动开关状态变化事件处理 / Vibration toggle event handler
    /// </summary>
    private void OnVibrationToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsVibrationEnabled = e.Value;
        }
    }

    /// <summary>
    /// 免打扰时间开关状态变化事件处理 / Quiet hours toggle event handler
    /// </summary>
    private void OnQuietHoursToggled(object sender, ToggledEventArgs e)
    {
        if (BindingContext is SetViewModel viewModel)
        {
            viewModel.IsQuietHoursEnabled = e.Value;
        }
    }

    /// <summary>
    /// 数据库测试按钮点击事件处理 / Database test button click event handler
    /// </summary>
    private async void OnDatabaseTestClicked(object sender, EventArgs e)
    {
        try
        {
            await Shell.Current.GoToAsync("DatabaseTest");
        }
        catch (Exception ex)
        {
            await DisplayAlert("错误 / Error", $"无法打开数据库测试页面 / Cannot open database test page: {ex.Message}", "确定 / OK");
        }
    }
}