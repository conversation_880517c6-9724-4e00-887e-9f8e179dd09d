namespace Scheduler.Services
{
    /// <summary>
    /// 默认键盘服务实现 / Default Keyboard Service Implementation
    /// 用于非Android平台的默认实现，提供空操作
    /// Default implementation for non-Android platforms, provides no-op operations
    /// </summary>
    public class DefaultKeyboardService : IKeyboardService
    {
        /// <summary>
        /// 强制显示软键盘（默认实现 - 无操作） / Force show soft keyboard (default implementation - no operation)
        /// 在不支持的平台上记录调试信息
        /// Logs debug information on unsupported platforms
        /// </summary>
        public void ForceShowKeyboard()
        {
            // 非Android平台的默认实现 / Default implementation for non-Android platforms
            System.Diagnostics.Debug.WriteLine("DefaultKeyboardService: ForceShowKeyboard - 当前平台不支持 / Current platform not supported");
        }

        /// <summary>
        /// 强制隐藏软键盘（默认实现 - 无操作） / Force hide soft keyboard (default implementation - no operation)
        /// 在不支持的平台上记录调试信息
        /// Logs debug information on unsupported platforms
        /// </summary>
        public void ForceHideKeyboard()
        {
            // 非Android平台的默认实现 / Default implementation for non-Android platforms
            System.Diagnostics.Debug.WriteLine("DefaultKeyboardService: ForceHideKeyboard - 当前平台不支持 / Current platform not supported");
        }

        /// <summary>
        /// 检查软键盘是否可见（默认实现 - 返回false） / Check if soft keyboard is visible (default implementation - returns false)
        /// 在不支持的平台上始终返回false
        /// Always returns false on unsupported platforms
        /// </summary>
        /// <returns>始终返回false / Always returns false</returns>
        public bool IsKeyboardVisible()
        {
            // 非Android平台的默认实现 / Default implementation for non-Android platforms
            return false;
        }
    }
}
