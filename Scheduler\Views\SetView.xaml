<?xml version="1.0" encoding="utf-8" ?>
<!--
    设置视图 / Settings View
    应用程序设置和配置管理页面
    Application settings and configuration management page
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:Class="Scheduler.Views.SetView"
             x:DataType="viewmodels:SetViewModel"
             Title="{Binding LocalizedTexts.Settings}">

    <!-- 下拉刷新容器 / Pull-to-refresh container -->
    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <VerticalStackLayout Spacing="20" Margin="15">

                <!-- 个人资料区域 / Personal profile area -->
                <Frame BackgroundColor="#007ACC"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20"
                       HeightRequest="120">
                    <Grid ColumnDefinitions="Auto,*,Auto">
                        <!-- 用户头像 / User avatar -->
                        <Frame Grid.Column="0"
                               WidthRequest="60"
                               HeightRequest="60"
                               CornerRadius="30"
                               Padding="0"
                               HasShadow="False"
                               BackgroundColor="White"
                               VerticalOptions="Center">
                            <Image Source="{Binding UserAvatarUrl}"
                                   Aspect="AspectFill"/>
                        </Frame>

                        <!-- 用户信息 -->
                        <StackLayout Grid.Column="1"
                                     VerticalOptions="Center"
                                     Margin="15,0,0,0">
                            <Label Text="{Binding UserName}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"/>
                            <Label Text="{Binding UserEmail}"
                                   FontSize="14"
                                   TextColor="White"
                                   Opacity="0.8"/>
                            <Label Text="{Binding MemberSince}"
                                   FontSize="12"
                                   TextColor="White"
                                   Opacity="0.7"/>
                        </StackLayout>

                        <!-- 编辑按钮 -->
                        <Button Grid.Column="2"
                                Text="{Binding LocalizedTexts.Edit}"
                                BackgroundColor="White"
                                TextColor="#007ACC"
                                CornerRadius="8"
                                FontSize="14"
                                WidthRequest="60"
                                HeightRequest="35"
                                VerticalOptions="Center"
                                Command="{Binding EditProfileCommand}"/>
                    </Grid>
                </Frame>

                <!-- 工作设置区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.WorkSettings}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- Log确认开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               IsVisible="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.LogConfirmation}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.LogConfirmationDesc}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <!-- TEMPORARILY DISABLED: Log Confirmation Switch -->
                                <!--
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsLogConfirmationEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnLogConfirmationToggled"/>
                                -->
                            </Grid>
                        </Frame>

                        <!-- Pay确认开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.PayConfirmation}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.PayConfirmationDesc}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsPayConfirmationEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnPayConfirmationToggled"/>
                            </Grid>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 通知提醒设置区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.Notifications}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 工作提醒开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.WorkReminders}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.WorkRemindersDesc}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsWorkReminderEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnWorkReminderToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 休息提醒开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.BreakReminders}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.BreakRemindersDesc}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsBreakReminderEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnBreakReminderToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 工资确认提醒开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.PaymentNotifications}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.PaymentNotificationsDesc}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsPaymentReminderEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnPaymentReminderToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 班次提醒时间设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               IsVisible="{Binding IsWorkReminderEnabled}">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.ShiftReminderTime}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.ShiftReminderDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding ShiftReminderMinutes, StringFormat='{0} minutes'}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectShiftReminderTimeCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 每日工作安排通知 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.DailySchedule}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.DailyScheduleDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsDailyScheduleEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnDailyScheduleToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 每日工作安排时间设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               IsVisible="{Binding IsDailyScheduleEnabled}">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.DailyNotificationTime}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.DailyNotificationDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding DailyScheduleTime, StringFormat='{0:HH:mm}'}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectDailyScheduleTimeCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 休息提醒间隔设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               IsVisible="{Binding IsBreakReminderEnabled}">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.BreakReminderInterval}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.BreakReminderDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding BreakReminderInterval, StringFormat='{0} hours'}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectBreakReminderIntervalCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 通知行为设置区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.NotificationBehavior}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 声音开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.NotificationSound}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.NotificationSoundDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsSoundEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnSoundToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 振动开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.VibrationReminder}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.VibrationDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsVibrationEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnVibrationToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 免打扰时间开关 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto">
                                <StackLayout Grid.Column="0" Spacing="4">
                                    <Label Text="{Binding LocalizedTexts.QuietHours}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.QuietHoursDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsQuietHoursEnabled}"
                                        OnColor="#4CAF50"
                                        VerticalOptions="Center"
                                        Toggled="OnQuietHoursToggled"/>
                            </Grid>
                        </Frame>

                        <!-- 免打扰时间设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               IsVisible="{Binding IsQuietHoursEnabled}">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.QuietHoursPeriod}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.QuietHoursPeriodDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding QuietHoursText}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectQuietHoursCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 权限管理区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.PermissionManagement}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 通知权限状态 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.NotificationPermission}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.NotificationPermissionDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding NotificationPermissionStatus}"
                                       FontSize="14"
                                       TextColor="{Binding NotificationPermissionColor}"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Button Grid.Column="2"
                                        Text="{Binding LocalizedTexts.Check}"
                                        FontSize="12"
                                        BackgroundColor="#007ACC"
                                        TextColor="White"
                                        CornerRadius="6"
                                        Padding="10,5"
                                        Command="{Binding CheckNotificationPermissionCommand}"/>
                            </Grid>
                        </Frame>

                        <!-- 位置权限状态 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.LocationPermission}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="{Binding LocalizedTexts.LocationPermissionDescription}"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding LocationPermissionStatus}"
                                       FontSize="14"
                                       TextColor="{Binding LocationPermissionColor}"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Button Grid.Column="2"
                                        Text="{Binding LocalizedTexts.Check}"
                                        FontSize="12"
                                        BackgroundColor="#007ACC"
                                        TextColor="White"
                                        CornerRadius="6"
                                        Padding="10,5"
                                        Command="{Binding CheckLocationPermissionCommand}"/>
                            </Grid>
                        </Frame>

                        <!-- 权限管理按钮 -->
                        <Button Text="{Binding LocalizedTexts.OpenAppSettings}"
                                FontSize="14"
                                BackgroundColor="#10B981"
                                TextColor="White"
                                CornerRadius="8"
                                Padding="15"
                                Command="{Binding OpenAppSettingsCommand}"/>

                        <!-- 测试通知按钮 -->
                        <Button Text="{Binding LocalizedTexts.TestNotification}"
                                FontSize="14"
                                BackgroundColor="#8B5CF6"
                                TextColor="White"
                                CornerRadius="8"
                                Padding="15"
                                Command="{Binding TestNotificationCommand}"/>
                    </StackLayout>
                </Frame>

                <!-- 显示与时间设置区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.DisplayTime}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 时区设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.TimeZone}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding CurrentTimeZone}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectTimeZoneCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 时间格式设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.TimeFormat}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding TimeFormatText}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ToggleTimeFormatCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 语言设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <StackLayout Grid.Column="0" VerticalOptions="Center">
                                    <Label Text="{Binding LocalizedTexts.Language}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                </StackLayout>
                                <Label Grid.Column="1"
                                       Text="{Binding CurrentLanguage}"
                                       FontSize="14"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"
                                       Margin="0,0,10,0"/>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding SelectLanguageCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 系统集成区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.SystemIntegration}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 闹钟设置 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Label Grid.Column="0"
                                       Text="🔔"
                                       FontSize="20"
                                       VerticalOptions="Center"
                                       Margin="0,0,15,0"/>
                                <StackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label Text="Alarm Settings"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="Link to phone alarm app"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding OpenAlarmSettingsCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 日历导出 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Label Grid.Column="0"
                                       Text="📅"
                                       FontSize="20"
                                       VerticalOptions="Center"
                                       Margin="0,0,15,0"/>
                                <StackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label Text="Export to Calendar"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="Sync with system calendar"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ExportToCalendarCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 数据管理区域 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <StackLayout Spacing="15">
                        <!-- 分组标题 -->
                        <Label Text="{Binding LocalizedTexts.DataManagement}"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#007ACC"
                               Margin="0,0,0,10"/>

                        <!-- 数据导出 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Label Grid.Column="0"
                                       Text="📤"
                                       FontSize="20"
                                       VerticalOptions="Center"
                                       Margin="0,0,15,0"/>
                                <StackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label Text="Export Data"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="Export work data to file"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ExportDataCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 备份同步 -->
                        <Frame BackgroundColor="#F8F9FA"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Label Grid.Column="0"
                                       Text="🔄"
                                       FontSize="20"
                                       VerticalOptions="Center"
                                       Margin="0,0,15,0"/>
                                <StackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label Text="Backup &amp; Sync"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#333333"/>
                                    <Label Text="Cloud backup settings"
                                           FontSize="12"
                                           TextColor="#666666"/>
                                </StackLayout>
                                <Label Grid.Column="2"
                                       Text=">"
                                       FontSize="16"
                                       TextColor="#007ACC"
                                       VerticalOptions="Center"/>
                            </Grid>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding BackupSyncCommand}"/>
                            </Frame.GestureRecognizers>
                        </Frame>

                        <!-- 数据库诊断 -->
                        <Frame BackgroundColor="#F0F9FF"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               Margin="0,0,0,10">
                            <StackLayout Spacing="10">
                                <Grid ColumnDefinitions="Auto,*">
                                    <Label Grid.Column="0"
                                           Text="🔍"
                                           FontSize="20"
                                           VerticalOptions="Center"
                                           Margin="0,0,15,0"/>
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Label Text="Database Diagnostics"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               TextColor="#1E40AF"/>
                                        <Label Text="Check database status and connection"
                                               FontSize="12"
                                               TextColor="#1E3A8A"/>
                                    </StackLayout>
                                </Grid>

                                <Button Text="🔍 Check Database Status"
                                        FontSize="14"
                                        FontAttributes="Bold"
                                        BackgroundColor="#2563EB"
                                        TextColor="White"
                                        CornerRadius="8"
                                        Padding="15"
                                        Command="{Binding TestDatabaseStateCommand}"/>
                            </StackLayout>
                        </Frame>

                        <!-- 核武器级数据库重置 -->
                        <Frame BackgroundColor="#FEF2F2"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False"
                               Margin="0,0,0,10">
                            <StackLayout Spacing="10">
                                <Grid ColumnDefinitions="Auto,*">
                                    <Label Grid.Column="0"
                                           Text="🚨"
                                           FontSize="20"
                                           VerticalOptions="Center"
                                           Margin="0,0,15,0"/>
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Label Text="Nuclear Database Reset"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               TextColor="#B91C1C"/>
                                        <Label Text="Completely destroy and recreate database"
                                               FontSize="12"
                                               TextColor="#7F1D1D"/>
                                    </StackLayout>
                                </Grid>

                                <Button Text="🚀 NUCLEAR RESET 🚀"
                                        FontSize="14"
                                        FontAttributes="Bold"
                                        BackgroundColor="#B91C1C"
                                        TextColor="White"
                                        CornerRadius="8"
                                        Padding="15"
                                        Command="{Binding NuclearDatabaseResetCommand}"/>
                            </StackLayout>
                        </Frame>

                        <!-- 数据库连接测试 -->
                        <Frame BackgroundColor="#F0F9FF"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <StackLayout Spacing="10">
                                <Grid ColumnDefinitions="Auto,*">
                                    <Label Grid.Column="0"
                                           Text="🔍"
                                           FontSize="20"
                                           VerticalOptions="Center"
                                           Margin="0,0,15,0"/>
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Label Text="Database Connection Test"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               TextColor="#0369A1"/>
                                        <Label Text="Test database connection and reset functionality"
                                               FontSize="12"
                                               TextColor="#0284C7"/>
                                    </StackLayout>
                                </Grid>

                                <Button Text="Run Database Test"
                                        FontSize="14"
                                        FontAttributes="Bold"
                                        BackgroundColor="#0369A1"
                                        TextColor="White"
                                        CornerRadius="8"
                                        Padding="15"
                                        Clicked="OnDatabaseTestClicked"/>
                            </StackLayout>
                        </Frame>

                        <!-- 重置所有数据 -->
                        <Frame BackgroundColor="#FEF2F2"
                               CornerRadius="8"
                               Padding="15"
                               HasShadow="False">
                            <StackLayout Spacing="10">
                                <Grid ColumnDefinitions="Auto,*">
                                    <Label Grid.Column="0"
                                           Text="⚠️"
                                           FontSize="20"
                                           VerticalOptions="Center"
                                           Margin="0,0,15,0"/>
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Label Text="Reset All Data"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               TextColor="#DC2626"/>
                                        <Label Text="This will permanently delete all data"
                                               FontSize="12"
                                               TextColor="#991B1B"/>
                                    </StackLayout>
                                </Grid>

                                <Button Text="Reset All Data"
                                        FontSize="14"
                                        FontAttributes="Bold"
                                        BackgroundColor="#DC2626"
                                        TextColor="White"
                                        CornerRadius="8"
                                        Padding="15"
                                        Command="{Binding ResetAllDataCommand}"/>
                            </StackLayout>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 关于应用 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <Frame BackgroundColor="#F8F9FA"
                           CornerRadius="8"
                           Padding="15"
                           HasShadow="False">
                        <Grid ColumnDefinitions="Auto,*,Auto">
                            <Label Grid.Column="0"
                                   Text="ℹ️"
                                   FontSize="20"
                                   VerticalOptions="Center"
                                   Margin="0,0,15,0"/>
                            <StackLayout Grid.Column="1" VerticalOptions="Center">
                                <Label Text="About App"
                                       FontSize="14"
                                       FontAttributes="Bold"
                                       TextColor="#333333"/>
                                <Label Text="{Binding AppVersion, StringFormat='Version {0}'}"
                                       FontSize="12"
                                       TextColor="#666666"/>
                            </StackLayout>
                            <Label Grid.Column="2"
                                   Text=">"
                                   FontSize="16"
                                   TextColor="#007ACC"
                                   VerticalOptions="Center"/>
                        </Grid>
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding AboutAppCommand}"/>
                        </Frame.GestureRecognizers>
                    </Frame>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsVisible="{Binding IsBusy}"
                                   IsRunning="{Binding IsBusy}"
                                   Color="#007ACC"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   Margin="20"/>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>