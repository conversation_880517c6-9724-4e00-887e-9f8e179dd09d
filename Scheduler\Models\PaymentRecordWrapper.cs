// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel; // MVVM组件模型 / MVVM component model

namespace Scheduler.Models;

/// <summary>
/// 支付记录包装类 / Payment Record Wrapper Class
/// 用于UI绑定和多选功能
/// Used for UI binding and multi-selection functionality
/// </summary>
public partial class PaymentRecordWrapper : ObservableObject
{
    /// <summary>
    /// 原始支付记录 / Original Payment Record
    /// 被包装的支付记录对象
    /// The wrapped payment record object
    /// </summary>
    public PaymentRecord PaymentRecord { get; set; }

    /// <summary>
    /// 雇主名称 / Employer Name
    /// 用于UI显示的雇主名称
    /// Employer name for UI display
    /// </summary>
    public string EmployerName { get; set; } = string.Empty;

    /// <summary>
    /// 是否被选中 / Is Selected
    /// 标识该支付记录是否在UI中被选中
    /// Indicates whether this payment record is selected in the UI
    /// </summary>
    [ObservableProperty]
    private bool isSelected;

    /// <summary>
    /// 是否已确认 / Is Confirmed
    /// 判断支付记录是否已确认支付
    /// Determines if the payment record has been confirmed as paid
    /// </summary>
    public bool IsConfirmed => PaymentRecord?.Status == PaymentStatus.Paid;

    /// <summary>
    /// 背景颜色 / Background Color
    /// 根据确认状态返回不同的背景颜色
    /// Returns different background colors based on confirmation status
    /// </summary>
    public string BackgroundColor => IsConfirmed ? "#424242" : "#2196F3";

    /// <summary>
    /// 文本颜色 / Text Color
    /// 根据确认状态返回不同的文本颜色
    /// Returns different text colors based on confirmation status
    /// </summary>
    public string TextColor => IsConfirmed ? "#BDBDBD" : "White";

    /// <summary>
    /// 金额文本颜色 / Amount Text Color
    /// 金额显示的文本颜色
    /// Text color for amount display
    /// </summary>
    public string AmountTextColor => IsConfirmed ? "#BDBDBD" : "#4CAF50";

    /// <summary>
    /// 状态文本颜色 / Status Text Color
    /// 状态显示的文本颜色
    /// Text color for status display
    /// </summary>
    public string StatusTextColor => IsConfirmed ? "#4CAF50" : "#FFC107";

    /// <summary>
    /// 文本装饰（删除线）
    /// </summary>
    public TextDecorations TextDecorations => IsConfirmed ? TextDecorations.Strikethrough : TextDecorations.None;

    /// <summary>
    /// 状态文本
    /// </summary>
    public string StatusText => IsConfirmed ? "已确认" : "待确认";

    /// <summary>
    /// 确认时间文本
    /// </summary>
    public string ConfirmationTimeText
    {
        get
        {
            if (IsConfirmed && PaymentRecord?.ActualPaymentDate.HasValue == true)
            {
                return $"确认时间: {PaymentRecord.ActualPaymentDate.Value:yyyy-MM-dd HH:mm:ss}";
            }
            return string.Empty;
        }
    }

    public PaymentRecordWrapper(PaymentRecord paymentRecord, string employerName = "")
    {
        PaymentRecord = paymentRecord;
        EmployerName = employerName;
    }

    /// <summary>
    /// 通知所有属性变更（用于状态更新后刷新UI）
    /// </summary>
    public void RefreshProperties()
    {
        OnPropertyChanged(nameof(IsConfirmed));
        OnPropertyChanged(nameof(BackgroundColor));
        OnPropertyChanged(nameof(TextColor));
        OnPropertyChanged(nameof(AmountTextColor));
        OnPropertyChanged(nameof(StatusTextColor));
        OnPropertyChanged(nameof(TextDecorations));
        OnPropertyChanged(nameof(StatusText));
        OnPropertyChanged(nameof(ConfirmationTimeText));
    }
}
