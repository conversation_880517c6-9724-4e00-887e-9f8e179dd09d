// 引入必要的命名空间 / Import necessary namespaces
using Plugin.LocalNotification;                // 本地通知插件 / Local notification plugin
using Plugin.LocalNotification.AndroidOption; // Android通知选项 / Android notification options
using Scheduler.Models;                         // 数据模型 / Data models

namespace Scheduler.Services
{
    /// <summary>
    /// 通知服务接口 / Notification Service Interface
    /// 定义应用程序通知功能的标准契约
    /// Defines standard contract for application notification functionality
    /// </summary>
    public interface INotificationService
    {
        // 权限管理 / Permission management
        /// <summary>请求通知权限 / Request notification permission</summary>
        Task<bool> RequestNotificationPermissionAsync();
        /// <summary>检查通知权限 / Check notification permission</summary>
        Task<bool> CheckNotificationPermissionAsync();

        // 班次相关通知 / Shift related notifications
        /// <summary>调度班次提醒 / Schedule shift reminder</summary>
        Task ScheduleShiftReminderAsync(Shift shift);
        /// <summary>调度每日工作安排通知 / Schedule daily work schedule notification</summary>
        Task ScheduleDailyWorkScheduleNotificationAsync(DateTime date);
        /// <summary>取消班次通知 / Cancel shift notifications</summary>
        Task CancelShiftNotificationsAsync(int shiftId);

        // 工作相关通知 / Work related notifications
        /// <summary>发送休息提醒 / Send break reminder</summary>
        Task SendBreakReminderAsync(string message);
        /// <summary>发送休息提醒 / Send break reminder</summary>
        Task SendBreakReminderAsync(WorkRecord activeRecord);
        /// <summary>调度周期性休息提醒 / Schedule periodic break reminders</summary>
        Task SchedulePeriodicBreakRemindersAsync(WorkRecord workRecord);

        // 薪资相关通知 / Salary related notifications
        /// <summary>发送付款到期通知 / Send payment due notification</summary>
        Task SendPaymentDueNotificationAsync(PaymentRecord payment);
        /// <summary>发送付款确认提醒 / Send payment confirmation reminder</summary>
        Task SendPaymentConfirmationReminderAsync(PaymentRecord payment);
        /// <summary>调度付款提醒 / Schedule payment reminders</summary>
        Task SchedulePaymentRemindersAsync();

        // 待办事项相关通知 / Todo item related notifications
        /// <summary>调度待办事项截止日期提醒 / Schedule todo deadline reminder</summary>
        Task ScheduleTodoDeadlineReminderAsync(TodoItem todoItem);
        /// <summary>发送待办事项截止日期通知 / Send todo deadline notification</summary>
        Task SendTodoDeadlineNotificationAsync(TodoItem todoItem);
        /// <summary>调度所有待办事项提醒 / Schedule all todo reminders</summary>
        Task ScheduleAllTodoRemindersAsync();
        /// <summary>取消待办事项通知 / Cancel todo notifications</summary>
        Task CancelTodoNotificationsAsync(int todoId);

        // 冲突和系统通知 / Conflict and system notifications
        /// <summary>发送班次冲突通知 / Send shift conflict notification</summary>
        Task SendShiftConflictNotificationAsync(string message);
        /// <summary>发送班次冲突通知 / Send shift conflict notification</summary>
        Task SendShiftConflictNotificationAsync(List<ShiftConflict> conflicts);

        // 通知管理 / Notification management
        /// <summary>取消通知 / Cancel notification</summary>
        Task CancelNotificationAsync(int notificationId);
        /// <summary>取消通知 / Cancel notification</summary>
        Task CancelNotificationAsync(int entityId, string notificationType);
        /// <summary>取消所有通知 / Cancel all notifications</summary>
        Task CancelAllNotificationsAsync();
        /// <summary>获取通知历史 / Get notification history</summary>
        Task<List<NotificationRecord>> GetNotificationHistoryAsync(int limit = 50);
        /// <summary>清除通知历史 / Clear notification history</summary>
        Task ClearNotificationHistoryAsync();
        /// <summary>标记通知为已读 / Mark notification as read</summary>
        Task MarkNotificationAsReadAsync(int notificationId);

        // 测试通知 / Test notifications
        /// <summary>发送测试通知 / Send test notification</summary>
        Task SendTestNotificationAsync();

        // 批量操作
        Task ScheduleAllShiftRemindersAsync();
        Task CleanupExpiredNotificationsAsync();

        // 通知交互
        Task HandleNotificationClickAsync(int notificationId);
    }

    /// <summary>
    /// 通知服务实现类 - 提供完整的本地通知功能
    /// Notification service implementation - provides complete local notification functionality
    /// </summary>
    /// <remarks>
    /// 该服务负责管理应用程序的所有通知功能，包括：
    /// - 权限管理和状态检查
    /// - 班次提醒和工作调度通知
    /// - 休息提醒和工作时间管理
    /// - 薪资支付提醒和确认通知
    /// - 冲突检测和系统通知
    /// - 通知历史记录和状态管理
    /// </remarks>
    public class NotificationService : INotificationService
    {
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 初始化通知服务实例
        /// Initialize NotificationService with database service
        /// </summary>
        /// <param name="databaseService">数据库服务实例，用于存储通知记录</param>
        public NotificationService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        #region 权限管理 - Permission Management

        /// <summary>
        /// 请求通知权限
        /// Request notification permission from the system
        /// </summary>
        /// <returns>是否成功获得权限</returns>
        /// <remarks>
        /// 在Android平台上使用POST_NOTIFICATIONS权限，
        /// 在其他平台上使用LocalNotificationCenter检查权限状态
        /// </remarks>
        public async Task<bool> RequestNotificationPermissionAsync()
        {
            try
            {
#if ANDROID
                var status = await Permissions.RequestAsync<Permissions.PostNotifications>();
                return status == Microsoft.Maui.ApplicationModel.PermissionStatus.Granted;
#else
                return await LocalNotificationCenter.Current.AreNotificationsEnabled();
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"请求通知权限失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查通知权限状态
        /// Check current notification permission status
        /// </summary>
        /// <returns>当前是否拥有通知权限</returns>
        /// <remarks>
        /// 该方法不会触发权限请求对话框，只检查当前状态
        /// </remarks>
        public async Task<bool> CheckNotificationPermissionAsync()
        {
            try
            {
#if ANDROID
                var status = await Permissions.CheckStatusAsync<Permissions.PostNotifications>();
                return status == Microsoft.Maui.ApplicationModel.PermissionStatus.Granted;
#else
                return await LocalNotificationCenter.Current.AreNotificationsEnabled();
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查通知权限失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 班次相关通知

        /// <summary>
        /// 安排每日工作安排通知
        /// </summary>
        public async Task ScheduleDailyWorkScheduleNotificationAsync(DateTime date)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法发送每日工作安排通知");
                    return;
                }

                // 获取当天的班次
                var shifts = await _databaseService.GetShiftsByDateAsync(date);
                if (!shifts.Any()) return;

                var shiftSummary = new List<string>();
                foreach (var shift in shifts.OrderBy(s => s.StartTime))
                {
                    var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
                    var timeRange = $"{shift.StartTime:HH:mm}-{shift.EndTime:HH:mm}";
                    shiftSummary.Add($"• {timeRange} {employer?.Name ?? "未知雇主"}");
                }

                var notification = new NotificationRequest
                {
                    NotificationId = GenerateNotificationId(date.GetHashCode(), "daily_schedule"),
                    Title = "今日工作安排",
                    Subtitle = $"{shifts.Count} 个班次",
                    Description = $"今天您有 {shifts.Count} 个班次:\n{string.Join("\n", shiftSummary)}",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = date.Date.AddHours(8) // 早上8点发送
                    }
                };

                await LocalNotificationCenter.Current.Show(notification);

                // 记录通知
                await SaveNotificationRecordAsync(new NotificationRecord
                {
                    Type = NotificationType.WorkReminder,
                    Title = notification.Title,
                    Message = notification.Description,
                    ScheduledAt = notification.Schedule.NotifyTime,
                    AdditionalData = System.Text.Json.JsonSerializer.Serialize(new {
                        Date = date.ToString("yyyy-MM-dd"),
                        ShiftCount = shifts.Count,
                        ShiftIds = shifts.Select(s => s.Id).ToArray()
                    })
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排每日工作安排通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排班次提醒（支持自定义提前时间）
        /// </summary>
        public async Task ScheduleShiftReminderAsync(Shift shift)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法安排班次提醒");
                    return;
                }

                // 获取通知设置
                var settings = await _databaseService.GetNotificationSettingsAsync();
                if (settings == null || !settings.IsShiftReminderEnabled)
                {
                    System.Diagnostics.Debug.WriteLine("全局班次提醒已禁用");
                    return;
                }

                // 检查班次级别的提醒设置
                if (!shift.IsReminderEnabled)
                {
                    System.Diagnostics.Debug.WriteLine($"班次 {shift.Id} 的提醒已禁用");
                    return;
                }

                // 检查是否在免打扰时间内
                if (settings.IsQuietHoursEnabled && settings.IsInQuietHours)
                {
                    System.Diagnostics.Debug.WriteLine("当前在免打扰时间内，跳过班次提醒");
                    return;
                }

                var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
                if (employer == null) return;

                // 使用班次级别的提前时间设置，如果没有设置则使用全局设置
                var reminderMinutes = shift.ReminderMinutes > 0 ? shift.ReminderMinutes : settings.ShiftReminderMinutes;
                var reminderTime = shift.StartTime.AddMinutes(-reminderMinutes);

                if (reminderTime > DateTime.Now)
                {
                    var notification = new NotificationRequest
                    {
                        NotificationId = GenerateNotificationId(shift.Id, "shift_reminder"),
                        Title = "工作提醒",
                        Subtitle = employer.Name,
                        Description = $"您的班次将在{reminderMinutes}分钟后开始\n时间: {shift.StartTime:HH:mm} - {shift.EndTime:HH:mm}\n地点: {shift.Location}",
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = reminderTime
                        },
                        Sound = settings.IsSoundEnabled ? "default" : null
                    };

                    // 保存通知ID到班次记录中
                    shift.NotificationId = notification.NotificationId;
                    await _databaseService.SaveShiftAsync(shift);

                    await LocalNotificationCenter.Current.Show(notification);

                    // 记录通知
                    await SaveNotificationRecordAsync(new NotificationRecord
                    {
                        Type = NotificationType.WorkReminder,
                        Title = notification.Title,
                        Message = notification.Description,
                        ScheduledAt = reminderTime,
                        RelatedEntityId = shift.Id,
                        RelatedEntityType = "Shift"
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排班次提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消班次相关的所有通知
        /// </summary>
        public async Task CancelShiftNotificationsAsync(int shiftId)
        {
            try
            {
                // 取消班次提醒通知
                var shiftReminderNotificationId = GenerateNotificationId(shiftId, "shift_reminder");
                LocalNotificationCenter.Current.Cancel(shiftReminderNotificationId);

                // 取消相关的休息提醒通知
                var breakReminderNotificationId = GenerateNotificationId(shiftId, "break_reminder");
                LocalNotificationCenter.Current.Cancel(breakReminderNotificationId);

                System.Diagnostics.Debug.WriteLine($"已取消班次 {shiftId} 的所有通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消班次通知失败: {ex.Message}");
            }
        }

        #endregion

        #region 工作相关通知

        /// <summary>
        /// 安排定期休息提醒（支持自定义间隔和智能提醒）
        /// </summary>
        public async Task SchedulePeriodicBreakRemindersAsync(WorkRecord workRecord)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法安排休息提醒");
                    return;
                }

                if (!workRecord.ClockInTime.HasValue) return;

                // 获取通知设置
                var settings = await _databaseService.GetNotificationSettingsAsync();
                if (settings == null || !settings.IsBreakReminderEnabled)
                {
                    System.Diagnostics.Debug.WriteLine("休息提醒已禁用");
                    return;
                }

                // 检查是否只在工作时间内提醒
                if (settings.BreakReminderOnlyDuringWork)
                {
                    var shift = await _databaseService.GetShiftAsync(workRecord.ShiftId);
                    if (shift == null) return;

                    var workStartTime = workRecord.ClockInTime.Value;
                    var workEndTime = shift.EndTime;
                    var reminderInterval = TimeSpan.FromHours(settings.BreakReminderIntervalHours);
                    var currentTime = workStartTime.Add(reminderInterval);

                    while (currentTime < workEndTime && currentTime > DateTime.Now)
                    {
                        // 检查是否在免打扰时间内
                        if (settings.IsQuietHoursEnabled && IsTimeInQuietHours(currentTime, settings))
                        {
                            currentTime = currentTime.Add(reminderInterval);
                            continue;
                        }

                        var workDuration = currentTime - workStartTime;
                        var notification = new NotificationRequest
                        {
                            NotificationId = GenerateNotificationId(workRecord.Id, $"break_reminder_{currentTime.Ticks}"),
                            Title = "休息提醒",
                            Subtitle = "该休息一下了",
                            Description = $"您已连续工作 {workDuration.Hours} 小时 {workDuration.Minutes} 分钟\n建议休息 10-15 分钟，保持工作效率",
                            Schedule = new NotificationRequestSchedule
                            {
                                NotifyTime = currentTime
                            },
                            Sound = settings.IsSoundEnabled ? "default" : null
                        };

                        await LocalNotificationCenter.Current.Show(notification);

                        // 记录通知
                        await SaveNotificationRecordAsync(new NotificationRecord
                        {
                            Type = NotificationType.BreakReminder,
                            Title = notification.Title,
                            Message = notification.Description,
                            ScheduledAt = currentTime,
                            RelatedEntityId = workRecord.Id,
                            RelatedEntityType = "WorkRecord"
                        });

                        currentTime = currentTime.Add(reminderInterval);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排定期休息提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查指定时间是否在免打扰时间内
        /// </summary>
        private bool IsTimeInQuietHours(DateTime time, NotificationSettings settings)
        {
            var timeOfDay = time.TimeOfDay;
            var quietStart = new TimeSpan(settings.QuietHoursStartHour, settings.QuietHoursStartMinute, 0);
            var quietEnd = new TimeSpan(settings.QuietHoursEndHour, settings.QuietHoursEndMinute, 0);

            // 处理跨天的情况（如22:00-07:00）
            if (quietStart > quietEnd)
            {
                return timeOfDay >= quietStart || timeOfDay <= quietEnd;
            }
            else
            {
                return timeOfDay >= quietStart && timeOfDay <= quietEnd;
            }
        }

        #endregion

        #region 薪资相关通知

        /// <summary>
        /// 发送支付到期通知（支持自定义提前时间）
        /// </summary>
        public async Task SendPaymentDueNotificationAsync(PaymentRecord payment)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法发送支付提醒");
                    return;
                }

                // 获取通知设置
                var settings = await _databaseService.GetNotificationSettingsAsync();
                if (settings == null || !settings.IsPaymentReminderEnabled)
                {
                    System.Diagnostics.Debug.WriteLine("支付提醒已禁用");
                    return;
                }

                // 检查是否在免打扰时间内
                if (settings.IsQuietHoursEnabled && settings.IsInQuietHours)
                {
                    System.Diagnostics.Debug.WriteLine("当前在免打扰时间内，跳过支付提醒");
                    return;
                }

                var employer = await _databaseService.GetEmployerAsync(payment.EmployerId);
                if (employer == null) return;

                // 计算支付状态描述
                var statusDescription = payment.Status switch
                {
                    PaymentStatus.Pending => "待支付",
                    PaymentStatus.Overdue => "已逾期",
                    PaymentStatus.Paid => "已支付",
                    _ => "未知状态"
                };

                var notification = new NotificationRequest
                {
                    NotificationId = GenerateNotificationId(payment.Id, "payment_due"),
                    Title = "支付提醒",
                    Subtitle = employer.Name,
                    Description = $"工资支付提醒 ({statusDescription})\n金额: ${payment.TotalAmount:F2}\n期间: {payment.PeriodStart:MM/dd} - {payment.PeriodEnd:MM/dd}",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now
                    },
                    Sound = settings.IsSoundEnabled ? "default" : null
                };

                await LocalNotificationCenter.Current.Show(notification);

                // 记录通知
                await SaveNotificationRecordAsync(new NotificationRecord
                {
                    Type = NotificationType.PaymentDue,
                    Title = notification.Title,
                    Message = notification.Description,
                    ScheduledAt = DateTime.Now,
                    SentAt = DateTime.Now,
                    Status = NotificationStatus.Sent,
                    RelatedEntityId = payment.Id,
                    RelatedEntityType = "PaymentRecord"
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送支付通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送薪资确认提醒
        /// </summary>
        public async Task SendPaymentConfirmationReminderAsync(PaymentRecord payment)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法发送薪资确认提醒");
                    return;
                }

                var employer = await _databaseService.GetEmployerAsync(payment.EmployerId);
                if (employer == null) return;

                var notification = new NotificationRequest
                {
                    NotificationId = GenerateNotificationId(payment.Id, "payment_confirmation"),
                    Title = "薪资确认提醒",
                    Subtitle = employer.Name,
                    Description = $"请确认您的薪资信息\n金额: ${payment.TotalAmount:F2}\n工作时长: {payment.TotalHours:F1} 小时\n期间: {payment.PeriodStart:MM/dd} - {payment.PeriodEnd:MM/dd}",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now
                    }
                };

                await LocalNotificationCenter.Current.Show(notification);

                // 记录通知
                await SaveNotificationRecordAsync(new NotificationRecord
                {
                    Type = NotificationType.PaymentDue,
                    Title = notification.Title,
                    Message = notification.Description,
                    ScheduledAt = DateTime.Now,
                    SentAt = DateTime.Now,
                    Status = NotificationStatus.Sent,
                    RelatedEntityId = payment.Id,
                    RelatedEntityType = "PaymentRecord"
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送薪资确认提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排薪资提醒
        /// </summary>
        public async Task SchedulePaymentRemindersAsync()
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法安排薪资提醒");
                    return;
                }

                var overduePayments = await _databaseService.GetOverduePaymentsAsync();

                foreach (var payment in overduePayments)
                {
                    // 检查是否已经发送过通知
                    var existingNotification = await GetNotificationRecordAsync(
                        payment.Id, "PaymentRecord", NotificationType.PaymentDue);

                    if (existingNotification == null)
                    {
                        await SendPaymentDueNotificationAsync(payment);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排薪资提醒失败: {ex.Message}");
            }
        }

        #endregion

        #region 待办事项相关通知

        /// <summary>
        /// 安排待办事项截止日期提醒
        /// </summary>
        public async Task ScheduleTodoDeadlineReminderAsync(TodoItem todoItem)
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法安排待办事项提醒");
                    return;
                }

                // 检查是否有截止日期
                if (!todoItem.DueDate.HasValue || todoItem.IsCompleted)
                {
                    return;
                }

                // 获取通知设置
                var settings = await _databaseService.GetNotificationSettingsAsync();
                if (settings == null || !settings.IsNotificationEnabled)
                {
                    return;
                }

                // 检查是否在免打扰时间内
                if (settings.IsQuietHoursEnabled && settings.IsInQuietHours)
                {
                    System.Diagnostics.Debug.WriteLine("当前在免打扰时间内，跳过待办事项提醒");
                    return;
                }

                var dueDate = todoItem.DueDate.Value;
                var now = DateTime.Now;

                // 安排多个提醒：1天前、1小时前、到期时
                var reminderTimes = new List<(DateTime time, string message)>();

                // 1天前提醒
                var oneDayBefore = dueDate.AddDays(-1);
                if (oneDayBefore > now)
                {
                    reminderTimes.Add((oneDayBefore, "明天到期"));
                }

                // 1小时前提醒
                var oneHourBefore = dueDate.AddHours(-1);
                if (oneHourBefore > now)
                {
                    reminderTimes.Add((oneHourBefore, "1小时后到期"));
                }

                // 到期时提醒
                if (dueDate > now)
                {
                    reminderTimes.Add((dueDate, "现在到期"));
                }

                foreach (var (time, message) in reminderTimes)
                {
                    var notification = new NotificationRequest
                    {
                        NotificationId = GenerateNotificationId(todoItem.Id, $"todo_reminder_{time.Ticks}"),
                        Title = "待办事项提醒",
                        Subtitle = todoItem.Title,
                        Description = $"{todoItem.Title}\n{message}",
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = time
                        },
                        Sound = settings.IsSoundEnabled ? "default" : null
                    };

                    await LocalNotificationCenter.Current.Show(notification);

                    // 记录通知
                    await SaveNotificationRecordAsync(new NotificationRecord
                    {
                        Type = NotificationType.WorkReminder, // 使用现有的枚举值
                        Title = notification.Title,
                        Message = notification.Description,
                        ScheduledAt = time,
                        RelatedEntityId = todoItem.Id,
                        RelatedEntityType = "TodoItem"
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排待办事项提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送待办事项截止通知
        /// </summary>
        public async Task SendTodoDeadlineNotificationAsync(TodoItem todoItem)
        {
            try
            {
                if (!await CheckNotificationPermissionAsync())
                {
                    return;
                }

                var notification = new NotificationRequest
                {
                    NotificationId = GenerateNotificationId(todoItem.Id, "todo_deadline"),
                    Title = "待办事项到期",
                    Subtitle = todoItem.Title,
                    Description = $"任务 '{todoItem.Title}' 已到期，请及时处理",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now
                    }
                };

                await LocalNotificationCenter.Current.Show(notification);

                // 记录通知
                await SaveNotificationRecordAsync(new NotificationRecord
                {
                    Type = NotificationType.WorkReminder,
                    Title = notification.Title,
                    Message = notification.Description,
                    ScheduledAt = DateTime.Now,
                    SentAt = DateTime.Now,
                    Status = NotificationStatus.Sent,
                    RelatedEntityId = todoItem.Id,
                    RelatedEntityType = "TodoItem"
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送待办事项截止通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排所有待办事项提醒
        /// </summary>
        public async Task ScheduleAllTodoRemindersAsync()
        {
            try
            {
                if (!await CheckNotificationPermissionAsync())
                {
                    return;
                }

                // 获取所有未完成且有截止日期的待办事项
                var todoItems = await _databaseService.GetTodoItemsAsync();
                var pendingTodos = todoItems.Where(t => !t.IsCompleted && t.DueDate.HasValue && t.DueDate > DateTime.Now).ToList();

                foreach (var todo in pendingTodos)
                {
                    await ScheduleTodoDeadlineReminderAsync(todo);
                }

                System.Diagnostics.Debug.WriteLine($"已安排 {pendingTodos.Count} 个待办事项的提醒通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排所有待办事项提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消待办事项相关通知
        /// </summary>
        public async Task CancelTodoNotificationsAsync(int todoId)
        {
            try
            {
                // 取消所有相关的通知
                var notificationTypes = new[] { "todo_reminder", "todo_deadline" };

                foreach (var type in notificationTypes)
                {
                    var notificationId = GenerateNotificationId(todoId, type);
                    LocalNotificationCenter.Current.Cancel(notificationId);
                }

                System.Diagnostics.Debug.WriteLine($"已取消待办事项 {todoId} 的所有通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消待办事项通知失败: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 发送休息提醒
        /// </summary>
        public async Task SendBreakReminderAsync(WorkRecord activeRecord)
        {
            try
            {
                if (!activeRecord.ClockInTime.HasValue) return;

                var workDuration = DateTime.Now - activeRecord.ClockInTime.Value;
                
                // 工作2小时后提醒休息
                if (workDuration.TotalHours >= 2)
                {
                    var notification = new NotificationRequest
                    {
                        NotificationId = GenerateNotificationId(activeRecord.Id, "break_reminder"),
                        Title = "休息提醒",
                        Subtitle = "该休息一下了",
                        Description = $"您已连续工作 {workDuration.Hours} 小时 {workDuration.Minutes} 分钟，建议休息一下",
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = DateTime.Now
                        }
                    };

                    await LocalNotificationCenter.Current.Show(notification);

                    // 记录通知
                    await SaveNotificationRecordAsync(new NotificationRecord
                    {
                        Type = NotificationType.BreakReminder,
                        Title = notification.Title,
                        Message = notification.Description,
                        ScheduledAt = DateTime.Now,
                        SentAt = DateTime.Now,
                        Status = NotificationStatus.Sent,
                        RelatedEntityId = activeRecord.Id,
                        RelatedEntityType = "WorkRecord"
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送休息提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送班次冲突通知
        /// </summary>
        public async Task SendShiftConflictNotificationAsync(List<ShiftConflict> conflicts)
        {
            try
            {
                if (!conflicts.Any()) return;

                var notification = new NotificationRequest
                {
                    NotificationId = GenerateNotificationId(0, "shift_conflict"),
                    Title = "班次冲突警告",
                    Subtitle = $"检测到 {conflicts.Count} 个冲突",
                    Description = "您的班次安排存在时间冲突，请检查并调整",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now
                    }
                };

                await LocalNotificationCenter.Current.Show(notification);

                // 记录通知
                await SaveNotificationRecordAsync(new NotificationRecord
                {
                    Type = NotificationType.ShiftConflict,
                    Title = notification.Title,
                    Message = notification.Description,
                    ScheduledAt = DateTime.Now,
                    SentAt = DateTime.Now,
                    Status = NotificationStatus.Sent,
                    AdditionalData = System.Text.Json.JsonSerializer.Serialize(conflicts.Select(c => new { 
                        NewShiftId = c.NewShift.Id, 
                        ConflictingShiftId = c.ConflictingShift.Id,
                        ConflictType = c.ConflictType.ToString()
                    }))
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送冲突通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消通知
        /// </summary>
        public async Task CancelNotificationAsync(int entityId, string notificationType)
        {
            try
            {
                var notificationId = GenerateNotificationId(entityId, notificationType);
                LocalNotificationCenter.Current.Cancel(notificationId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消所有通知
        /// </summary>
        public async Task CancelAllNotificationsAsync()
        {
            try
            {
                LocalNotificationCenter.Current.CancelAll();
                System.Diagnostics.Debug.WriteLine("已取消所有通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消所有通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取通知历史记录
        /// </summary>
        public async Task<List<NotificationRecord>> GetNotificationHistoryAsync(int limit = 50)
        {
            try
            {
                return await _databaseService.GetUserNotificationsAsync(null, limit);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取通知历史失败: {ex.Message}");
                return new List<NotificationRecord>();
            }
        }

        /// <summary>
        /// 清除通知历史记录
        /// </summary>
        public async Task ClearNotificationHistoryAsync()
        {
            try
            {
                await _databaseService.ClearNotificationHistoryAsync();
                System.Diagnostics.Debug.WriteLine("通知历史记录已清除");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除通知历史失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        public async Task MarkNotificationAsReadAsync(int notificationId)
        {
            try
            {
                await _databaseService.MarkNotificationAsReadAsync(notificationId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"标记通知为已读失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并发送逾期支付通知
        /// </summary>
        public async Task CheckAndSendOverduePaymentNotificationsAsync()
        {
            try
            {
                var overduePayments = await _databaseService.GetOverduePaymentsAsync();

                foreach (var payment in overduePayments)
                {
                    // 检查是否已经发送过通知
                    var existingNotification = await GetNotificationRecordAsync(
                        payment.Id, "PaymentRecord", NotificationType.PaymentDue);

                    if (existingNotification == null)
                    {
                        await SendPaymentDueNotificationAsync(payment);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查逾期支付通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排所有班次提醒
        /// </summary>
        public async Task ScheduleAllShiftRemindersAsync()
        {
            try
            {
                // 检查权限
                if (!await CheckNotificationPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("没有通知权限，无法安排班次提醒");
                    return;
                }

                // 获取未来7天的班次
                var startDate = DateTime.Today;
                var endDate = startDate.AddDays(7);
                var shifts = await _databaseService.GetShiftsByDateRangeAsync(startDate, endDate);

                foreach (var shift in shifts)
                {
                    if (shift.Status == ShiftStatus.Scheduled && shift.StartTime > DateTime.Now)
                    {
                        await ScheduleShiftReminderAsync(shift);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已安排 {shifts.Count} 个班次的提醒通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排所有班次提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理过期通知
        /// </summary>
        public async Task CleanupExpiredNotificationsAsync()
        {
            try
            {
                // 删除30天前的通知记录
                var cutoffDate = DateTime.Now.AddDays(-30);
                await _databaseService.DeleteExpiredNotificationsAsync(cutoffDate);

                System.Diagnostics.Debug.WriteLine("已清理过期通知记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理过期通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成通知ID
        /// </summary>
        private int GenerateNotificationId(int entityId, string type)
        {
            return $"{entityId}_{type}".GetHashCode();
        }

        /// <summary>
        /// 保存通知记录
        /// </summary>
        private async Task SaveNotificationRecordAsync(NotificationRecord record)
        {
            try
            {
                await _databaseService.SaveNotificationRecordAsync(record);
                System.Diagnostics.Debug.WriteLine($"通知记录已保存: {record.Title}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存通知记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取通知记录
        /// </summary>
        private async Task<NotificationRecord?> GetNotificationRecordAsync(int entityId, string entityType, NotificationType type)
        {
            try
            {
                return await _databaseService.GetNotificationRecordAsync(entityId, entityType, type);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取通知记录失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 发送休息提醒
        /// </summary>
        public async Task SendBreakReminderAsync(string message)
        {
            try
            {
                var request = new NotificationRequest
                {
                    NotificationId = new Random().Next(1000, 9999),
                    Title = "休息提醒",
                    Subtitle = "工作间隙",
                    Description = message,
                    BadgeNumber = 1,
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now.AddSeconds(1)
                    }
                };

                await LocalNotificationCenter.Current.Show(request);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送休息提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送班次冲突通知
        /// </summary>
        public async Task SendShiftConflictNotificationAsync(string message)
        {
            try
            {
                var request = new NotificationRequest
                {
                    NotificationId = new Random().Next(1000, 9999),
                    Title = "班次冲突警告",
                    Subtitle = "时间安排",
                    Description = message,
                    BadgeNumber = 1,
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now.AddSeconds(1)
                    }
                };

                await LocalNotificationCenter.Current.Show(request);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送冲突通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消通知
        /// </summary>
        public async Task CancelNotificationAsync(int notificationId)
        {
            try
            {
                LocalNotificationCenter.Current.Cancel(notificationId);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送测试通知 (Send test notification)
        /// </summary>
        public async Task SendTestNotificationAsync()
        {
            try
            {
                // 检查通知权限 (Check notification permission)
                var hasPermission = await CheckNotificationPermissionAsync();
                if (!hasPermission)
                {
                    var permissionGranted = await RequestNotificationPermissionAsync();
                    if (!permissionGranted)
                    {
                        System.Diagnostics.Debug.WriteLine("测试通知失败: 没有通知权限");
                        return;
                    }
                }

                // 发送测试通知 (Send test notification)
                var testNotification = new NotificationRequest
                {
                    NotificationId = 999999,
                    Title = "📱 通知功能测试",
                    Description = "这是一个测试通知，用于验证通知功能是否正常工作。如果您看到这条通知，说明通知系统运行正常！",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now.AddSeconds(3)
                    }
                };

                await LocalNotificationCenter.Current.Show(testNotification);

                // 记录测试通知 (Record test notification)
                var testRecord = new NotificationRecord
                {
                    Type = NotificationType.SystemUpdate,
                    Title = "📱 通知功能测试",
                    Message = "这是一个测试通知，用于验证通知功能是否正常工作。",
                    Status = NotificationStatus.Sent,
                    ScheduledAt = DateTime.Now.AddSeconds(3),
                    SentAt = DateTime.Now,
                    RelatedEntityType = "TestNotification",
                    CreatedAt = DateTime.Now
                };

                await SaveNotificationRecordAsync(testRecord);
                System.Diagnostics.Debug.WriteLine("测试通知已发送并记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送测试通知失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理通知点击事件
        /// </summary>
        public async Task HandleNotificationClickAsync(int notificationId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"处理通知点击: NotificationId={notificationId}");

                // 查找通知记录
                var notifications = await _databaseService.GetUserNotificationsAsync();
                var notification = notifications.FirstOrDefault(n => n.Id == notificationId);

                if (notification != null)
                {
                    // 标记为已读
                    await MarkNotificationAsReadAsync(notificationId);

                    // 这里可以添加导航逻辑
                    // 由于NavigationService需要在UI线程中使用，这里只记录日志
                    System.Diagnostics.Debug.WriteLine($"通知点击处理完成: {notification.Type}, EntityId: {notification.RelatedEntityId}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到通知记录: {notificationId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理通知点击失败: {ex.Message}");
            }
        }
    }
}
