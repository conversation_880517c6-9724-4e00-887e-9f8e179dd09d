using Microsoft.EntityFrameworkCore;
using DishMeAPP.Models;
using DishMeAPP.Data;

namespace DishMeAPP.Services
{
    /// <summary>
    /// 菜谱存储服务 - 基于SQLite数据库的菜谱数据存储和检索
    /// Recipe Storage Service - SQLite database-based recipe data storage and retrieval
    /// 负责所有菜谱相关的数据库操作，包括增删改查、搜索和统计功能
    /// </summary>
    public class RecipeStorageService : IDisposable
    {
        private readonly RecipeDbContext _context;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数 - 初始化数据库上下文
        /// Constructor - Initialize database context
        /// </summary>
        public RecipeStorageService()
        {
            try
            {
                _context = new RecipeDbContext();
                
                // 异步初始化数据库（在构造函数中同步等待）
                // Asynchronously initialize database (synchronously wait in constructor)
                Task.Run(async () => await _context.EnsureDatabaseCreatedAsync()).Wait();
                
                System.Diagnostics.Debug.WriteLine("RecipeStorageService (SQLite) 初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RecipeStorageService 初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 保存菜谱到数据库（新增或更新）
        /// Save recipe to database (create or update)
        /// </summary>
        /// <param name="recipe">要保存的菜谱对象</param>
        /// <returns>保存后的菜谱对象（包含生成的ID）</returns>
        public async Task<Recipe> SaveRecipeAsync(Recipe recipe)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始保存菜谱: {recipe.Name}");

                if (recipe.Id == 0)
                {
                    // 新增菜谱 Create new recipe
                    recipe.CreatedAt = DateTime.Now;
                    recipe.UpdatedAt = DateTime.Now;
                    
                    _context.Recipes.Add(recipe);
                    System.Diagnostics.Debug.WriteLine($"新增菜谱: {recipe.Name}");
                }
                else
                {
                    // 更新现有菜谱 Update existing recipe
                    recipe.UpdatedAt = DateTime.Now;
                    
                    _context.Recipes.Update(recipe);
                    System.Diagnostics.Debug.WriteLine($"更新菜谱: {recipe.Name} (ID: {recipe.Id})");
                }

                // 保存到数据库 Save to database
                await _context.SaveChangesAsync();
                
                System.Diagnostics.Debug.WriteLine($"菜谱保存成功: {recipe.Name} (ID: {recipe.Id})");
                return recipe;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存菜谱失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 获取所有菜谱（按创建时间倒序）
        /// Get all recipes (ordered by creation time descending)
        /// </summary>
        /// <returns>菜谱列表</returns>
        public async Task<List<Recipe>> GetAllRecipesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始获取所有菜谱...");

                var recipes = await _context.Recipes
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"获取到 {recipes.Count} 个菜谱");
                return recipes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取菜谱列表失败: {ex.Message}");
                return new List<Recipe>();
            }
        }

        /// <summary>
        /// 按分类获取菜谱
        /// Get recipes by category
        /// </summary>
        /// <param name="category">菜谱分类</param>
        /// <returns>指定分类的菜谱列表</returns>
        public async Task<List<Recipe>> GetRecipesByCategoryAsync(string category)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始获取分类菜谱: {category}");

                if (string.IsNullOrWhiteSpace(category))
                {
                    return await GetAllRecipesAsync();
                }

                var recipes = await _context.Recipes
                    .Where(r => r.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"分类 '{category}' 获取到 {recipes.Count} 个菜谱");
                return recipes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"按分类获取菜谱失败: {ex.Message}");
                return new List<Recipe>();
            }
        }

        /// <summary>
        /// 根据ID获取单个菜谱
        /// Get single recipe by ID
        /// </summary>
        /// <param name="id">菜谱ID</param>
        /// <returns>菜谱对象，如果不存在则返回null</returns>
        public async Task<Recipe?> GetRecipeByIdAsync(int id)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始获取菜谱: ID {id}");

                var recipe = await _context.Recipes.FindAsync(id);
                
                if (recipe != null)
                {
                    System.Diagnostics.Debug.WriteLine($"找到菜谱: {recipe.Name}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到ID为 {id} 的菜谱");
                }

                return recipe;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取菜谱失败 (ID: {id}): {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 删除菜谱
        /// Delete recipe
        /// </summary>
        /// <param name="id">要删除的菜谱ID</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteRecipeAsync(int id)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始删除菜谱: ID {id}");

                var recipe = await _context.Recipes.FindAsync(id);
                if (recipe != null)
                {
                    _context.Recipes.Remove(recipe);
                    await _context.SaveChangesAsync();
                    
                    System.Diagnostics.Debug.WriteLine($"菜谱删除成功: {recipe.Name} (ID: {id})");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到要删除的菜谱: ID {id}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除菜谱失败 (ID: {id}): {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 搜索菜谱（按名称、食材或步骤搜索）
        /// Search recipes (by name, ingredients, or steps)
        /// </summary>
        /// <param name="searchTerm">搜索关键词</param>
        /// <returns>匹配的菜谱列表</returns>
        public async Task<List<Recipe>> SearchRecipesAsync(string searchTerm)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始搜索菜谱: '{searchTerm}'");

                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return await GetAllRecipesAsync();
                }

                var recipes = await _context.Recipes
                    .Where(r => r.Name.Contains(searchTerm) || 
                               r.Ingredients.Contains(searchTerm) ||
                               r.Steps.Contains(searchTerm) ||
                               (r.Tags != null && r.Tags.Contains(searchTerm)))
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"搜索 '{searchTerm}' 找到 {recipes.Count} 个菜谱");
                return recipes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索菜谱失败: {ex.Message}");
                return new List<Recipe>();
            }
        }

        /// <summary>
        /// 获取收藏的菜谱
        /// Get favorite recipes
        /// </summary>
        /// <returns>收藏的菜谱列表</returns>
        public async Task<List<Recipe>> GetFavoriteRecipesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始获取收藏菜谱...");

                var recipes = await _context.Recipes
                    .Where(r => r.IsFavorite)
                    .OrderByDescending(r => r.CreatedAt)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"获取到 {recipes.Count} 个收藏菜谱");
                return recipes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取收藏菜谱失败: {ex.Message}");
                return new List<Recipe>();
            }
        }

        /// <summary>
        /// 切换菜谱收藏状态
        /// Toggle recipe favorite status
        /// </summary>
        /// <param name="id">菜谱ID</param>
        /// <returns>新的收藏状态</returns>
        public async Task<bool> ToggleFavoriteAsync(int id)
        {
            try
            {
                var recipe = await _context.Recipes.FindAsync(id);
                if (recipe != null)
                {
                    recipe.IsFavorite = !recipe.IsFavorite;
                    recipe.UpdatedAt = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    
                    System.Diagnostics.Debug.WriteLine($"菜谱 '{recipe.Name}' 收藏状态已更新为: {recipe.IsFavorite}");
                    return recipe.IsFavorite;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"切换收藏状态失败 (ID: {id}): {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取存储统计信息
        /// Get storage statistics
        /// </summary>
        /// <returns>菜谱数量、数据库大小</returns>
        public async Task<(int RecipeCount, long StorageSize)> GetStorageStatsAsync()
        {
            try
            {
                var stats = await _context.GetDatabaseStatsAsync();
                
                System.Diagnostics.Debug.WriteLine($"存储统计: {stats.RecipeCount} 个菜谱, {stats.DatabaseSize / 1024.0:F2} KB");
                return (stats.RecipeCount, stats.DatabaseSize);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取存储统计失败: {ex.Message}");
                return (0, 0);
            }
        }

        /// <summary>
        /// 释放资源
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 受保护的释放方法
        /// Protected dispose method
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _context?.Dispose();
                System.Diagnostics.Debug.WriteLine("RecipeStorageService 资源已释放");
                _disposed = true;
            }
        }
    }
}
