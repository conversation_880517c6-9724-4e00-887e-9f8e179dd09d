// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 用户信息模型 / User Information Model
    /// 存储用户的基本信息和配置设置
    /// Stores user's basic information and configuration settings
    /// </summary>
    [Table("Users")]
    public class User
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 用户名 / Username
        /// 用户的唯一登录名称
        /// User's unique login name
        /// </summary>
        [DataAnnotations.Required, SQLite.MaxLength(50)]
        [Unique]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱地址 / Email Address
        /// 用户的电子邮件地址
        /// User's email address
        /// </summary>
        [SQLite.MaxLength(100)]
        [Indexed]
        public string? Email { get; set; }

        /// <summary>
        /// 显示名称 / Display Name
        /// 用户的显示名称或昵称
        /// User's display name or nickname
        /// </summary>
        [SQLite.MaxLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 头像URL / Avatar URL
        /// 用户头像图片的URL地址
        /// URL address of user's avatar image
        /// </summary>
        [SQLite.MaxLength(500)]
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 手机号码 / Phone Number
        /// 用户的联系电话号码
        /// User's contact phone number
        /// </summary>
        [SQLite.MaxLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 时区设置
        /// </summary>
        [SQLite.MaxLength(50)]
        public string TimeZone { get; set; } = "Australia/Sydney";

        /// <summary>
        /// 语言设置
        /// </summary>
        [SQLite.MaxLength(10)]
        public string Language { get; set; } = "en";

        /// <summary>
        /// 是否启用24小时制
        /// </summary>
        public bool Is24HourFormat { get; set; } = true;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
