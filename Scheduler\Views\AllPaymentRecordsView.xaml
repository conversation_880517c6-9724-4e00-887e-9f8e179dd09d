<?xml version="1.0" encoding="utf-8" ?>
<!--
    所有付款记录视图 / All Payment Records View
    显示所有付款记录的历史和统计信息，支持筛选和管理
    Display history and statistics of all payment records with filtering and management support
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             xmlns:converters="clr-namespace:Scheduler.Converters"
             x:Class="Scheduler.Views.AllPaymentRecordsView"
             x:DataType="viewmodels:AllPaymentRecordsViewModel"
             Title="{Binding LocalizedTexts.AllPaymentRecords}"
             BackgroundColor="#1E1E1E">

    <!-- 页面资源 / Page resources -->
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:IntToBoolConverter x:Key="IntToBoolConverter"/>
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid RowDefinitions="Auto,Auto,*,Auto" Padding="20">

        <!-- 顶部统计信息 / Top statistics information -->
        <Frame Grid.Row="0"
               BackgroundColor="#2196F3"
               HasShadow="True"
               CornerRadius="12"
               Padding="20"
               Margin="0,0,0,15">
            <Grid ColumnDefinitions="*,*,*">
                <!-- 总记录数 / Total records count -->
                <StackLayout Grid.Column="0">
                    <Label Text="{Binding LocalizedTexts.TotalRecords}"
                           TextColor="White"
                           FontSize="12"/>
                    <Label Text="{Binding TotalRecordsCount}"
                           TextColor="White"
                           FontSize="18"
                           FontAttributes="Bold"/>
                </StackLayout>
                <!-- 待确认记录数 / Pending confirmation records count -->
                <StackLayout Grid.Column="1">
                    <Label Text="{Binding LocalizedTexts.PendingConfirmation}"
                           TextColor="White"
                           FontSize="12"/>
                    <Label Text="{Binding PendingRecordsCount}"
                           TextColor="White"
                           FontSize="18"
                           FontAttributes="Bold"/>
                </StackLayout>
                <!-- 已确认记录数 / Confirmed records count -->
                <StackLayout Grid.Column="2">
                    <Label Text="{Binding LocalizedTexts.Confirmed}"
                           TextColor="White"
                           FontSize="12"/>
                    <Label Text="{Binding ConfirmedRecordsCount}"
                           TextColor="White"
                           FontSize="18"
                           FontAttributes="Bold"/>
                </StackLayout>
            </Grid>
        </Frame>

        <!-- 全选/取消全选控制 -->
        <Frame Grid.Row="1" 
               BackgroundColor="#424242" 
               HasShadow="True" 
               CornerRadius="8" 
               Padding="15" 
               Margin="0,0,0,15">
            <Grid ColumnDefinitions="Auto,*,Auto">
                <CheckBox Grid.Column="0" 
                          IsChecked="{Binding IsAllSelected}" 
                          Color="White"/>
                <Label Grid.Column="1"
                       Text="{Binding LocalizedTexts.SelectAllToggle}"
                       TextColor="White"
                       FontSize="14"
                       VerticalOptions="Center"
                       Margin="10,0,0,0"/>
                <Label Grid.Column="2"
                       TextColor="#FFC107"
                       FontSize="12"
                       VerticalOptions="Center">
                    <Label.FormattedText>
                        <FormattedString>
                            <Span Text="Selected: " />
                            <Span Text="{Binding SelectedRecordsCount}" />
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
            </Grid>
        </Frame>

        <!-- 支付记录列表 -->
        <RefreshView Grid.Row="2" 
                     IsRefreshing="{Binding IsRefreshing}" 
                     Command="{Binding RefreshCommand}">
            <CollectionView ItemsSource="{Binding PaymentRecords}" 
                            BackgroundColor="Transparent">
                <CollectionView.EmptyView>
                    <StackLayout HorizontalOptions="Center" VerticalOptions="Center">
                        <Label Text="{Binding LocalizedTexts.NoPaymentRecords}"
                               TextColor="Gray"
                               FontSize="16"
                               HorizontalOptions="Center"/>
                    </StackLayout>
                </CollectionView.EmptyView>
                
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:PaymentRecordWrapper">
                        <Frame BackgroundColor="{Binding BackgroundColor}"
                               HasShadow="True"
                               CornerRadius="12"
                               Padding="15"
                               Margin="0,5">
                            <Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto">

                                <!-- 复选框 -->
                                <CheckBox Grid.Column="0"
                                          Grid.RowSpan="3"
                                          IsChecked="{Binding IsSelected}"
                                          Color="White"
                                          VerticalOptions="Center"/>

                                <!-- 主要信息 -->
                                <StackLayout Grid.Column="1" Grid.Row="0" Margin="15,0,0,0">
                                    <Label Text="{Binding EmployerName}"
                                           TextColor="{Binding TextColor}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextDecorations="{Binding TextDecorations}"/>
                                    <Label TextColor="{Binding TextColor}"
                                           FontSize="12"
                                           TextDecorations="{Binding TextDecorations}">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span Text="Work Period: " />
                                                <Span Text="{Binding PaymentRecord.PeriodStart, StringFormat='{0:yyyy-MM-dd}'}" />
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                    <Label TextColor="{Binding TextColor}"
                                           FontSize="12"
                                           TextDecorations="{Binding TextDecorations}">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span Text="To: " />
                                                <Span Text="{Binding PaymentRecord.PeriodEnd, StringFormat='{0:yyyy-MM-dd}'}" />
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                                <!-- 金额信息 -->
                                <StackLayout Grid.Column="2" Grid.Row="0" HorizontalOptions="End">
                                    <Label Text="{Binding PaymentRecord.TotalAmount, StringFormat='${0:F2}'}"
                                           TextColor="{Binding AmountTextColor}"
                                           FontSize="18"
                                           FontAttributes="Bold"
                                           TextDecorations="{Binding TextDecorations}"/>
                                    <Label TextColor="{Binding TextColor}"
                                           FontSize="12"
                                           TextDecorations="{Binding TextDecorations}">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <Span Text="{Binding PaymentRecord.TotalHours, StringFormat='{0:F1}'}" />
                                                <Span Text=" hours" />
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                                <!-- 状态信息和操作按钮 -->
                                <Grid Grid.Column="1" Grid.ColumnSpan="2" Grid.Row="1" Margin="15,5,0,0" ColumnDefinitions="*,Auto">
                                    <StackLayout Grid.Column="0">
                                        <Label Text="{Binding StatusText}"
                                               TextColor="{Binding StatusTextColor}"
                                               FontSize="12"
                                               FontAttributes="Bold"/>
                                        <Label Text="{Binding ConfirmationTimeText}"
                                               TextColor="{Binding TextColor}"
                                               FontSize="11"
                                               IsVisible="{Binding IsConfirmed}"
                                               TextDecorations="{Binding TextDecorations}"/>
                                    </StackLayout>

                                    <!-- 单个取消确认按钮 -->
                                    <Button Grid.Column="1"
                                            Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AllPaymentRecordsViewModel}}, Path=LocalizedTexts.CancelConfirmation}"
                                            BackgroundColor="#FF5722"
                                            TextColor="White"
                                            CornerRadius="6"
                                            HeightRequest="32"
                                            WidthRequest="80"
                                            FontSize="11"
                                            IsVisible="{Binding IsConfirmed}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AllPaymentRecordsViewModel}}, Path=CancelSingleConfirmationCommand}"
                                            CommandParameter="{Binding .}"
                                            VerticalOptions="Center"/>
                                </Grid>

                            </Grid>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

        <!-- 底部操作按钮 -->
        <Frame Grid.Row="3" 
               BackgroundColor="#424242" 
               HasShadow="True" 
               CornerRadius="12" 
               Padding="15" 
               Margin="0,15,0,0" 
               IsVisible="{Binding HasSelectedConfirmedRecords}">
            <Grid ColumnDefinitions="*,Auto">
                <Label Grid.Column="0" 
                       Text="{Binding SelectedConfirmedRecordsText}" 
                       TextColor="White" 
                       FontSize="14" 
                       VerticalOptions="Center"/>
                <Button Grid.Column="1"
                        Text="{Binding LocalizedTexts.CancelConfirmation}"
                        BackgroundColor="#FF5722"
                        TextColor="White"
                        CornerRadius="8" 
                        HeightRequest="40" 
                        WidthRequest="100" 
                        Command="{Binding CancelConfirmationCommand}"/>
            </Grid>
        </Frame>
        
    </Grid>
</ContentPage>
