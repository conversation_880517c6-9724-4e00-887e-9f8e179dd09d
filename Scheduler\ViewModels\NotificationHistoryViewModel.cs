// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using static Scheduler.ViewModels.SetViewModel;  // 设置视图模型静态成员 / Settings view model static members

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 通知历史页面视图模型 / Notification History Page ViewModel
    /// 管理通知历史记录的显示和筛选功能
    /// Manages notification history records display and filtering functionality
    /// </summary>
    public partial class NotificationHistoryViewModel : ObservableObject
    {
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 本地化文本属性 / Localized texts property
        /// 提供界面显示的本地化文本 / Provides localized texts for UI display
        /// </summary>
        public LocalizedTexts LocalizedTexts { get; }

        // 通知记录集合 / Notification records collection
        [ObservableProperty]
        private ObservableCollection<NotificationRecord> _notifications = new();

        // 加载状态 / Loading state
        [ObservableProperty]
        private bool _isLoading;

        // 状态消息 / Status message
        [ObservableProperty]
        private string _statusMessage = string.Empty;

        // 选中的通知记录 / Selected notification record
        [ObservableProperty]
        private NotificationRecord? _selectedNotification;

        // 搜索文本 / Search text
        [ObservableProperty]
        private string _searchText = string.Empty;

        // 选中的通知类型 / Selected notification type
        [ObservableProperty]
        private NotificationType? _selectedType;

        // 选中的通知状态 / Selected notification status
        [ObservableProperty]
        private NotificationStatus? _selectedStatus;

        // 开始日期 / Start date
        [ObservableProperty]
        private DateTime _startDate = DateTime.Today.AddDays(-30);

        // 结束日期 / End date
        [ObservableProperty]
        private DateTime _endDate = DateTime.Today.AddDays(1);

        // 新增：已启用通知的班次列表 (New: List of shifts with enabled notifications)
        [ObservableProperty]
        private ObservableCollection<Shift> _shiftsWithNotifications = new();

        // 新增：是否显示班次通知管理区域 (New: Whether to show shift notification management area)
        [ObservableProperty]
        private bool _isShiftNotificationSectionVisible = true;

        public NotificationHistoryViewModel(
            INotificationService notificationService,
            DatabaseService databaseService,
            LocalizationService localizationService)
        {
            _notificationService = notificationService;
            _databaseService = databaseService;
            _localizationService = localizationService;
            LocalizedTexts = new LocalizedTexts(_localizationService);
        }

        /// <summary>
        /// 页面出现时加载通知历史
        /// </summary>
        [RelayCommand]
        private async Task LoadNotificationsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.LoadingNotificationHistory;

                var notifications = await _notificationService.GetNotificationHistoryAsync();
                
                // 应用筛选条件
                var filteredNotifications = ApplyFilters(notifications);
                
                Notifications.Clear();
                foreach (var notification in filteredNotifications.OrderByDescending(n => n.ScheduledAt))
                {
                    Notifications.Add(notification);
                }

                StatusMessage = string.Format(LocalizedTexts.LoadedNotificationCount, Notifications.Count);
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.LoadNotificationHistoryFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"加载通知历史失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 加载已启用通知的班次列表 (Load shifts with enabled notifications)
        /// </summary>
        [RelayCommand]
        private async Task LoadShiftsWithNotificationsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.LoadingShiftsWithNotifications;

                // 获取未来7天内已启用通知的班次 (Get shifts with enabled notifications in the next 7 days)
                var startDate = DateTime.Today;
                var endDate = startDate.AddDays(7);
                var allShifts = await _databaseService.GetShiftsByDateRangeAsync(startDate, endDate);

                // 筛选出已启用通知的班次 (Filter shifts with enabled notifications)
                var shiftsWithNotifications = allShifts
                    .Where(s => s.IsReminderEnabled && s.Status == ShiftStatus.Scheduled && s.StartTime > DateTime.Now)
                    .OrderBy(s => s.StartTime)
                    .ToList();

                // 加载雇主信息 (Load employer information)
                foreach (var shift in shiftsWithNotifications)
                {
                    var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
                    if (employer != null)
                    {
                        shift.EmployerName = employer.Name;
                    }
                }

                ShiftsWithNotifications.Clear();
                foreach (var shift in shiftsWithNotifications)
                {
                    ShiftsWithNotifications.Add(shift);
                }

                StatusMessage = string.Format(LocalizedTexts.FoundShiftsWithNotifications, ShiftsWithNotifications.Count);
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.LoadShiftNotificationsFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"加载班次通知失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 取消单个班次的通知设置 (Cancel notification for a single shift)
        /// </summary>
        [RelayCommand]
        private async Task CancelShiftNotificationAsync(Shift shift)
        {
            try
            {
                if (shift == null) return;

                // 更新班次的通知设置 (Update shift notification settings)
                shift.IsReminderEnabled = false;
                shift.ReminderMinutes = 0;
                shift.NotificationId = null;

                await _databaseService.SaveShiftAsync(shift);

                // 取消相关通知 (Cancel related notifications)
                await _notificationService.CancelShiftNotificationsAsync(shift.Id);

                // 从列表中移除 (Remove from list)
                ShiftsWithNotifications.Remove(shift);

                StatusMessage = string.Format(LocalizedTexts.ShiftNotificationCancelled, shift.EmployerName, shift.StartTime);
                System.Diagnostics.Debug.WriteLine($"已取消班次 {shift.Id} 的通知设置");
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.CancelShiftNotificationFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"取消班次通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送测试通知 (Send test notification)
        /// </summary>
        [RelayCommand]
        private async Task SendTestNotificationAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.SendingTestNotification;

                // 检查通知权限 (Check notification permission)
                var hasPermission = await _notificationService.CheckNotificationPermissionAsync();
                if (!hasPermission)
                {
                    var permissionGranted = await _notificationService.RequestNotificationPermissionAsync();
                    if (!permissionGranted)
                    {
                        StatusMessage = LocalizedTexts.TestNotificationFailedNoPermission;
                        return;
                    }
                }

                // 发送测试通知 (Send test notification)
                var testNotification = new Plugin.LocalNotification.NotificationRequest
                {
                    NotificationId = 999999,
                    Title = LocalizedTexts.TestNotificationTitle,
                    Description = LocalizedTexts.TestNotificationDescription,
                    Schedule = new Plugin.LocalNotification.NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now.AddSeconds(3)
                    }
                };

                await Plugin.LocalNotification.LocalNotificationCenter.Current.Show(testNotification);

                // 记录测试通知 (Record test notification)
                await SaveTestNotificationRecordAsync();

                StatusMessage = LocalizedTexts.TestNotificationSent;
                System.Diagnostics.Debug.WriteLine("测试通知已发送");
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.SendTestNotificationFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"发送测试通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索通知
        /// </summary>
        [RelayCommand]
        private async Task SearchNotificationsAsync()
        {
            await LoadNotificationsAsync();
        }

        /// <summary>
        /// 清除筛选条件
        /// </summary>
        [RelayCommand]
        private async Task ClearFiltersAsync()
        {
            SearchText = string.Empty;
            SelectedType = null;
            SelectedStatus = null;
            StartDate = DateTime.Today.AddDays(-30);
            EndDate = DateTime.Today.AddDays(1);
            
            await LoadNotificationsAsync();
        }

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        [RelayCommand]
        private async Task MarkAsReadAsync(NotificationRecord notification)
        {
            try
            {
                if (notification.Status != NotificationStatus.Read)
                {
                    notification.Status = NotificationStatus.Read;
                    notification.ReadAt = DateTime.Now;
                    
                    await _databaseService.SaveNotificationRecordAsync(notification);
                    StatusMessage = LocalizedTexts.NotificationMarkedAsRead;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.MarkNotificationFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"标记通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除通知记录
        /// </summary>
        [RelayCommand]
        private async Task DeleteNotificationAsync(NotificationRecord notification)
        {
            try
            {
                var success = await _databaseService.DeleteNotificationRecordAsync(notification.Id);
                if (success)
                {
                    Notifications.Remove(notification);
                    StatusMessage = LocalizedTexts.NotificationRecordDeleted;
                }
                else
                {
                    StatusMessage = LocalizedTexts.DeleteNotificationRecordFailed;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"删除通知记录失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"删除通知记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除所有通知记录
        /// </summary>
        [RelayCommand]
        private async Task ClearAllNotificationsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.ClearingAllNotificationRecords;

                await _notificationService.ClearNotificationHistoryAsync();
                Notifications.Clear();

                StatusMessage = LocalizedTexts.AllNotificationRecordsCleared;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.ClearNotificationRecordsFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"清除通知记录失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 重新发送通知
        /// </summary>
        [RelayCommand]
        private async Task ResendNotificationAsync(NotificationRecord notification)
        {
            try
            {
                StatusMessage = LocalizedTexts.ResendingNotification;

                // 根据通知类型重新发送
                switch (notification.Type)
                {
                    case NotificationType.BreakReminder:
                        await _notificationService.SendBreakReminderAsync(notification.Message);
                        break;
                    default:
                        // 发送通用通知
                        await _notificationService.SendBreakReminderAsync(string.Format(LocalizedTexts.ResendPrefix, notification.Title) + "\n" + notification.Message);
                        break;
                }

                StatusMessage = LocalizedTexts.NotificationResent;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.ResendNotificationFailed, ex.Message);
                System.Diagnostics.Debug.WriteLine($"重新发送通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用筛选条件
        /// </summary>
        private List<NotificationRecord> ApplyFilters(List<NotificationRecord> notifications)
        {
            var filtered = notifications.AsEnumerable();

            // 按文本搜索
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(n => 
                    n.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    n.Message.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
            }

            // 按类型筛选
            if (SelectedType.HasValue)
            {
                filtered = filtered.Where(n => n.Type == SelectedType.Value);
            }

            // 按状态筛选
            if (SelectedStatus.HasValue)
            {
                filtered = filtered.Where(n => n.Status == SelectedStatus.Value);
            }

            // 按日期范围筛选
            filtered = filtered.Where(n => 
                n.ScheduledAt >= StartDate && 
                n.ScheduledAt <= EndDate);

            return filtered.ToList();
        }

        /// <summary>
        /// 清除状态消息
        /// </summary>
        [RelayCommand]
        private void ClearStatusMessage()
        {
            StatusMessage = string.Empty;
        }

        /// <summary>
        /// 通知类型选项
        /// </summary>
        public List<NotificationType?> NotificationTypeOptions { get; } = new()
        {
            null,
            NotificationType.WorkReminder,
            NotificationType.BreakReminder,
            NotificationType.PaymentDue,
            NotificationType.ShiftConflict,
            NotificationType.SystemUpdate
        };

        /// <summary>
        /// 通知状态选项
        /// </summary>
        public List<NotificationStatus?> NotificationStatusOptions { get; } = new()
        {
            null,
            NotificationStatus.Pending,
            NotificationStatus.Sent,
            NotificationStatus.Read,
            NotificationStatus.Dismissed
        };

        /// <summary>
        /// 获取通知类型显示文本
        /// </summary>
        public string GetNotificationTypeDisplay(NotificationType? type)
        {
            return type switch
            {
                null => LocalizedTexts.AllTypes,
                NotificationType.WorkReminder => LocalizedTexts.WorkReminder,
                NotificationType.BreakReminder => LocalizedTexts.BreakReminder,
                NotificationType.PaymentDue => LocalizedTexts.PaymentDue,
                NotificationType.ShiftConflict => LocalizedTexts.ShiftConflict,
                NotificationType.SystemUpdate => LocalizedTexts.SystemUpdate,
                _ => LocalizedTexts.UnknownType
            };
        }

        /// <summary>
        /// 获取通知状态显示文本
        /// </summary>
        public string GetNotificationStatusDisplay(NotificationStatus? status)
        {
            return status switch
            {
                null => LocalizedTexts.AllStatuses,
                NotificationStatus.Pending => LocalizedTexts.Pending,
                NotificationStatus.Sent => LocalizedTexts.Sent,
                NotificationStatus.Read => LocalizedTexts.Read,
                NotificationStatus.Dismissed => LocalizedTexts.Dismissed,
                _ => LocalizedTexts.UnknownStatus
            };
        }

        /// <summary>
        /// 保存测试通知记录 (Save test notification record)
        /// </summary>
        private async Task SaveTestNotificationRecordAsync()
        {
            try
            {
                var testRecord = new NotificationRecord
                {
                    Type = NotificationType.SystemUpdate,
                    Title = LocalizedTexts.TestNotificationTitle,
                    Message = LocalizedTexts.TestNotificationMessage,
                    Status = NotificationStatus.Sent,
                    ScheduledAt = DateTime.Now.AddSeconds(3),
                    SentAt = DateTime.Now,
                    RelatedEntityType = "TestNotification",
                    CreatedAt = DateTime.Now
                };

                await _databaseService.SaveNotificationRecordAsync(testRecord);
                System.Diagnostics.Debug.WriteLine("测试通知记录已保存");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存测试通知记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新所有数据 (Refresh all data)
        /// </summary>
        [RelayCommand]
        private async Task RefreshAllDataAsync()
        {
            await LoadNotificationsCommand.ExecuteAsync(null);
            await LoadShiftsWithNotificationsCommand.ExecuteAsync(null);
        }


    }
}
