// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 工作日志视图 / Work Log View
/// 显示工作时间记录、班次统计和工作历史
/// Display work time records, shift statistics and work history
/// </summary>
public partial class LogView : ContentPage
{
    // 工作日志视图模型实例 / Work log view model instance
    private readonly LogViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化工作日志视图并配置数据绑定 / Initialize work log view and configure data binding
    /// </summary>
    /// <param name="viewModel">工作日志视图模型 / Work log view model</param>
    public LogView(LogViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载工作日志数据和统计信息 / Load work log data and statistics
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        // 执行视图模型的出现逻辑 / Execute view model appearing logic
        await _viewModel.OnAppearingAsync();
    }
}