// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 工作记录模型 / Work Record Model
    /// 记录实际的工作时间和相关信息
    /// Records actual work time and related information
    /// </summary>
    [Table("WorkRecords")]
    public class WorkRecord
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 班次ID / Shift ID
        /// 关联的班次标识
        /// Associated shift identifier
        /// </summary>
        [DataAnnotations.Required]
        [Indexed]
        public int ShiftId { get; set; }

        /// <summary>
        /// 打卡上班时间 / Clock-in Time
        /// 实际开始工作的时间
        /// Actual time when work started
        /// </summary>
        [Indexed]
        public DateTime? ClockInTime { get; set; }

        /// <summary>
        /// 打卡下班时间 / Clock-out Time
        /// 实际结束工作的时间
        /// Actual time when work ended
        /// </summary>
        public DateTime? ClockOutTime { get; set; }

        /// <summary>
        /// 实际工作小时数 / Actual Work Hours
        /// 实际工作的总时长
        /// Total duration of actual work
        /// </summary>
        public double? ActualHours { get; set; }

        /// <summary>
        /// 实际工作地点 / Actual Work Location
        /// 实际工作的地点位置
        /// Actual location where work was performed
        /// </summary>
        [SQLite.MaxLength(200)]
        public string? ActualLocation { get; set; }

        /// <summary>
        /// 工作备注 / Work Notes
        /// 关于工作内容的备注说明
        /// Notes about work content
        /// </summary>
        [SQLite.MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Tasks completed
        /// </summary>
        [SQLite.MaxLength(1000)]
        public string? TasksCompleted { get; set; }

        /// <summary>
        /// Work quality rating (1-5)
        /// </summary>
        public int? QualityRating { get; set; }

        /// <summary>
        /// Whether arrived on time
        /// </summary>
        public bool IsOnTime { get; set; } = true;

        /// <summary>
        /// Late arrival minutes
        /// </summary>
        public int LateMinutes { get; set; } = 0;

        /// <summary>
        /// Early departure minutes
        /// </summary>
        public int EarlyLeaveMinutes { get; set; } = 0;

        /// <summary>
        /// Break time in minutes
        /// </summary>
        public int BreakMinutes { get; set; } = 0;

        /// <summary>
        /// Overtime minutes
        /// </summary>
        public int OvertimeMinutes { get; set; } = 0;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Calculate actual work hours based on clock times
        /// </summary>
        [Ignore]
        public double? CalculatedHours
        {
            get
            {
                if (ClockInTime.HasValue && ClockOutTime.HasValue)
                {
                    var totalMinutes = (ClockOutTime.Value - ClockInTime.Value).TotalMinutes;
                    var workMinutes = totalMinutes - BreakMinutes;
                    return Math.Max(0, workMinutes / 60.0);
                }
                return null;
            }
        }

        /// <summary>
        /// Get final work hours (prioritizes manual input, then calculated value)
        /// </summary>
        [Ignore]
        public double FinalHours => ActualHours ?? CalculatedHours ?? 0;

        /// <summary>
        /// Whether the work record is completed
        /// </summary>
        [Ignore]
        public bool IsCompleted => ClockOutTime.HasValue;
    }
}
