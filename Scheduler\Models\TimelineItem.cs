// 引入必要的命名空间 / Import necessary namespaces
using System.Collections.ObjectModel; // 可观察集合 / Observable collection

namespace Scheduler.Models
{
    /// <summary>
    /// 班次显示块 / Shift Block
    /// 表示班次在时间线上的显示块
    /// Represents a shift display block on the timeline
    /// </summary>
    public class ShiftBlock
    {
        /// <summary>班次对象 / Shift Object</summary>
        public Shift Shift { get; set; } = new();
        /// <summary>开始时间 / Start Time</summary>
        public DateTime StartTime => Shift.StartTime;
        /// <summary>结束时间 / End Time</summary>
        public DateTime EndTime => Shift.EndTime;
        /// <summary>描述 / Description</summary>
        public string Description => Shift.Description ?? "";
        /// <summary>地点 / Location</summary>
        public string Location => Shift.Location ?? "";
        /// <summary>雇主名称 / Employer Name</summary>
        public string EmployerName => Shift.EmployerName ?? "";
        /// <summary>状态颜色 / Status Color</summary>
        public string StatusColor => Shift.StatusColor;
        /// <summary>分配的小时 / Assigned Hour</summary>
        public int AssignedHour { get; set; }

        /// <summary>
        /// 是否是班次的起始小时 / Is Start Hour
        /// 用于控制是否显示详细信息
        /// Used to control whether to display detailed information
        /// </summary>
        public bool IsStartHour => StartTime.Hour == AssignedHour;

        /// <summary>
        /// 是否是班次的结束小时 / Is End Hour
        /// 判断当前小时是否为班次结束小时
        /// Determines if the current hour is the shift end hour
        /// </summary>
        public bool IsEndHour => EndTime.Hour == AssignedHour ||
                                (EndTime.Minute == 0 && EndTime.Hour - 1 == AssignedHour);

        /// <summary>
        /// 是否是班次的中间小时 / Is Middle Hour
        /// 判断当前小时是否为班次的中间小时
        /// Determines if the current hour is a middle hour of the shift
        /// </summary>
        public bool IsMiddleHour => !IsStartHour && !IsEndHour &&
                                   AssignedHour > StartTime.Hour &&
                                   AssignedHour < EndTime.Hour;

        /// <summary>
        /// 在当前小时的高度比例（0.0 - 1.0）
        /// </summary>
        public double HeightRatio
        {
            get
            {
                if (IsMiddleHour) return 1.0; // 中间小时占满整个高度

                var hourStart = new DateTime(StartTime.Year, StartTime.Month, StartTime.Day, AssignedHour, 0, 0);
                var hourEnd = hourStart.AddHours(1);

                var blockStart = StartTime > hourStart ? StartTime : hourStart;
                var blockEnd = EndTime < hourEnd ? EndTime : hourEnd;

                var totalMinutes = (blockEnd - blockStart).TotalMinutes;
                return Math.Max(0.1, totalMinutes / 60.0); // 最小高度10%
            }
        }

        /// <summary>
        /// 在当前小时的垂直偏移比例（0.0 - 1.0）
        /// </summary>
        public double VerticalOffset
        {
            get
            {
                if (IsMiddleHour) return 0.0; // 中间小时从顶部开始

                var hourStart = new DateTime(StartTime.Year, StartTime.Month, StartTime.Day, AssignedHour, 0, 0);
                var blockStart = StartTime > hourStart ? StartTime : hourStart;

                var offsetMinutes = (blockStart - hourStart).TotalMinutes;
                return offsetMinutes / 60.0;
            }
        }

        /// <summary>
        /// 时间显示文本
        /// </summary>
        public string TimeText => $"{StartTime:HH:mm} - {EndTime:HH:mm}";

        /// <summary>
        /// 持续时间文本
        /// </summary>
        public string DurationText
        {
            get
            {
                var duration = EndTime - StartTime;
                if (duration.TotalHours >= 1)
                {
                    return $"{duration.TotalHours:F1}小时";
                }
                else
                {
                    return $"{duration.TotalMinutes:F0}分钟";
                }
            }
        }

        public ShiftBlock(Shift shift, int assignedHour)
        {
            Shift = shift;
            AssignedHour = assignedHour;
        }
    }

    /// <summary>
    /// Represents an hour slot in the day timeline
    /// </summary>
    public class TimelineItem
    {
        public int Hour { get; set; }
        public string TimeLabel { get; set; } = string.Empty;
        public ObservableCollection<ShiftBlock> ShiftBlocks { get; set; } = new();
        public bool IsCurrentHour { get; set; }
        public bool HasShifts => ShiftBlocks.Count > 0;

        /// <summary>
        /// 是否有冲突的班次
        /// </summary>
        public bool HasConflicts { get; set; }

        /// <summary>
        /// 冲突指示器颜色
        /// </summary>
        public string ConflictColor => HasConflicts ? "#EF4444" : "Transparent";

        public TimelineItem(int hour)
        {
            Hour = hour;
            TimeLabel = hour == 0 ? "12:00" : $"{hour:D2}:00";
            IsCurrentHour = DateTime.Now.Hour == hour;
        }

        /// <summary>
        /// 添加班次块
        /// </summary>
        public void AddShiftBlock(Shift shift)
        {
            var shiftBlock = new ShiftBlock(shift, Hour);
            ShiftBlocks.Add(shiftBlock);

            // 检查是否有冲突
            CheckForConflicts();
        }

        /// <summary>
        /// 检查当前小时是否有班次冲突
        /// </summary>
        private void CheckForConflicts()
        {
            HasConflicts = false;

            for (int i = 0; i < ShiftBlocks.Count; i++)
            {
                for (int j = i + 1; j < ShiftBlocks.Count; j++)
                {
                    if (ShiftBlocks[i].Shift.ConflictsWith(ShiftBlocks[j].Shift))
                    {
                        HasConflicts = true;
                        return;
                    }
                }
            }
        }
    }
}
