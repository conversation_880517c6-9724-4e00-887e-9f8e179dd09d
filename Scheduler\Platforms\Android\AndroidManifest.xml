﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="Scheduler.app" android:versionCode="1" android:versionName="test">
	<application android:allowBackup="true" android:icon="@mipmap/appicon" android:supportsRtl="true" android:label="Scheduler"></application>

	<!-- 网络权限 -->
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />

	<!-- 存储权限 -->
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

	<!-- 通知权限 -->
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

	<!-- 闹钟和精确定时权限 -->
	<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
	<uses-permission android:name="android.permission.USE_EXACT_ALARM" />
</manifest>