namespace Scheduler.Services
{
    /// <summary>
    /// 应用初始化服务接口 / Application Initialization Service Interface
    /// 定义应用程序初始化的标准契约
    /// Defines standard contract for application initialization
    /// </summary>
    public interface IAppInitializationService
    {
        /// <summary>初始化应用程序 / Initialize application</summary>
        Task InitializeAsync();
        /// <summary>是否已初始化 / Whether initialized</summary>
        bool IsInitialized { get; }
    }

    /// <summary>
    /// 应用初始化服务 / Application Initialization Service
    /// 负责应用启动时的初始化工作和服务配置
    /// Responsible for initialization work and service configuration during application startup
    /// </summary>
    public class AppInitializationService : IAppInitializationService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 通知设置服务实例 / Notification settings service instance
        private readonly INotificationSettingsService _notificationSettingsService;
        // 初始化状态标志 / Initialization status flag
        private bool _isInitialized = false;

        /// <summary>
        /// 是否已初始化 / Whether initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化应用初始化服务 / Initialize application initialization service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="notificationScheduler">通知调度器 / Notification scheduler</param>
        /// <param name="notificationSettingsService">通知设置服务 / Notification settings service</param>
        public AppInitializationService(
            DatabaseService databaseService,
            NotificationScheduler notificationScheduler,
            INotificationSettingsService notificationSettingsService)
        {
            _databaseService = databaseService;
            _notificationScheduler = notificationScheduler;
            _notificationSettingsService = notificationSettingsService;
        }

        /// <summary>
        /// 初始化应用
        /// </summary>
        public async Task InitializeAsync()
        {
            if (_isInitialized)
                return;

            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化应用...");

                // 1. 初始化数据库
                await InitializeDatabaseAsync();

                // 2. 初始化通知设置
                await InitializeNotificationSettingsAsync();

                // 3. 初始化通知调度器
                await InitializeNotificationSchedulerAsync();

                // 4. 执行数据库迁移和清理
                await PerformMaintenanceTasksAsync();

                _isInitialized = true;
                System.Diagnostics.Debug.WriteLine("应用初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用初始化失败: {ex.Message}");
                // 即使初始化失败，也标记为已初始化，避免重复尝试
                _isInitialized = true;
                throw;
            }
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("初始化数据库...");

                // 检查并清理测试数据（生产版本）
                var hasTestData = await _databaseService.HasTestDataAsync();
                if (hasTestData)
                {
                    System.Diagnostics.Debug.WriteLine("检测到测试数据，正在清理...");
                    await _databaseService.ClearAllTestDataAsync();
                    System.Diagnostics.Debug.WriteLine("测试数据清理完成");
                }

                // 确保数据库连接正常（不会创建测试数据）
                await _databaseService.GetEmployersAsync();

                System.Diagnostics.Debug.WriteLine("数据库初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化通知设置
        /// </summary>
        private async Task InitializeNotificationSettingsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("初始化通知设置...");
                
                // 确保通知设置已加载
                var settings = await _notificationSettingsService.GetSettingsAsync();
                
                System.Diagnostics.Debug.WriteLine($"通知设置初始化完成 - 班次提醒: {settings.IsShiftReminderEnabled}, 每日安排: {settings.IsDailyScheduleEnabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通知设置初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化通知调度器
        /// </summary>
        private async Task InitializeNotificationSchedulerAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("初始化通知调度器...");
                
                // 初始化通知调度器
                await _notificationScheduler.InitializeAsync();
                
                System.Diagnostics.Debug.WriteLine("通知调度器初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通知调度器初始化失败: {ex.Message}");
                // 通知初始化失败不应该阻止应用启动
                // 只记录错误但不抛出异常
            }
        }

        /// <summary>
        /// 执行维护任务
        /// </summary>
        private async Task PerformMaintenanceTasksAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("执行维护任务...");
                
                // 清理过期通知
                await _notificationScheduler.CleanupExpiredNotificationsAsync();
                
                // 可以在这里添加其他维护任务，如：
                // - 清理过期的工作记录
                // - 数据库优化
                // - 缓存清理等
                
                System.Diagnostics.Debug.WriteLine("维护任务完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"维护任务执行失败: {ex.Message}");
                // 维护任务失败不应该阻止应用启动
            }
        }

        /// <summary>
        /// 重新初始化通知系统
        /// </summary>
        public async Task ReinitializeNotificationsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("重新初始化通知系统...");
                
                await _notificationScheduler.RescheduleAllNotificationsAsync();
                
                System.Diagnostics.Debug.WriteLine("通知系统重新初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通知系统重新初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查应用健康状态
        /// </summary>
        public async Task<bool> CheckHealthAsync()
        {
            try
            {
                // 检查数据库连接
                await _databaseService.GetEmployersAsync();
                
                // 检查通知设置
                await _notificationSettingsService.GetSettingsAsync();
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"健康检查失败: {ex.Message}");
                return false;
            }
        }
    }
}
