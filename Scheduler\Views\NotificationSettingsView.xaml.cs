// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 通知设置视图 / Notification Settings View
/// 用于配置应用程序通知偏好和提醒设置
/// Used to configure application notification preferences and reminder settings
/// </summary>
public partial class NotificationSettingsView : ContentPage
{
    // 通知设置视图模型实例 / Notification settings view model instance
    private readonly NotificationSettingsViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化通知设置视图并配置数据绑定 / Initialize notification settings view and configure data binding
    /// </summary>
    /// <param name="viewModel">通知设置视图模型 / Notification settings view model</param>
    public NotificationSettingsView(NotificationSettingsViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载通知设置和权限状态 / Load notification settings and permission status
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        System.Diagnostics.Debug.WriteLine("NotificationSettingsView: 页面出现，开始加载设置 / Page appearing, starting to load settings");

        // 加载通知设置 / Load notification settings
        if (_viewModel != null)
        {
            await _viewModel.LoadSettingsCommand.ExecuteAsync(null);
        }
    }
}
