// 引入必要的命名空间 / Import necessary namespaces
using System.Collections.ObjectModel; // 可观察集合 / Observable collection

namespace Scheduler.Models
{
    /// <summary>
    /// 日历日期模型 / Calendar Day Model
    /// 表示日历中的一天及其包含的班次
    /// Represents a day in the calendar with its shifts
    /// </summary>
    public class CalendarDay
    {
        /// <summary>
        /// 日期 / Date
        /// 当前日历日的具体日期
        /// Specific date of the current calendar day
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 班次集合 / Shifts Collection
        /// 该日期包含的所有班次
        /// All shifts contained in this date
        /// </summary>
        public ObservableCollection<Shift> Shifts { get; set; } = new();

        /// <summary>
        /// 是否为当前月 / Is Current Month
        /// 标识该日期是否属于当前显示的月份
        /// Indicates whether this date belongs to the currently displayed month
        /// </summary>
        public bool IsCurrentMonth { get; set; }

        /// <summary>
        /// 是否为今天 / Is Today
        /// 判断该日期是否为今天
        /// Determines if this date is today
        /// </summary>
        public bool IsToday => Date.Date == DateTime.Today;

        /// <summary>
        /// 是否有班次 / Has Shifts
        /// 判断该日期是否包含班次
        /// Determines if this date contains shifts
        /// </summary>
        public bool HasShifts => Shifts.Count > 0;

        /// <summary>
        /// 是否有冲突 / Has Conflicts
        /// 标识该日期的班次是否存在时间冲突
        /// Indicates whether shifts on this date have time conflicts
        /// </summary>
        public bool HasConflicts { get; set; }

        /// <summary>
        /// 日期数字 / Day Number
        /// 获取日期的天数部分
        /// Gets the day part of the date
        /// </summary>
        public string DayNumber => Date.Day.ToString();

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化日历日期对象
        /// Initialize calendar day object
        /// </summary>
        /// <param name="date">日期 / Date</param>
        /// <param name="isCurrentMonth">是否为当前月 / Is current month</param>
        public CalendarDay(DateTime date, bool isCurrentMonth = true)
        {
            Date = date;
            IsCurrentMonth = isCurrentMonth;
        }
    }
}
