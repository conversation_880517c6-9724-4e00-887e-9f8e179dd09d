﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>Scheduler</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Scheduler</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.scheduler</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>

		<!-- XAML预览器配置 -->
		<EnableXamlPreviewer>true</EnableXamlPreviewer>
		<XamlDesignTimeTargetFramework Condition="$([MSBuild]::IsOSPlatform('windows'))">net9.0-windows10.0.19041.0</XamlDesignTimeTargetFramework>
		<DesignTimeTargetFramework Condition="$([MSBuild]::IsOSPlatform('windows'))">net9.0-windows10.0.19041.0</DesignTimeTargetFramework>

		<!-- 设计时构建配置 -->
		<SkipValidatePackageReferences Condition="'$(DesignTimeBuild)' == 'true'">true</SkipValidatePackageReferences>
		<GenerateAssemblyInfo Condition="'$(DesignTimeBuild)' == 'true'">false</GenerateAssemblyInfo>

		<!-- XAML预览器专用配置 - 避免Android程序集冲突 -->
		<SkipAndroidResourceGeneration Condition="'$(DesignTimeBuild)' == 'true'">true</SkipAndroidResourceGeneration>
		<AndroidGenerateJavaStubs Condition="'$(DesignTimeBuild)' == 'true'">false</AndroidGenerateJavaStubs>
		<AndroidGenerateLayoutBindings Condition="'$(DesignTimeBuild)' == 'true'">false</AndroidGenerateLayoutBindings>

		<!-- 设计时禁用平台特定代码 -->
		<DefineConstants Condition="'$(DesignTimeBuild)' == 'true'">$(DefineConstants);DESIGN</DefineConstants>
		<NoWarn Condition="'$(DesignTimeBuild)' == 'true'">$(NoWarn);CA1416</NoWarn>

		<!-- Android 特定配置 - 简化配置以避免类生成问题 -->
		<AndroidUseAapt2 Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">true</AndroidUseAapt2>
		<AndroidLinkMode Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">SdkOnly</AndroidLinkMode>
	</PropertyGroup>



	<!-- 导入XAML预览器专用配置 -->
	<Import Project="XamlPreviewer.targets" Condition="Exists('XamlPreviewer.targets')" />

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- TabBar Icons with specific sizing -->
		<MauiImage Update="Resources\Images\homeicon.png" Resize="True" BaseSize="24,24" />
		<MauiImage Update="Resources\Images\calendaricon.png" Resize="True" BaseSize="24,24" />
		<MauiImage Update="Resources\Images\logicon.png" Resize="True" BaseSize="24,24" />
		<MauiImage Update="Resources\Images\payicon.png" Resize="True" BaseSize="24,24" />
		<MauiImage Update="Resources\Images\seticon.png" Resize="True" BaseSize="24,24" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Maui" Version="9.1.0" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.21" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.21" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="Plugin.LocalNotification" Version="12.0.1" />
		<PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.10" />
	</ItemGroup>



	<ItemGroup>
	  <MauiXaml Update="MainPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="SalaryView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\CalendarView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\Dialog\EmployerView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\HomeView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\LogView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\PayView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\SetView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\LoginView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\SimpleShiftView.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<!-- 导入XAML预览器配置 -->
	<Import Project="XamlPreviewer.props" Condition="Exists('XamlPreviewer.props')" />

</Project>
