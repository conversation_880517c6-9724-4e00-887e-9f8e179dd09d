using SQLite;
using Scheduler.Models;
using System.Text;

namespace Scheduler.Services
{
    /// <summary>
    /// 数据库服务类 - 提供SQLite数据库的完整操作接口
    /// Database service class - Provides complete SQLite database operation interface
    /// </summary>
    /// <remarks>
    /// 该服务负责管理应用程序的所有数据库操作，包括：
    /// - 数据库连接和初始化
    /// - 雇主信息的CRUD操作
    /// - 班次记录的管理
    /// - 工作记录和薪资记录的处理
    /// - 数据库迁移和版本管理
    /// </remarks>
    public class DatabaseService
    {
        private SQLiteAsyncConnection? _database;
        private readonly string _databasePath;
        private DatabaseMigrationService? _migrationService;

        /// <summary>
        /// 初始化数据库服务实例
        /// Initializes a new instance of the DatabaseService class
        /// </summary>
        /// <remarks>
        /// 构造函数会设置数据库文件路径到应用程序数据目录
        /// </remarks>
        public DatabaseService()
        {
            _databasePath = Path.Combine(FileSystem.AppDataDirectory, "scheduler.db");
        }

        /// <summary>
        /// 初始化数据库连接和表结构
        /// Initialize database connection and table structure
        /// </summary>
        /// <returns>异步任务</returns>
        /// <remarks>
        /// 该方法执行以下操作：
        /// 1. 创建SQLite异步连接
        /// 2. 初始化数据库迁移服务
        /// 3. 创建所有必要的数据表
        /// 4. 执行数据库架构迁移
        /// 5. 启用外键约束以确保数据完整性
        /// </remarks>
        private async Task InitAsync()
        {
            if (_database is not null)
                return;

            _database = new SQLiteAsyncConnection(_databasePath);

            // 创建迁移服务
            _migrationService = new DatabaseMigrationService(_database);

            // 创建基础表
            await _database.CreateTableAsync<Employer>();
            await _database.CreateTableAsync<Shift>();
            await _database.CreateTableAsync<WorkRecord>();
            await _database.CreateTableAsync<PaymentRecord>();
            await _database.CreateTableAsync<TodoItem>();

            // 创建新增表
            await _database.CreateTableAsync<User>();
            await _database.CreateTableAsync<AppSettings>();
            await _database.CreateTableAsync<NotificationRecord>();
            await _database.CreateTableAsync<NotificationSettings>();
            await _database.CreateTableAsync<AuditLog>();

            // 执行数据库迁移
            await _migrationService.MigrateAsync();

            // 启用外键约束
            await _migrationService.CreateForeignKeyConstraintsAsync();

            // 验证数据完整性
            var isValid = await _migrationService.ValidateDataIntegrityAsync();
            if (!isValid)
            {
                System.Diagnostics.Debug.WriteLine("数据完整性验证失败，请检查数据库");
            }
        }

        #region Employer Operations - 雇主信息管理操作

        /// <summary>
        /// 获取所有活跃的雇主信息
        /// Get all active employers
        /// </summary>
        /// <returns>活跃雇主列表，按名称排序</returns>
        /// <remarks>
        /// 该方法只返回IsActive为true的雇主记录，
        /// 结果按雇主名称字母顺序排列，便于用户查找
        /// </remarks>
        public async Task<List<Employer>> GetEmployersAsync()
        {
            await InitAsync();

            // 生产版本：直接返回数据库中的雇主，不创建测试数据
            return await _database!.Table<Employer>()
                .Where(e => e.IsActive)
                .OrderBy(e => e.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取特定雇主信息
        /// Get employer by ID
        /// </summary>
        /// <param name="id">雇主的唯一标识符</param>
        /// <returns>雇主对象，如果不存在则返回null</returns>
        /// <remarks>
        /// 该方法会返回指定ID的雇主，无论其是否处于活跃状态
        /// </remarks>
        public async Task<Employer?> GetEmployerAsync(int id)
        {
            await InitAsync();
            return await _database!.FindAsync<Employer>(id);
        }

        /// <summary>
        /// 保存雇主信息（新增或更新）
        /// Save employer (insert or update)
        /// </summary>
        /// <param name="employer">要保存的雇主对象</param>
        /// <returns>雇主的ID（新增时返回新生成的ID，更新时返回原ID）</returns>
        /// <remarks>
        /// 根据雇主ID判断操作类型：
        /// - ID为0：执行插入操作，自动设置CreatedAt时间
        /// - ID不为0：执行更新操作，自动更新UpdatedAt时间
        /// </remarks>
        public async Task<int> SaveEmployerAsync(Employer employer)
        {
            await InitAsync();
            employer.UpdatedAt = DateTime.Now;

            if (employer.Id != 0)
            {
                await _database!.UpdateAsync(employer);
                return employer.Id;
            }
            else
            {
                employer.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(employer);
            }
        }

        /// <summary>
        /// 检查雇主是否有已确认的支付记录
        /// </summary>
        /// <param name="employerId">雇主ID</param>
        /// <returns>是否有已确认的支付记录</returns>
        public async Task<bool> HasConfirmedPaymentRecordsAsync(int employerId)
        {
            await InitAsync();
            var confirmedPayments = await _database!.Table<PaymentRecord>()
                .Where(p => p.EmployerId == employerId && p.Status == PaymentStatus.Paid)
                .CountAsync();
            return confirmedPayments > 0;
        }

        /// <summary>
        /// 获取雇主的已确认支付记录数量
        /// </summary>
        /// <param name="employerId">雇主ID</param>
        /// <returns>已确认支付记录数量</returns>
        public async Task<int> GetConfirmedPaymentRecordsCountAsync(int employerId)
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.EmployerId == employerId && p.Status == PaymentStatus.Paid)
                .CountAsync();
        }

        /// <summary>
        /// 删除雇主信息（软删除）
        /// Delete employer (soft delete)
        /// </summary>
        /// <param name="employer">要删除的雇主对象</param>
        /// <returns>受影响的行数</returns>
        /// <remarks>
        /// 采用软删除策略，将IsActive设置为false而不是物理删除记录，
        /// 这样可以保留历史数据的完整性，避免关联数据的孤立
        /// </remarks>
        public async Task<int> DeleteEmployerAsync(Employer employer)
        {
            await InitAsync();

            // 检查是否有已确认的支付记录
            var hasConfirmedPayments = await HasConfirmedPaymentRecordsAsync(employer.Id);
            if (hasConfirmedPayments)
            {
                var confirmedCount = await GetConfirmedPaymentRecordsCountAsync(employer.Id);
                throw new InvalidOperationException($"该雇主名下有 {confirmedCount} 条已确认的工资记录，请先取消所有相关的工资确认后再删除雇主");
            }

            employer.IsActive = false;
            employer.UpdatedAt = DateTime.Now;
            return await _database!.UpdateAsync(employer);
        }








        /// <summary>
        /// 清理所有测试数据（生产版本启动时调用）
        /// Clear all test data (called when production version starts)
        /// </summary>
        /// <returns>异步任务</returns>
        /// <remarks>
        /// 该方法用于生产环境启动时清理开发和测试阶段产生的数据，
        /// 确保用户获得干净的初始状态
        /// </remarks>
        public async Task ClearAllTestDataAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("DatabaseService: 开始清理所有测试数据");

                // 清理所有数据表（保持表结构）
                await _database!.DeleteAllAsync<WorkRecord>();
                await _database!.DeleteAllAsync<PaymentRecord>();
                await _database!.DeleteAllAsync<Shift>();
                await _database!.DeleteAllAsync<Employer>();

                // 清理其他可能的测试数据
                await _database!.DeleteAllAsync<NotificationRecord>();

                // 重置自增ID（如果需要）
                await _database!.ExecuteAsync("DELETE FROM sqlite_sequence WHERE name IN ('Employer', 'Shift', 'WorkRecord', 'PaymentRecord')");

                System.Diagnostics.Debug.WriteLine("DatabaseService: 测试数据清理完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DatabaseService: 清理测试数据失败 - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查是否存在测试数据
        /// Check if test data exists in the database
        /// </summary>
        /// <returns>如果存在测试数据返回true，否则返回false</returns>
        /// <remarks>
        /// 通过检查已知的测试雇主名称来判断数据库中是否包含测试数据，
        /// 用于决定是否需要清理测试数据
        /// </remarks>
        public async Task<bool> HasTestDataAsync()
        {
            await InitAsync();

            try
            {
                // 检查是否有已知的测试雇主
                var testEmployerNames = new[] { "ABC Restaurant", "Tech Solutions Ltd", "Green Garden Cafe", "星巴克咖啡", "麦当劳", "Woolworths超市" };

                var testEmployers = await _database!.Table<Employer>()
                    .Where(e => testEmployerNames.Contains(e.Name))
                    .CountAsync();

                return testEmployers > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DatabaseService: 检查测试数据失败 - {ex.Message}");
                return false;
            }
        }

        #endregion

        #region PaymentRecord Methods - 薪资记录管理

        /// <summary>
        /// 获取所有支付记录
        /// Get all payment records from database
        /// </summary>
        /// <returns>支付记录列表</returns>
        /// <remarks>
        /// 返回数据库中的所有薪资支付记录，用于薪资统计和历史查看
        /// </remarks>
        public async Task<List<PaymentRecord>> GetPaymentRecordsAsync()
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>().ToListAsync();
        }

        /// <summary>
        /// 根据雇主ID获取支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetPaymentRecordsByEmployerAsync(int employerId)
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.EmployerId == employerId)
                .OrderByDescending(p => p.PeriodEnd)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定时间段的支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetPaymentRecordsByPeriodAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.PeriodStart >= startDate && p.PeriodEnd <= endDate)
                .OrderByDescending(p => p.PeriodEnd)
                .ToListAsync();
        }

        /// <summary>
        /// 获取本周的支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetWeeklyPaymentRecordsAsync()
        {
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);
            return await GetPaymentRecordsByPeriodAsync(startOfWeek, endOfWeek);
        }

        /// <summary>
        /// 获取本周的已确认支付记录（仅用于收入统计）
        /// Get confirmed weekly payment records for income statistics only
        /// </summary>
        public async Task<List<PaymentRecord>> GetWeeklyConfirmedPaymentRecordsAsync()
        {
            await InitAsync();
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            return await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Paid &&
                           p.PeriodStart >= startOfWeek &&
                           p.PeriodEnd <= endOfWeek)
                .OrderByDescending(p => p.PeriodEnd)
                .ToListAsync();
        }

        /// <summary>
        /// 获取本月的支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetMonthlyPaymentRecordsAsync()
        {
            var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
            return await GetPaymentRecordsByPeriodAsync(startOfMonth, endOfMonth);
        }

        /// <summary>
        /// 获取本月的已确认支付记录（仅用于收入统计）
        /// Get confirmed monthly payment records for income statistics only
        /// </summary>
        public async Task<List<PaymentRecord>> GetMonthlyConfirmedPaymentRecordsAsync()
        {
            await InitAsync();
            var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            return await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Paid &&
                           p.PeriodStart >= startOfMonth &&
                           p.PeriodEnd <= endOfMonth)
                .OrderByDescending(p => p.PeriodEnd)
                .ToListAsync();
        }

        /// <summary>
        /// 获取近5年的年度收入统计
        /// </summary>
        public async Task<Dictionary<int, decimal>> GetYearlyIncomeAsync()
        {
            await InitAsync();
            var fiveYearsAgo = DateTime.Today.AddYears(-5);

            var records = await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Paid && p.ActualPaymentDate >= fiveYearsAgo)
                .ToListAsync();

            return records
                .GroupBy(p => p.ActualPaymentDate?.Year ?? p.PeriodEnd.Year)
                .ToDictionary(g => g.Key, g => g.Sum(p => p.TotalAmount));
        }

        /// <summary>
        /// 获取待确认的支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetPendingPaymentRecordsAsync()
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Pending)
                .OrderBy(p => p.ExpectedPaymentDate)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取支付记录
        /// </summary>
        public async Task<PaymentRecord?> GetPaymentRecordAsync(int id)
        {
            await InitAsync();
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.Id == id)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 确认支付记录
        /// </summary>
        public async Task<int> ConfirmPaymentAsync(PaymentRecord payment)
        {
            await InitAsync();
            payment.Status = PaymentStatus.Paid;
            payment.ActualPaymentDate = DateTime.Now;
            payment.UpdatedAt = DateTime.Now;
            return await _database!.UpdateAsync(payment);
        }

        /// <summary>
        /// 保存支付记录
        /// </summary>
        public async Task<int> SavePaymentRecordAsync(PaymentRecord payment)
        {
            await InitAsync();
            payment.UpdatedAt = DateTime.Now;

            if (payment.Id != 0)
            {
                return await _database!.UpdateAsync(payment);
            }
            else
            {
                payment.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(payment);
            }
        }

        /// <summary>
        /// 更新支付记录
        /// </summary>
        public async Task<int> UpdatePaymentRecordAsync(PaymentRecord payment)
        {
            await InitAsync();
            payment.UpdatedAt = DateTime.Now;
            return await _database!.UpdateAsync(payment);
        }

        /// <summary>
        /// 计算总确认收入
        /// </summary>
        public async Task<decimal> GetTotalConfirmedIncomeAsync()
        {
            await InitAsync();
            var confirmedRecords = await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Paid)
                .ToListAsync();

            return confirmedRecords.Sum(p => p.TotalAmount);
        }

        /// <summary>
        /// 计算指定时间段的确认收入
        /// </summary>
        public async Task<decimal> GetConfirmedIncomeByPeriodAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            var records = await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Paid &&
                           p.ActualPaymentDate >= startDate &&
                           p.ActualPaymentDate <= endDate)
                .ToListAsync();

            return records.Sum(p => p.TotalAmount);
        }

        #endregion

        #region 批量操作方法

        /// <summary>
        /// 批量保存班次
        /// </summary>
        public async Task<int> SaveShiftsBatchAsync(List<Shift> shifts)
        {
            await InitAsync();
            var result = 0;

            await _database!.RunInTransactionAsync(tran =>
            {
                foreach (var shift in shifts)
                {
                    shift.UpdatedAt = DateTime.Now;
                    if (shift.Id != 0)
                    {
                        tran.Update(shift);
                    }
                    else
                    {
                        shift.CreatedAt = DateTime.Now;
                        tran.Insert(shift);
                    }
                    result++;
                }
            });

            return result;
        }

        /// <summary>
        /// 批量删除工作记录
        /// </summary>
        public async Task<int> DeleteWorkRecordsBatchAsync(List<int> workRecordIds)
        {
            await InitAsync();
            var result = 0;

            await _database!.RunInTransactionAsync(tran =>
            {
                foreach (var id in workRecordIds)
                {
                    tran.Delete<WorkRecord>(id);
                    result++;
                }
            });

            return result;
        }

        /// <summary>
        /// 批量更新支付状态
        /// </summary>
        public async Task<int> UpdatePaymentStatusBatchAsync(List<PaymentRecord> payments)
        {
            await InitAsync();
            var result = 0;

            await _database!.RunInTransactionAsync(tran =>
            {
                foreach (var payment in payments)
                {
                    payment.UpdatedAt = DateTime.Now;
                    tran.Update(payment);
                    result++;
                }
            });

            return result;
        }

        /// <summary>
        /// 批量保存雇主
        /// </summary>
        public async Task<int> SaveEmployersBatchAsync(List<Employer> employers)
        {
            await InitAsync();
            var result = 0;

            await _database!.RunInTransactionAsync(tran =>
            {
                foreach (var employer in employers)
                {
                    employer.UpdatedAt = DateTime.Now;
                    if (employer.Id != 0)
                    {
                        tran.Update(employer);
                    }
                    else
                    {
                        employer.CreatedAt = DateTime.Now;
                        tran.Insert(employer);
                    }
                    result++;
                }
            });

            return result;
        }

        #endregion

        #region Shift Operations

        /// <summary>
        /// 获取指定日期范围的班次
        /// </summary>
        public async Task<List<Shift>> GetShiftsAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            return await _database!.Table<Shift>()
                .Where(s => s.StartTime >= startDate && s.StartTime <= endDate)
                .OrderBy(s => s.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定日期范围的有效班次（排除已取消的班次）
        /// </summary>
        public async Task<List<Shift>> GetActiveShiftsAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            return await _database!.Table<Shift>()
                .Where(s => s.StartTime >= startDate && s.StartTime <= endDate && s.Status != ShiftStatus.Cancelled)
                .OrderBy(s => s.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取今日班次（排除已取消的班次）
        /// </summary>
        public async Task<List<Shift>> GetTodayShiftsAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            return await GetActiveShiftsAsync(today, tomorrow);
        }

        /// <summary>
        /// 获取即将到来的班次（明天开始的未来7天，排除已取消的班次）
        /// </summary>
        public async Task<List<Shift>> GetUpcomingShiftsAsync(int days = 7)
        {
            var tomorrow = DateTime.Today.AddDays(1);
            var endDate = tomorrow.AddDays(days);
            return await GetActiveShiftsAsync(tomorrow, endDate);
        }

        /// <summary>
        /// 根据雇主获取班次
        /// </summary>
        public async Task<List<Shift>> GetShiftsByEmployerAsync(int employerId)
        {
            await InitAsync();
            return await _database!.Table<Shift>()
                .Where(s => s.EmployerId == employerId)
                .OrderBy(s => s.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// 保存班次
        /// </summary>
        public async Task<int> SaveShiftAsync(Shift shift)
        {
            await InitAsync();
            shift.UpdatedAt = DateTime.Now;

            if (shift.Id != 0)
            {
                await _database!.UpdateAsync(shift);
                return shift.Id;
            }
            else
            {
                shift.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(shift);
            }
        }

        /// <summary>
        /// 删除班次
        /// </summary>
        public async Task<int> DeleteShiftAsync(Shift shift)
        {
            await InitAsync();
            return await _database!.DeleteAsync(shift);
        }

        /// <summary>
        /// 检查班次冲突
        /// </summary>
        public async Task<List<Shift>> GetConflictingShiftsAsync(Shift shift)
        {
            await InitAsync();
            var allShifts = await _database!.Table<Shift>()
                .Where(s => s.Id != shift.Id && s.Status != ShiftStatus.Cancelled)
                .ToListAsync();

            return allShifts.Where(s => shift.ConflictsWith(s)).ToList();
        }

        /// <summary>
        /// 检查班次是否有冲突（返回布尔值）
        /// </summary>
        public async Task<bool> HasConflictingShiftsAsync(Shift shift)
        {
            var conflicts = await GetConflictingShiftsAsync(shift);
            return conflicts.Any();
        }

        /// <summary>
        /// 安全保存班次（包含冲突检测）
        /// </summary>
        public async Task<(bool Success, int ShiftId, string ErrorMessage)> SaveShiftWithConflictCheckAsync(Shift shift)
        {
            try
            {
                // 检查冲突
                var hasConflicts = await HasConflictingShiftsAsync(shift);
                if (hasConflicts)
                {
                    var conflicts = await GetConflictingShiftsAsync(shift);
                    var conflictDetails = string.Join(", ", conflicts.Select(c =>
                        $"{c.EmployerName} ({c.StartTime:HH:mm}-{c.EndTime:HH:mm})"));
                    return (false, 0, $"Time conflict detected with: {conflictDetails}");
                }

                // 保存班次
                var shiftId = await SaveShiftAsync(shift);
                return (true, shiftId, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, 0, $"Failed to save shift: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理重复的班次记录（用于数据修复）
        /// </summary>
        public async Task<int> CleanupDuplicateShiftsAsync()
        {
            await InitAsync();

            var allShifts = await _database!.Table<Shift>()
                .Where(s => s.Status != ShiftStatus.Cancelled)
                .OrderBy(s => s.StartTime)
                .ThenBy(s => s.Id)
                .ToListAsync();

            var duplicatesRemoved = 0;
            var shiftsToRemove = new List<Shift>();

            // 按时间分组，找出重复的班次
            var groupedShifts = allShifts.GroupBy(s => new { s.StartTime, s.EndTime, s.EmployerId });

            foreach (var group in groupedShifts.Where(g => g.Count() > 1))
            {
                // 保留第一个（最早创建的），删除其他重复的
                var shiftsInGroup = group.OrderBy(s => s.CreatedAt).ToList();
                for (int i = 1; i < shiftsInGroup.Count; i++)
                {
                    shiftsToRemove.Add(shiftsInGroup[i]);
                }
            }

            // 删除重复的班次
            foreach (var shift in shiftsToRemove)
            {
                await _database!.DeleteAsync(shift);
                duplicatesRemoved++;
            }

            System.Diagnostics.Debug.WriteLine($"Cleaned up {duplicatesRemoved} duplicate shifts");
            return duplicatesRemoved;
        }

        #endregion

        #region WorkRecord Operations

        /// <summary>
        /// 根据班次获取工作记录
        /// </summary>
        public async Task<WorkRecord?> GetWorkRecordByShiftAsync(int shiftId)
        {
            await InitAsync();
            return await _database!.Table<WorkRecord>()
                .Where(w => w.ShiftId == shiftId)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 保存工作记录
        /// </summary>
        public async Task<int> SaveWorkRecordAsync(WorkRecord workRecord)
        {
            await InitAsync();
            workRecord.UpdatedAt = DateTime.Now;

            if (workRecord.Id != 0)
            {
                await _database!.UpdateAsync(workRecord);
                return workRecord.Id;
            }
            else
            {
                workRecord.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(workRecord);
            }
        }

        /// <summary>
        /// 获取所有工作记录
        /// </summary>
        public async Task<List<WorkRecord>> GetWorkRecordsAsync()
        {
            await InitAsync();
            return await _database!.Table<WorkRecord>()
                .OrderByDescending(w => w.ClockInTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取班次
        /// </summary>
        public async Task<Shift?> GetShiftByIdAsync(int shiftId)
        {
            await InitAsync();
            return await _database!.Table<Shift>()
                .Where(s => s.Id == shiftId)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据ID获取班次（简化方法名）
        /// </summary>
        public async Task<Shift?> GetShiftAsync(int shiftId)
        {
            return await GetShiftByIdAsync(shiftId);
        }

        /// <summary>
        /// 更新班次
        /// </summary>
        public async Task<int> UpdateShiftAsync(Shift shift)
        {
            await InitAsync();
            shift.UpdatedAt = DateTime.Now;
            return await _database!.UpdateAsync(shift);
        }

        /// <summary>
        /// 根据ID删除班次
        /// </summary>
        public async Task<int> DeleteShiftAsync(int shiftId)
        {
            await InitAsync();
            return await _database!.DeleteAsync<Shift>(shiftId);
        }

        /// <summary>
        /// 获取指定日期的班次
        /// </summary>
        public async Task<List<Shift>> GetShiftsByDateAsync(DateTime date)
        {
            await InitAsync();
            var startOfDay = date.Date;
            var endOfDay = startOfDay.AddDays(1).AddTicks(-1);

            return await _database!.Table<Shift>()
                .Where(s => s.StartTime >= startOfDay && s.StartTime <= endOfDay)
                .OrderBy(s => s.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定日期范围的班次（简化方法名）
        /// </summary>
        public async Task<List<Shift>> GetShiftsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await GetActiveShiftsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取班次及雇主信息
        /// </summary>
        public async Task<List<Shift>> GetShiftsWithEmployerInfoAsync()
        {
            await InitAsync();
            var query = @"
                SELECT s.*, e.Name as EmployerName
                FROM Shifts s
                LEFT JOIN Employers e ON s.EmployerId = e.Id
                ORDER BY s.StartTime DESC";

            var shifts = await _database!.QueryAsync<Shift>(query);
            return shifts;
        }

        /// <summary>
        /// 删除工作记录
        /// </summary>
        public async Task<int> DeleteWorkRecordAsync(int workRecordId)
        {
            await InitAsync();
            return await _database!.DeleteAsync<WorkRecord>(workRecordId);
        }

        #endregion

        #region 复杂查询方法

        /// <summary>
        /// 获取工作记录及关联的班次和雇主信息
        /// </summary>
        public async Task<List<WorkRecordWithDetails>> GetWorkRecordsWithDetailsAsync()
        {
            await InitAsync();
            var query = @"
                SELECT
                    wr.*,
                    s.StartTime as ShiftStartTime,
                    s.EndTime as ShiftEndTime,
                    s.Location as ShiftLocation,
                    s.Description as ShiftDescription,
                    e.Name as EmployerName,
                    e.HourlyRate as EmployerHourlyRate,
                    e.Color as EmployerColor
                FROM WorkRecords wr
                INNER JOIN Shifts s ON wr.ShiftId = s.Id
                INNER JOIN Employers e ON s.EmployerId = e.Id
                ORDER BY wr.ClockInTime DESC";

            var results = await _database!.QueryAsync<WorkRecordWithDetails>(query);
            return results;
        }

        /// <summary>
        /// 获取指定雇主的班次统计
        /// </summary>
        public async Task<ShiftStatistics> GetShiftStatisticsByEmployerAsync(int employerId, DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            var query = @"
                SELECT
                    COUNT(*) as TotalShifts,
                    SUM(CASE WHEN s.Status = 2 THEN 1 ELSE 0 END) as CompletedShifts,
                    SUM(CASE WHEN s.Status = 3 THEN 1 ELSE 0 END) as CancelledShifts,
                    AVG((julianday(s.EndTime) - julianday(s.StartTime)) * 24) as AverageHours,
                    SUM((julianday(s.EndTime) - julianday(s.StartTime)) * 24) as TotalScheduledHours
                FROM Shifts s
                WHERE s.EmployerId = ? AND s.StartTime >= ? AND s.StartTime <= ?";

            var result = await _database!.QueryAsync<ShiftStatistics>(query, employerId, startDate, endDate);
            return result.FirstOrDefault() ?? new ShiftStatistics();
        }

        /// <summary>
        /// 获取逾期支付记录
        /// </summary>
        public async Task<List<PaymentRecord>> GetOverduePaymentsAsync()
        {
            await InitAsync();
            var today = DateTime.Today;
            return await _database!.Table<PaymentRecord>()
                .Where(p => p.Status == PaymentStatus.Pending &&
                           p.ExpectedPaymentDate.HasValue &&
                           p.ExpectedPaymentDate.Value < today)
                .OrderBy(p => p.ExpectedPaymentDate)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定日期范围的工作记录
        /// </summary>
        public async Task<List<WorkRecord>> GetWorkRecordsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            return await _database!.Table<WorkRecord>()
                .Where(w => w.ClockInTime >= startDate && w.ClockInTime <= endDate)
                .OrderBy(w => w.ClockInTime)
                .ToListAsync();
        }

        #endregion

        #region NotificationRecord Operations

        /// <summary>
        /// 保存通知记录
        /// </summary>
        public async Task<int> SaveNotificationRecordAsync(NotificationRecord notification)
        {
            await InitAsync();
            notification.UpdatedAt = DateTime.Now;

            if (notification.Id != 0)
            {
                return await _database!.UpdateAsync(notification);
            }
            else
            {
                notification.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(notification);
            }
        }

        /// <summary>
        /// 获取通知记录
        /// </summary>
        public async Task<NotificationRecord?> GetNotificationRecordAsync(int entityId, string entityType, NotificationType type)
        {
            await InitAsync();
            return await _database!.Table<NotificationRecord>()
                .Where(n => n.RelatedEntityId == entityId &&
                           n.RelatedEntityType == entityType &&
                           n.Type == type)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取用户的所有通知
        /// </summary>
        public async Task<List<NotificationRecord>> GetUserNotificationsAsync(int? userId = null, int limit = 50)
        {
            await InitAsync();
            var query = _database!.Table<NotificationRecord>();

            if (userId.HasValue)
            {
                query = query.Where(n => n.UserId == userId.Value);
            }

            return await query.OrderByDescending(n => n.CreatedAt)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        public async Task<int> MarkNotificationAsReadAsync(int notificationId)
        {
            await InitAsync();
            var notification = await _database!.FindAsync<NotificationRecord>(notificationId);
            if (notification != null)
            {
                notification.Status = NotificationStatus.Read;
                notification.ReadAt = DateTime.Now;
                notification.UpdatedAt = DateTime.Now;
                return await _database!.UpdateAsync(notification);
            }
            return 0;
        }

        /// <summary>
        /// 删除通知记录
        /// </summary>
        public async Task<bool> DeleteNotificationRecordAsync(int notificationId)
        {
            try
            {
                await InitAsync();
                var result = await _database!.DeleteAsync<NotificationRecord>(notificationId);
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除通知记录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除所有通知历史记录
        /// </summary>
        public async Task ClearNotificationHistoryAsync()
        {
            try
            {
                await InitAsync();
                await _database!.DeleteAllAsync<NotificationRecord>();
                System.Diagnostics.Debug.WriteLine("通知历史记录已清除");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除通知历史失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 统计和聚合方法

        /// <summary>
        /// 获取指定雇主的月度工作时长统计
        /// </summary>
        public async Task<List<MonthlyWorkHours>> GetMonthlyWorkHoursByEmployerAsync(int employerId, int year)
        {
            await InitAsync();
            var query = @"
                SELECT
                    strftime('%m', wr.ClockInTime) as Month,
                    SUM(wr.ActualHours) as TotalHours,
                    COUNT(*) as TotalShifts,
                    AVG(wr.ActualHours) as AverageHours
                FROM WorkRecords wr
                INNER JOIN Shifts s ON wr.ShiftId = s.Id
                WHERE s.EmployerId = ? AND strftime('%Y', wr.ClockInTime) = ?
                GROUP BY strftime('%m', wr.ClockInTime)
                ORDER BY Month";

            var results = await _database!.QueryAsync<MonthlyWorkHours>(query, employerId, year.ToString());
            return results;
        }

        /// <summary>
        /// 获取平均每日工作时长
        /// </summary>
        public async Task<double> GetAverageWorkHoursPerDayAsync(DateTime startDate, DateTime endDate)
        {
            await InitAsync();
            var query = @"
                SELECT AVG(daily_hours) as AverageHours
                FROM (
                    SELECT DATE(wr.ClockInTime) as work_date, SUM(wr.ActualHours) as daily_hours
                    FROM WorkRecords wr
                    WHERE wr.ClockInTime >= ? AND wr.ClockInTime <= ?
                    GROUP BY DATE(wr.ClockInTime)
                ) daily_totals";

            var result = await _database!.ExecuteScalarAsync<double?>(query, startDate, endDate);
            return result ?? 0;
        }

        /// <summary>
        /// 获取表现最佳的雇主（按收入排序）
        /// </summary>
        public async Task<List<TopEmployer>> GetTopPerformingEmployersAsync(int count = 5)
        {
            await InitAsync();
            var query = @"
                SELECT
                    e.Id,
                    e.Name,
                    e.Color,
                    SUM(pr.TotalAmount) as TotalEarnings,
                    COUNT(DISTINCT s.Id) as TotalShifts,
                    SUM(wr.ActualHours) as TotalHours
                FROM Employers e
                LEFT JOIN Shifts s ON e.Id = s.EmployerId
                LEFT JOIN WorkRecords wr ON s.Id = wr.ShiftId
                LEFT JOIN PaymentRecords pr ON e.Id = pr.EmployerId AND pr.Status = 1
                WHERE e.IsActive = 1
                GROUP BY e.Id, e.Name, e.Color
                ORDER BY TotalEarnings DESC
                LIMIT ?";

            var results = await _database!.QueryAsync<TopEmployer>(query, count);
            return results;
        }

        /// <summary>
        /// 获取工作效率指标
        /// </summary>
        public async Task<WorkEfficiencyMetrics> GetWorkEfficiencyMetricsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            await InitAsync();

            var dateFilter = "";
            var parameters = new List<object>();

            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "WHERE wr.ClockInTime >= ? AND wr.ClockInTime <= ?";
                parameters.Add(startDate.Value);
                parameters.Add(endDate.Value);
            }

            var query = $@"
                SELECT
                    COUNT(*) as TotalWorkRecords,
                    SUM(wr.ActualHours) as TotalWorkHours,
                    AVG(wr.ActualHours) as AverageShiftHours,
                    SUM(wr.OvertimeMinutes) / 60.0 as TotalOvertimeHours,
                    SUM(CASE WHEN wr.IsOnTime = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as OnTimePercentage,
                    AVG(wr.LateMinutes) as AverageLateMinutes,
                    COUNT(DISTINCT DATE(wr.ClockInTime)) as WorkingDays
                FROM WorkRecords wr
                {dateFilter}";

            var result = await _database!.QueryAsync<WorkEfficiencyMetrics>(query, parameters.ToArray());
            return result.FirstOrDefault() ?? new WorkEfficiencyMetrics();
        }

        /// <summary>
        /// 获取收入趋势数据
        /// </summary>
        public async Task<List<IncomeTrend>> GetIncomeTrendAsync(int months = 12)
        {
            await InitAsync();
            var startDate = DateTime.Today.AddMonths(-months);

            var query = @"
                SELECT
                    strftime('%Y-%m', pr.PeriodEnd) as Period,
                    SUM(pr.TotalAmount) as TotalIncome,
                    COUNT(*) as PaymentCount
                FROM PaymentRecords pr
                WHERE pr.Status = 1 AND pr.PeriodEnd >= ?
                GROUP BY strftime('%Y-%m', pr.PeriodEnd)
                ORDER BY Period";

            var results = await _database!.QueryAsync<IncomeTrend>(query, startDate);
            return results;
        }

        #endregion

        #region TodoItem Operations - 待办事项管理操作

        /// <summary>
        /// 获取所有待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetTodoItemsAsync()
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 获取未完成的待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetPendingTodoItemsAsync()
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => !t.IsCompleted)
                .OrderBy(t => t.DueDate)
                .ThenByDescending(t => t.Priority)
                .ToListAsync();
        }

        /// <summary>
        /// 获取已完成的待办事项（最近N条）
        /// </summary>
        public async Task<List<TodoItem>> GetRecentCompletedTodoItemsAsync(int count = 5)
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => t.IsCompleted)
                .OrderByDescending(t => t.CompletedDate)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 根据类型获取待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetTodoItemsByTypeAsync(TodoItemType type)
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => t.Type == type)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 获取逾期的待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetOverdueTodoItemsAsync()
        {
            await InitAsync();
            var now = DateTime.Now;
            return await _database!.Table<TodoItem>()
                .Where(t => !t.IsCompleted && t.DueDate.HasValue && t.DueDate.Value < now)
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }

        /// <summary>
        /// 根据关联实体ID获取待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetTodoItemsByRelatedEntityAsync(int relatedEntityId)
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => t.RelatedEntityId == relatedEntityId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 根据雇主获取待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetTodoItemsByEmployerAsync(int employerId)
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => t.EmployerId == employerId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取待办事项
        /// </summary>
        public async Task<TodoItem?> GetTodoItemAsync(int todoItemId)
        {
            await InitAsync();
            return await _database!.Table<TodoItem>()
                .Where(t => t.Id == todoItemId)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 保存待办事项
        /// </summary>
        public async Task<int> SaveTodoItemAsync(TodoItem todoItem)
        {
            await InitAsync();
            todoItem.UpdatedAt = DateTime.Now;

            if (todoItem.Id == 0)
            {
                todoItem.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(todoItem);
            }
            else
            {
                return await _database!.UpdateAsync(todoItem);
            }
        }

        /// <summary>
        /// 删除待办事项
        /// </summary>
        public async Task<int> DeleteTodoItemAsync(int todoItemId)
        {
            await InitAsync();
            return await _database!.DeleteAsync<TodoItem>(todoItemId);
        }

        /// <summary>
        /// 删除待办事项
        /// </summary>
        public async Task<int> DeleteTodoItemAsync(TodoItem todoItem)
        {
            await InitAsync();
            return await _database!.DeleteAsync(todoItem);
        }

        /// <summary>
        /// 获取待办事项统计信息
        /// </summary>
        public async Task<(int Total, int Pending, int Completed, int Overdue)> GetTodoItemStatisticsAsync()
        {
            await InitAsync();
            var now = DateTime.Now;

            var total = await _database!.Table<TodoItem>().CountAsync();
            var pending = await _database!.Table<TodoItem>().CountAsync(t => !t.IsCompleted);
            var completed = await _database!.Table<TodoItem>().CountAsync(t => t.IsCompleted);
            var overdue = await _database!.Table<TodoItem>()
                .CountAsync(t => !t.IsCompleted && t.DueDate.HasValue && t.DueDate.Value < now);

            return (total, pending, completed, overdue);
        }



        #endregion

        #region NotificationSettings Operations

        /// <summary>
        /// 获取通知设置
        /// </summary>
        public async Task<NotificationSettings?> GetNotificationSettingsAsync(int? userId = null)
        {
            await InitAsync();
            var query = _database!.Table<NotificationSettings>();

            if (userId.HasValue)
            {
                query = query.Where(s => s.UserId == userId.Value);
            }
            else
            {
                query = query.Where(s => s.UserId == null);
            }

            return await query.FirstOrDefaultAsync();
        }

        /// <summary>
        /// 保存通知设置
        /// </summary>
        public async Task<int> SaveNotificationSettingsAsync(NotificationSettings settings)
        {
            await InitAsync();
            settings.UpdatedAt = DateTime.Now;

            if (settings.Id != 0)
            {
                return await _database!.UpdateAsync(settings);
            }
            else
            {
                settings.CreatedAt = DateTime.Now;
                return await _database!.InsertAsync(settings);
            }
        }

        /// <summary>
        /// 删除通知设置
        /// </summary>
        public async Task<int> DeleteNotificationSettingsAsync(int settingsId)
        {
            await InitAsync();
            return await _database!.DeleteAsync<NotificationSettings>(settingsId);
        }

        /// <summary>
        /// 删除过期通知记录
        /// </summary>
        public async Task<int> DeleteExpiredNotificationsAsync(DateTime cutoffDate)
        {
            await InitAsync();
            return await _database!.ExecuteAsync(
                "DELETE FROM NotificationRecords WHERE CreatedAt < ?", cutoffDate);
        }

        #endregion

        #region Data Management Operations

        /// <summary>
        /// 清除所有数据（保留用户设置）
        /// </summary>
        public async Task ClearAllDataAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("Starting data reset operation...");

                // 先检查数据删除前的记录数
                var employersCountBefore = await _database!.Table<Employer>().CountAsync();
                var shiftsCountBefore = await _database!.Table<Shift>().CountAsync();
                var workRecordsCountBefore = await _database!.Table<WorkRecord>().CountAsync();
                var paymentRecordsCountBefore = await _database!.Table<PaymentRecord>().CountAsync();
                var notificationRecordsCountBefore = await _database!.Table<NotificationRecord>().CountAsync();
                var usersCountBefore = await _database!.Table<User>().CountAsync();
                var notificationSettingsCountBefore = await _database!.Table<NotificationSettings>().CountAsync();
                var auditLogCountBefore = await _database!.Table<AuditLog>().CountAsync();

                System.Diagnostics.Debug.WriteLine($"Before reset - Employers: {employersCountBefore}, Shifts: {shiftsCountBefore}, WorkRecords: {workRecordsCountBefore}, PaymentRecords: {paymentRecordsCountBefore}, NotificationRecords: {notificationRecordsCountBefore}, Users: {usersCountBefore}, NotificationSettings: {notificationSettingsCountBefore}, AuditLog: {auditLogCountBefore}");

                // 开始事务以确保数据一致性
                await _database!.RunInTransactionAsync((connection) =>
                {
                    try
                    {
                        // 禁用外键约束（如果有的话）
                        connection.Execute("PRAGMA foreign_keys = OFF");
                        System.Diagnostics.Debug.WriteLine("Disabled foreign key constraints");

                        // 清除所有雇主信息
                        var employersDeleted = connection.Execute("DELETE FROM Employers");
                        System.Diagnostics.Debug.WriteLine($"Deleted {employersDeleted} employers");

                        // 清除所有班次记录
                        var shiftsDeleted = connection.Execute("DELETE FROM Shifts");
                        System.Diagnostics.Debug.WriteLine($"Deleted {shiftsDeleted} shifts");

                        // 清除所有工作日志
                        var workRecordsDeleted = connection.Execute("DELETE FROM WorkRecords");
                        System.Diagnostics.Debug.WriteLine($"Deleted {workRecordsDeleted} work records");

                        // 清除所有收入信息
                        var paymentRecordsDeleted = connection.Execute("DELETE FROM PaymentRecords");
                        System.Diagnostics.Debug.WriteLine($"Deleted {paymentRecordsDeleted} payment records");

                        // 清除通知记录
                        var notificationRecordsDeleted = connection.Execute("DELETE FROM NotificationRecords");
                        System.Diagnostics.Debug.WriteLine($"Deleted {notificationRecordsDeleted} notification records");

                        // 清除用户信息
                        var usersDeleted = connection.Execute("DELETE FROM Users");
                        System.Diagnostics.Debug.WriteLine($"Deleted {usersDeleted} users");

                        // 清除通知设置（保留默认设置，只删除用户自定义的）
                        var notificationSettingsDeleted = connection.Execute("DELETE FROM NotificationSettings WHERE UserId IS NOT NULL");
                        System.Diagnostics.Debug.WriteLine($"Deleted {notificationSettingsDeleted} user notification settings");

                        // 清除审计日志
                        var auditLogDeleted = connection.Execute("DELETE FROM AuditLog");
                        System.Diagnostics.Debug.WriteLine($"Deleted {auditLogDeleted} audit log entries");

                        // 重置自增ID计数器
                        var sequenceReset = connection.Execute("DELETE FROM sqlite_sequence WHERE name IN ('Employers', 'Shifts', 'WorkRecords', 'PaymentRecords', 'NotificationRecords', 'Users', 'NotificationSettings', 'AuditLog')");
                        System.Diagnostics.Debug.WriteLine($"Reset {sequenceReset} sequence counters");

                        // 重新启用外键约束
                        connection.Execute("PRAGMA foreign_keys = ON");
                        System.Diagnostics.Debug.WriteLine("Re-enabled foreign key constraints");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error during transaction: {ex.Message}");
                        throw;
                    }
                });

                // 验证数据删除后的记录数
                var employersCountAfter = await _database!.Table<Employer>().CountAsync();
                var shiftsCountAfter = await _database!.Table<Shift>().CountAsync();
                var workRecordsCountAfter = await _database!.Table<WorkRecord>().CountAsync();
                var paymentRecordsCountAfter = await _database!.Table<PaymentRecord>().CountAsync();
                var notificationRecordsCountAfter = await _database!.Table<NotificationRecord>().CountAsync();
                var usersCountAfter = await _database!.Table<User>().CountAsync();
                var notificationSettingsCountAfter = await _database!.Table<NotificationSettings>().CountAsync();
                var auditLogCountAfter = await _database!.Table<AuditLog>().CountAsync();

                System.Diagnostics.Debug.WriteLine($"After reset - Employers: {employersCountAfter}, Shifts: {shiftsCountAfter}, WorkRecords: {workRecordsCountAfter}, PaymentRecords: {paymentRecordsCountAfter}, NotificationRecords: {notificationRecordsCountAfter}, Users: {usersCountAfter}, NotificationSettings: {notificationSettingsCountAfter}, AuditLog: {auditLogCountAfter}");

                var totalRecordsAfter = employersCountAfter + shiftsCountAfter + workRecordsCountAfter + paymentRecordsCountAfter + notificationRecordsCountAfter + usersCountAfter + auditLogCountAfter;

                if (totalRecordsAfter == 0)
                {
                    System.Diagnostics.Debug.WriteLine("✅ All data has been cleared successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ Warning: Some data may not have been cleared completely. Remaining records: {totalRecordsAfter}");
                    System.Diagnostics.Debug.WriteLine($"Details - Employers: {employersCountAfter}, Shifts: {shiftsCountAfter}, WorkRecords: {workRecordsCountAfter}, PaymentRecords: {paymentRecordsCountAfter}, NotificationRecords: {notificationRecordsCountAfter}, Users: {usersCountAfter}, NotificationSettings: {notificationSettingsCountAfter}, AuditLog: {auditLogCountAfter}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing data: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 验证数据是否已被清除
        /// </summary>
        public async Task<bool> VerifyDataClearedAsync()
        {
            await InitAsync();

            try
            {
                var employersCount = await _database!.Table<Employer>().CountAsync();
                var shiftsCount = await _database!.Table<Shift>().CountAsync();
                var workRecordsCount = await _database!.Table<WorkRecord>().CountAsync();
                var paymentRecordsCount = await _database!.Table<PaymentRecord>().CountAsync();
                var notificationRecordsCount = await _database!.Table<NotificationRecord>().CountAsync();
                var usersCount = await _database!.Table<User>().CountAsync();
                var notificationSettingsCount = await _database!.Table<NotificationSettings>().CountAsync();
                var auditLogCount = await _database!.Table<AuditLog>().CountAsync();

                var totalRecords = employersCount + shiftsCount + workRecordsCount + paymentRecordsCount + notificationRecordsCount + usersCount + auditLogCount;

                System.Diagnostics.Debug.WriteLine($"Verification - Total records remaining: {totalRecords} (Employers: {employersCount}, Shifts: {shiftsCount}, WorkRecords: {workRecordsCount}, PaymentRecords: {paymentRecordsCount}, NotificationRecords: {notificationRecordsCount}, Users: {usersCount}, NotificationSettings: {notificationSettingsCount}, AuditLog: {auditLogCount})");

                return totalRecords == 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error verifying data clear: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 应用启动时的数据清理检查
        /// </summary>
        public async Task<bool> CheckAndClearStartupDataAsync()
        {
            await InitAsync();

            try
            {
                // 检查是否存在测试数据或需要清理的数据
                var shouldClear = await ShouldClearDataOnStartupAsync();

                if (shouldClear)
                {
                    System.Diagnostics.Debug.WriteLine("DatabaseService: Detected test data, performing startup cleanup...");
                    await ClearAllDataAsync();

                    // 设置标记，表示已经进行过启动清理
                    await SetStartupCleanupCompletedAsync();

                    System.Diagnostics.Debug.WriteLine("DatabaseService: Startup cleanup completed");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DatabaseService: Error during startup cleanup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否应该在启动时清理数据
        /// </summary>
        private async Task<bool> ShouldClearDataOnStartupAsync()
        {
            try
            {
                // 检查是否已经完成过启动清理
                var hasCompletedCleanup = await HasCompletedStartupCleanupAsync();
                if (hasCompletedCleanup)
                {
                    return false; // 已经清理过，不需要再次清理
                }

                // 检查是否存在数据（如果是全新安装，不需要清理）
                var employersCount = await _database!.Table<Employer>().CountAsync();
                var shiftsCount = await _database!.Table<Shift>().CountAsync();
                var totalRecords = employersCount + shiftsCount;

                // 如果有数据，则认为需要清理（这可能是测试数据）
                return totalRecords > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking startup cleanup status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否已完成启动清理
        /// </summary>
        private async Task<bool> HasCompletedStartupCleanupAsync()
        {
            try
            {
                var setting = await _database!.Table<AppSettings>()
                    .Where(s => s.Key == "StartupCleanupCompleted")
                    .FirstOrDefaultAsync();

                return setting != null && setting.Value == "true";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking startup cleanup flag: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置启动清理已完成标记
        /// </summary>
        private async Task SetStartupCleanupCompletedAsync()
        {
            try
            {
                var setting = new AppSettings
                {
                    Key = "StartupCleanupCompleted",
                    Value = "true",
                    IsUserSetting = false, // 这是系统设置
                    UpdatedAt = DateTime.Now
                };

                await _database!.InsertOrReplaceAsync(setting);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting startup cleanup flag: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制清理所有数据表（包括可能遗漏的表）
        /// </summary>
        public async Task ForceClearAllDataAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("Starting FORCE data reset operation...");

                // 获取数据库中所有表的名称
                var tableQuery = await _database!.QueryAsync<dynamic>(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'AppSettings'");
                var tableNames = tableQuery.Select(t => (string)t.name).ToList();

                System.Diagnostics.Debug.WriteLine($"Found {tableNames.Count} tables to clear: {string.Join(", ", tableNames)}");

                // 开始事务以确保数据一致性
                await _database!.RunInTransactionAsync((connection) =>
                {
                    foreach (var tableName in tableNames)
                    {
                        try
                        {
                            var deletedCount = connection.Execute($"DELETE FROM {tableName}");
                            System.Diagnostics.Debug.WriteLine($"Deleted {deletedCount} records from {tableName}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error clearing table {tableName}: {ex.Message}");
                        }
                    }

                    // 重置所有自增ID计数器
                    try
                    {
                        var sequenceReset = connection.Execute("DELETE FROM sqlite_sequence WHERE name != 'AppSettings'");
                        System.Diagnostics.Debug.WriteLine($"Reset {sequenceReset} sequence counters");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error resetting sequences: {ex.Message}");
                    }
                });

                System.Diagnostics.Debug.WriteLine("✅ FORCE data reset completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during FORCE data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 验证数据库实例一致性 - 确保我们操作的是正确的数据库
        /// </summary>
        public async Task<bool> VerifyDatabaseInstanceAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("=== DATABASE INSTANCE VERIFICATION ===");

                // 创建一个测试记录来验证数据库实例
                var testEmployer = new Employer
                {
                    Name = "TEST_VERIFICATION_EMPLOYER",
                    HourlyRate = 999.99m,
                    ContactInfo = "<EMAIL>"
                };

                // 插入测试记录
                var insertedId = await _database!.InsertAsync(testEmployer);
                System.Diagnostics.Debug.WriteLine($"Inserted test employer with ID: {insertedId}");

                // 立即查询验证
                var retrievedEmployer = await _database!.FindAsync<Employer>(insertedId);
                if (retrievedEmployer != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Test employer retrieved: {retrievedEmployer.Name}");

                    // 删除测试记录
                    await _database!.DeleteAsync(retrievedEmployer);
                    System.Diagnostics.Debug.WriteLine("Test employer deleted");

                    // 验证删除
                    var deletedEmployer = await _database!.FindAsync<Employer>(insertedId);
                    if (deletedEmployer == null)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ Database instance verification successful");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ Database instance verification failed - delete operation didn't work");
                        return false;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ Database instance verification failed - insert operation didn't work");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during database instance verification: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 直接的数据清理功能（使用原生 SQL 强制删除）
        /// </summary>
        public async Task<bool> DirectDataResetAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("Starting direct data reset...");

                // 验证数据库实例
                var instanceValid = await VerifyDatabaseInstanceAsync();
                if (!instanceValid)
                {
                    System.Diagnostics.Debug.WriteLine("❌ Database instance verification failed, aborting direct reset");
                    return false;
                }

                // 先检查数据删除前的记录数
                var employersCountBefore = await _database!.Table<Employer>().CountAsync();
                var shiftsCountBefore = await _database!.Table<Shift>().CountAsync();
                var workRecordsCountBefore = await _database!.Table<WorkRecord>().CountAsync();
                var paymentRecordsCountBefore = await _database!.Table<PaymentRecord>().CountAsync();
                var notificationRecordsCountBefore = await _database!.Table<NotificationRecord>().CountAsync();
                var usersCountBefore = await _database!.Table<User>().CountAsync();
                var auditLogCountBefore = await _database!.Table<AuditLog>().CountAsync();

                System.Diagnostics.Debug.WriteLine($"Before direct reset - Employers: {employersCountBefore}, Shifts: {shiftsCountBefore}, WorkRecords: {workRecordsCountBefore}, PaymentRecords: {paymentRecordsCountBefore}, NotificationRecords: {notificationRecordsCountBefore}, Users: {usersCountBefore}, AuditLog: {auditLogCountBefore}");

                // 使用原生 SQL 强制删除
                var employersDeleted = await _database!.ExecuteAsync("DELETE FROM Employers");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM Employers - Affected rows: {employersDeleted}");

                var shiftsDeleted = await _database!.ExecuteAsync("DELETE FROM Shifts");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM Shifts - Affected rows: {shiftsDeleted}");

                var workRecordsDeleted = await _database!.ExecuteAsync("DELETE FROM WorkRecords");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM WorkRecords - Affected rows: {workRecordsDeleted}");

                var paymentRecordsDeleted = await _database!.ExecuteAsync("DELETE FROM PaymentRecords");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM PaymentRecords - Affected rows: {paymentRecordsDeleted}");

                var notificationRecordsDeleted = await _database!.ExecuteAsync("DELETE FROM NotificationRecords");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM NotificationRecords - Affected rows: {notificationRecordsDeleted}");

                var usersDeleted = await _database!.ExecuteAsync("DELETE FROM Users");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM Users - Affected rows: {usersDeleted}");

                var auditLogDeleted = await _database!.ExecuteAsync("DELETE FROM AuditLog");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM AuditLog - Affected rows: {auditLogDeleted}");

                // 清除用户自定义的通知设置
                var notificationSettingsDeleted = await _database!.ExecuteAsync("DELETE FROM NotificationSettings WHERE UserId IS NOT NULL");
                System.Diagnostics.Debug.WriteLine($"Executed: DELETE FROM NotificationSettings WHERE UserId IS NOT NULL - Affected rows: {notificationSettingsDeleted}");

                // 重置自增ID计数器
                var sequenceReset = await _database!.ExecuteAsync("DELETE FROM sqlite_sequence WHERE name IN ('Employers', 'Shifts', 'WorkRecords', 'PaymentRecords', 'NotificationRecords', 'Users', 'AuditLog', 'NotificationSettings')");
                System.Diagnostics.Debug.WriteLine($"Reset sequence counters - Affected rows: {sequenceReset}");

                // 验证清理结果
                var isCleared = await VerifyDataClearedAsync();

                if (isCleared)
                {
                    System.Diagnostics.Debug.WriteLine("✅ Direct data reset successful");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ Direct data reset completed but data verification failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during direct data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 简化的数据清理功能（使用 SQLite-net 的 DeleteAll 方法）
        /// </summary>
        public async Task<bool> SimpleDataResetAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("Starting simple data reset...");

                // 使用 SQLite-net 的 DeleteAll 方法清理数据
                var employersDeleted = await _database!.DeleteAllAsync<Employer>();
                System.Diagnostics.Debug.WriteLine($"Deleted {employersDeleted} employers");

                var shiftsDeleted = await _database!.DeleteAllAsync<Shift>();
                System.Diagnostics.Debug.WriteLine($"Deleted {shiftsDeleted} shifts");

                var workRecordsDeleted = await _database!.DeleteAllAsync<WorkRecord>();
                System.Diagnostics.Debug.WriteLine($"Deleted {workRecordsDeleted} work records");

                var paymentRecordsDeleted = await _database!.DeleteAllAsync<PaymentRecord>();
                System.Diagnostics.Debug.WriteLine($"Deleted {paymentRecordsDeleted} payment records");

                var notificationRecordsDeleted = await _database!.DeleteAllAsync<NotificationRecord>();
                System.Diagnostics.Debug.WriteLine($"Deleted {notificationRecordsDeleted} notification records");

                var usersDeleted = await _database!.DeleteAllAsync<User>();
                System.Diagnostics.Debug.WriteLine($"Deleted {usersDeleted} users");

                var auditLogDeleted = await _database!.DeleteAllAsync<AuditLog>();
                System.Diagnostics.Debug.WriteLine($"Deleted {auditLogDeleted} audit log entries");

                // 清除用户自定义的通知设置
                var userNotificationSettings = await _database!.Table<NotificationSettings>()
                    .Where(ns => ns.UserId != null)
                    .ToListAsync();

                foreach (var setting in userNotificationSettings)
                {
                    await _database!.DeleteAsync(setting);
                }
                System.Diagnostics.Debug.WriteLine($"Deleted {userNotificationSettings.Count} user notification settings");

                // 验证清理结果
                var isCleared = await VerifyDataClearedAsync();

                if (isCleared)
                {
                    System.Diagnostics.Debug.WriteLine("✅ Simple data reset successful");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ Simple data reset completed but data verification failed");
                    return false; // 返回 false，让 CompleteDataResetAsync 尝试其他方法
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during simple data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 核武器级数据清理 - 完全删除并重建数据库文件
        /// </summary>
        public async Task<(bool Success, string DetailedLog)> NuclearDatabaseResetAsync()
        {
            var log = new StringBuilder();
            bool success = false;

            try
            {
                log.AppendLine("=== NUCLEAR DATABASE RESET STARTED ===");
                log.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                log.AppendLine();

                // 步骤 1: 记录重置前的状态
                log.AppendLine("STEP 1: Recording current database state...");
                if (File.Exists(_databasePath))
                {
                    var fileInfo = new FileInfo(_databasePath);
                    log.AppendLine($"Current database file size: {fileInfo.Length} bytes");
                    log.AppendLine($"Current database last modified: {fileInfo.LastWriteTime}");
                }
                else
                {
                    log.AppendLine("Database file does not exist");
                }

                // 步骤 2: 强制关闭所有数据库连接
                log.AppendLine();
                log.AppendLine("STEP 2: Closing all database connections...");
                if (_database != null)
                {
                    try
                    {
                        await _database.CloseAsync();
                        log.AppendLine("Database connection closed successfully");
                    }
                    catch (Exception ex)
                    {
                        log.AppendLine($"Warning: Error closing database connection: {ex.Message}");
                    }
                    _database = null;
                }

                // 强制垃圾回收，释放所有可能的数据库句柄
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                log.AppendLine("Forced garbage collection completed");

                // 等待一段时间确保文件句柄被释放
                await Task.Delay(500);

                // 步骤 3: 删除所有数据库相关文件
                log.AppendLine();
                log.AppendLine("STEP 3: Deleting all database files...");

                var filesToDelete = new[]
                {
                    _databasePath,
                    _databasePath + "-wal",
                    _databasePath + "-shm",
                    _databasePath + "-journal"
                };

                foreach (var filePath in filesToDelete)
                {
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            // 尝试删除文件，如果被锁定则强制删除
                            File.SetAttributes(filePath, FileAttributes.Normal);
                            File.Delete(filePath);
                            log.AppendLine($"✅ Deleted: {Path.GetFileName(filePath)}");
                        }
                        else
                        {
                            log.AppendLine($"⚪ Not found: {Path.GetFileName(filePath)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        log.AppendLine($"❌ Failed to delete {Path.GetFileName(filePath)}: {ex.Message}");
                    }
                }

                // 步骤 4: 验证文件删除
                log.AppendLine();
                log.AppendLine("STEP 4: Verifying file deletion...");
                bool allFilesDeleted = true;
                foreach (var filePath in filesToDelete)
                {
                    if (File.Exists(filePath))
                    {
                        log.AppendLine($"❌ File still exists: {Path.GetFileName(filePath)}");
                        allFilesDeleted = false;
                    }
                }

                if (allFilesDeleted)
                {
                    log.AppendLine("✅ All database files successfully deleted");
                }
                else
                {
                    log.AppendLine("⚠️ Some database files could not be deleted");
                }

                // 步骤 5: 重新创建数据库
                log.AppendLine();
                log.AppendLine("STEP 5: Recreating database from scratch...");

                try
                {
                    await InitAsync();
                    log.AppendLine("✅ Database recreated successfully");

                    // 验证新数据库
                    if (File.Exists(_databasePath))
                    {
                        var newFileInfo = new FileInfo(_databasePath);
                        log.AppendLine($"New database file size: {newFileInfo.Length} bytes");
                        log.AppendLine($"New database created: {newFileInfo.CreationTime}");
                    }
                }
                catch (Exception ex)
                {
                    log.AppendLine($"❌ Failed to recreate database: {ex.Message}");
                    throw;
                }

                // 步骤 6: 验证数据库功能
                log.AppendLine();
                log.AppendLine("STEP 6: Verifying database functionality...");

                try
                {
                    // 测试数据库连接
                    var tables = await _database!.QueryAsync<dynamic>("SELECT name FROM sqlite_master WHERE type='table'");
                    log.AppendLine($"✅ Database connection working, found {tables.Count} tables");

                    // 验证数据为空
                    var isCleared = await VerifyDataClearedAsync();
                    if (isCleared)
                    {
                        log.AppendLine("✅ Database is completely empty as expected");
                        success = true;
                    }
                    else
                    {
                        log.AppendLine("❌ Database still contains data after reset");
                    }
                }
                catch (Exception ex)
                {
                    log.AppendLine($"❌ Database functionality test failed: {ex.Message}");
                }

                log.AppendLine();
                log.AppendLine($"=== NUCLEAR DATABASE RESET {(success ? "COMPLETED SUCCESSFULLY" : "FAILED")} ===");
                log.AppendLine($"End timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            }
            catch (Exception ex)
            {
                log.AppendLine($"CRITICAL ERROR during nuclear reset: {ex.Message}");
                log.AppendLine($"Stack trace: {ex.StackTrace}");
                success = false;
            }

            var logResult = log.ToString();
            System.Diagnostics.Debug.WriteLine(logResult);
            return (success, logResult);
        }

        /// <summary>
        /// 使用原生 SQLite 命令的强力清理
        /// </summary>
        public async Task<bool> RawSqlDataResetAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("Starting RAW SQL data reset...");

                // 获取所有表名
                var tables = await _database!.QueryAsync<dynamic>("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'AppSettings'");
                var tableNames = tables.Select(t => (string)t.name).ToList();

                System.Diagnostics.Debug.WriteLine($"Found {tableNames.Count} tables to clear: {string.Join(", ", tableNames)}");

                // 禁用外键约束
                await _database.ExecuteAsync("PRAGMA foreign_keys = OFF");
                System.Diagnostics.Debug.WriteLine("Foreign keys disabled");

                // 开始事务
                await _database.ExecuteAsync("BEGIN TRANSACTION");
                System.Diagnostics.Debug.WriteLine("Transaction started");

                try
                {
                    // 清空每个表
                    foreach (var tableName in tableNames)
                    {
                        var sql = $"DELETE FROM [{tableName}]";
                        var deletedRows = await _database.ExecuteAsync(sql);
                        System.Diagnostics.Debug.WriteLine($"Executed: {sql} - Deleted {deletedRows} rows");
                    }

                    // 重置序列
                    await _database.ExecuteAsync("DELETE FROM sqlite_sequence WHERE name != 'AppSettings'");
                    System.Diagnostics.Debug.WriteLine("Sequences reset");

                    // 提交事务
                    await _database.ExecuteAsync("COMMIT");
                    System.Diagnostics.Debug.WriteLine("Transaction committed");

                    // 重新启用外键约束
                    await _database.ExecuteAsync("PRAGMA foreign_keys = ON");
                    System.Diagnostics.Debug.WriteLine("Foreign keys re-enabled");

                    // 执行 VACUUM 来压缩数据库
                    await _database.ExecuteAsync("VACUUM");
                    System.Diagnostics.Debug.WriteLine("Database vacuumed");

                    // 验证清理结果
                    var isCleared = await VerifyDataClearedAsync();

                    if (isCleared)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ RAW SQL data reset successful");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ RAW SQL data reset failed verification");
                        return false;
                    }
                }
                catch (Exception)
                {
                    // 回滚事务
                    await _database.ExecuteAsync("ROLLBACK");
                    System.Diagnostics.Debug.WriteLine("Transaction rolled back due to error");
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during RAW SQL data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 超详细的数据重置测试方法 - 用于深度调试
        /// </summary>
        public async Task<(bool Success, string DetailedLog)> TestDataResetWithFullLoggingAsync()
        {
            var log = new StringBuilder();
            bool success = false;

            try
            {
                log.AppendLine("=== DETAILED DATA RESET TEST ===");
                log.AppendLine($"Start Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                log.AppendLine();

                // 1. 获取初始诊断信息
                log.AppendLine("STEP 1: Getting initial database diagnostics...");
                var initialDiagnostics = await GetComprehensiveDatabaseDiagnosticsAsync();
                log.AppendLine("Initial diagnostics completed.");
                log.AppendLine();

                // 2. 记录重置前的数据状态
                log.AppendLine("STEP 2: Recording data state BEFORE reset...");
                await DebugShowAllDataAsync();
                log.AppendLine("Data state recorded.");
                log.AppendLine();

                // 3. 尝试数据库实例验证
                log.AppendLine("STEP 3: Verifying database instance...");
                var instanceValid = await VerifyDatabaseInstanceAsync();
                log.AppendLine($"Database instance verification: {(instanceValid ? "PASSED" : "FAILED")}");
                log.AppendLine();

                // 4. 尝试第一种方法：原生 SQL
                log.AppendLine("STEP 4: Attempting RAW SQL reset...");
                try
                {
                    var rawSqlSuccess = await RawSqlDataResetAsync();
                    log.AppendLine($"RAW SQL reset result: {(rawSqlSuccess ? "SUCCESS" : "FAILED")}");

                    if (rawSqlSuccess)
                    {
                        var verificationAfterRaw = await VerifyDataClearedAsync();
                        log.AppendLine($"Verification after RAW SQL: {(verificationAfterRaw ? "PASSED" : "FAILED")}");

                        if (verificationAfterRaw)
                        {
                            success = true;
                            log.AppendLine("✅ Data reset successful with RAW SQL method");
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.AppendLine($"RAW SQL reset EXCEPTION: {ex.Message}");
                }
                log.AppendLine();

                // 5. 如果第一种方法失败，尝试核武器方法
                if (!success)
                {
                    log.AppendLine("STEP 5: RAW SQL failed, attempting NUCLEAR reset...");
                    try
                    {
                        var (nuclearSuccess, nuclearLog) = await NuclearDatabaseResetAsync();
                        log.AppendLine($"NUCLEAR reset result: {(nuclearSuccess ? "SUCCESS" : "FAILED")}");

                        if (nuclearSuccess)
                        {
                            var verificationAfterNuclear = await VerifyDataClearedAsync();
                            log.AppendLine($"Verification after NUCLEAR: {(verificationAfterNuclear ? "PASSED" : "FAILED")}");

                            if (verificationAfterNuclear)
                            {
                                success = true;
                                log.AppendLine("✅ Data reset successful with NUCLEAR method");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        log.AppendLine($"NUCLEAR reset EXCEPTION: {ex.Message}");
                    }
                    log.AppendLine();
                }

                // 6. 记录重置后的数据状态
                log.AppendLine("STEP 6: Recording data state AFTER reset...");
                await DebugShowAllDataAsync();
                log.AppendLine("Final data state recorded.");
                log.AppendLine();

                // 7. 获取最终诊断信息
                log.AppendLine("STEP 7: Getting final database diagnostics...");
                var finalDiagnostics = await GetComprehensiveDatabaseDiagnosticsAsync();
                log.AppendLine("Final diagnostics completed.");
                log.AppendLine();

                log.AppendLine($"=== TEST COMPLETED: {(success ? "SUCCESS" : "FAILED")} ===");
                log.AppendLine($"End Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            }
            catch (Exception ex)
            {
                log.AppendLine($"CRITICAL ERROR in test: {ex.Message}");
                log.AppendLine($"Stack trace: {ex.StackTrace}");
                success = false;
            }

            var logResult = log.ToString();
            System.Diagnostics.Debug.WriteLine(logResult);
            return (success, logResult);
        }

        /// <summary>
        /// 完整的数据清理功能（包含所有验证和日志）
        /// </summary>
        public async Task<bool> CompleteDataResetAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting complete data reset...");

                // 使用详细的测试方法
                var (success, detailedLog) = await TestDataResetWithFullLoggingAsync();

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("✅ Complete data reset successful");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ Complete data reset failed");
                    System.Diagnostics.Debug.WriteLine("Detailed log:");
                    System.Diagnostics.Debug.WriteLine(detailedLog);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during complete data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 超详细的数据库诊断信息 - 用于深度调试
        /// </summary>
        public async Task<string> GetComprehensiveDatabaseDiagnosticsAsync()
        {
            var diagnostics = new StringBuilder();

            try
            {
                diagnostics.AppendLine("=== COMPREHENSIVE DATABASE DIAGNOSTICS ===");
                diagnostics.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                diagnostics.AppendLine($"Thread ID: {Thread.CurrentThread.ManagedThreadId}");
                diagnostics.AppendLine();

                // 1. 路径和文件信息
                diagnostics.AppendLine("--- FILE SYSTEM INFO ---");
                diagnostics.AppendLine($"Database Path: {_databasePath}");
                diagnostics.AppendLine($"AppDataDirectory: {FileSystem.AppDataDirectory}");
                diagnostics.AppendLine($"Database File Exists: {File.Exists(_databasePath)}");

                if (File.Exists(_databasePath))
                {
                    var fileInfo = new FileInfo(_databasePath);
                    diagnostics.AppendLine($"Database File Size: {fileInfo.Length} bytes");
                    diagnostics.AppendLine($"Database Last Modified: {fileInfo.LastWriteTime}");
                    diagnostics.AppendLine($"Database Created: {fileInfo.CreationTime}");
                    diagnostics.AppendLine($"Database Attributes: {fileInfo.Attributes}");
                }

                // 检查相关文件
                var walFile = _databasePath + "-wal";
                var shmFile = _databasePath + "-shm";
                diagnostics.AppendLine($"WAL File Exists: {File.Exists(walFile)}");
                diagnostics.AppendLine($"SHM File Exists: {File.Exists(shmFile)}");

                // 2. 连接状态
                diagnostics.AppendLine();
                diagnostics.AppendLine("--- CONNECTION INFO ---");
                diagnostics.AppendLine($"Database Connection Initialized: {_database != null}");

                if (_database == null)
                {
                    diagnostics.AppendLine("Attempting to initialize database...");
                    await InitAsync();
                    diagnostics.AppendLine($"Database Connection After Init: {_database != null}");
                }

                if (_database != null)
                {
                    // 3. 数据库结构信息
                    diagnostics.AppendLine();
                    diagnostics.AppendLine("--- DATABASE STRUCTURE ---");

                    try
                    {
                        var tables = await _database.QueryAsync<dynamic>("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
                        diagnostics.AppendLine($"Total Tables: {tables.Count}");

                        foreach (var table in tables)
                        {
                            var tableName = (string)table.name;
                            diagnostics.AppendLine($"  Table: {tableName}");

                            try
                            {
                                var countResult = await _database.QueryAsync<dynamic>($"SELECT COUNT(*) as count FROM [{tableName}]");
                                var count = countResult.FirstOrDefault()?.count ?? 0;
                                diagnostics.AppendLine($"    Records: {count}");
                            }
                            catch (Exception ex)
                            {
                                diagnostics.AppendLine($"    Records: ERROR - {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        diagnostics.AppendLine($"Error getting table structure: {ex.Message}");
                    }

                    // 4. 数据库设置
                    diagnostics.AppendLine();
                    diagnostics.AppendLine("--- DATABASE SETTINGS ---");

                    try
                    {
                        var pragmas = new[]
                        {
                            "foreign_keys",
                            "journal_mode",
                            "synchronous",
                            "cache_size",
                            "temp_store",
                            "locking_mode",
                            "auto_vacuum"
                        };

                        foreach (var pragma in pragmas)
                        {
                            try
                            {
                                var pragmaResult = await _database.QueryAsync<dynamic>($"PRAGMA {pragma}");
                                var value = pragmaResult.FirstOrDefault();
                                diagnostics.AppendLine($"  {pragma}: {value}");
                            }
                            catch (Exception ex)
                            {
                                diagnostics.AppendLine($"  {pragma}: ERROR - {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        diagnostics.AppendLine($"Error getting database settings: {ex.Message}");
                    }
                }

                diagnostics.AppendLine("=== END COMPREHENSIVE DIAGNOSTICS ===");
            }
            catch (Exception ex)
            {
                diagnostics.AppendLine($"CRITICAL ERROR in diagnostics: {ex.Message}");
                diagnostics.AppendLine($"Stack trace: {ex.StackTrace}");
            }

            var result = diagnostics.ToString();
            System.Diagnostics.Debug.WriteLine(result);
            return result;
        }

        /// <summary>
        /// 调试方法：显示数据库连接和路径信息
        /// </summary>
        public async Task DebugShowDatabaseInfoAsync()
        {
            await GetComprehensiveDatabaseDiagnosticsAsync();
        }

        /// <summary>
        /// 调试方法：显示当前数据库中的所有数据
        /// </summary>
        public async Task DebugShowAllDataAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("=== DATABASE DATA INFO ===");

                var employersCount = await _database!.Table<Employer>().CountAsync();
                var shiftsCount = await _database!.Table<Shift>().CountAsync();
                var workRecordsCount = await _database!.Table<WorkRecord>().CountAsync();
                var paymentRecordsCount = await _database!.Table<PaymentRecord>().CountAsync();
                var notificationRecordsCount = await _database!.Table<NotificationRecord>().CountAsync();
                var usersCount = await _database!.Table<User>().CountAsync();
                var notificationSettingsCount = await _database!.Table<NotificationSettings>().CountAsync();
                var auditLogCount = await _database!.Table<AuditLog>().CountAsync();
                var appSettingsCount = await _database!.Table<AppSettings>().CountAsync();

                System.Diagnostics.Debug.WriteLine($"Employers: {employersCount}");
                System.Diagnostics.Debug.WriteLine($"Shifts: {shiftsCount}");
                System.Diagnostics.Debug.WriteLine($"WorkRecords: {workRecordsCount}");
                System.Diagnostics.Debug.WriteLine($"PaymentRecords: {paymentRecordsCount}");
                System.Diagnostics.Debug.WriteLine($"NotificationRecords: {notificationRecordsCount}");
                System.Diagnostics.Debug.WriteLine($"Users: {usersCount}");
                System.Diagnostics.Debug.WriteLine($"NotificationSettings: {notificationSettingsCount}");
                System.Diagnostics.Debug.WriteLine($"AuditLog: {auditLogCount}");
                System.Diagnostics.Debug.WriteLine($"AppSettings: {appSettingsCount}");

                var totalRecords = employersCount + shiftsCount + workRecordsCount + paymentRecordsCount + notificationRecordsCount + usersCount + auditLogCount;
                System.Diagnostics.Debug.WriteLine($"Total business records: {totalRecords}");

                // 显示一些具体的数据
                if (employersCount > 0)
                {
                    var employers = await _database!.Table<Employer>().ToListAsync();
                    System.Diagnostics.Debug.WriteLine("Employers:");
                    foreach (var emp in employers)
                    {
                        System.Diagnostics.Debug.WriteLine($"  - {emp.Name} (ID: {emp.Id})");
                    }
                }

                if (shiftsCount > 0)
                {
                    var shifts = await _database!.Table<Shift>().ToListAsync();
                    System.Diagnostics.Debug.WriteLine("Shifts:");
                    foreach (var shift in shifts)
                    {
                        System.Diagnostics.Debug.WriteLine($"  - Shift {shift.StartTime:yyyy-MM-dd HH:mm} (ID: {shift.Id}, EmployerId: {shift.EmployerId})");
                    }
                }

                System.Diagnostics.Debug.WriteLine("=== END DATA INFO ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during data debug: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查当前数据库中的所有数据（调试用）
        /// </summary>
        public async Task<string> GetCurrentDataSummaryAsync()
        {
            await InitAsync();

            try
            {
                var summary = new StringBuilder();
                summary.AppendLine("=== 当前数据库内容摘要 ===");

                // 检查雇主数据
                var employers = await _database!.Table<Employer>().ToListAsync();
                summary.AppendLine($"雇主数量: {employers.Count}");
                foreach (var emp in employers)
                {
                    summary.AppendLine($"  - {emp.Name} (ID: {emp.Id}, Active: {emp.IsActive})");
                }

                // 检查班次数据
                var shifts = await _database!.Table<Shift>().ToListAsync();
                summary.AppendLine($"班次数量: {shifts.Count}");
                foreach (var shift in shifts.Take(5)) // 只显示前5个
                {
                    summary.AppendLine($"  - {shift.StartTime:yyyy-MM-dd HH:mm} (ID: {shift.Id}, Status: {shift.Status}, EmployerId: {shift.EmployerId})");
                }
                if (shifts.Count > 5)
                {
                    summary.AppendLine($"  ... 还有 {shifts.Count - 5} 个班次");
                }

                // 检查工作记录数据
                var workRecords = await _database!.Table<WorkRecord>().ToListAsync();
                summary.AppendLine($"工作记录数量: {workRecords.Count}");

                // 检查支付记录数据
                var paymentRecords = await _database!.Table<PaymentRecord>().ToListAsync();
                summary.AppendLine($"支付记录数量: {paymentRecords.Count}");

                // 检查通知记录数据
                var notificationRecords = await _database!.Table<NotificationRecord>().ToListAsync();
                summary.AppendLine($"通知记录数量: {notificationRecords.Count}");

                // 检查应用设置
                var appSettings = await _database!.Table<AppSettings>().ToListAsync();
                summary.AppendLine($"应用设置数量: {appSettings.Count}");
                foreach (var setting in appSettings)
                {
                    summary.AppendLine($"  - {setting.Key}: {setting.Value}");
                }

                summary.AppendLine("=== 摘要结束 ===");
                return summary.ToString();
            }
            catch (Exception ex)
            {
                return $"获取数据摘要失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 强制清理所有数据并验证（调试用）
        /// </summary>
        public async Task<bool> ForceCompleteDataClearAsync()
        {
            await InitAsync();

            try
            {
                System.Diagnostics.Debug.WriteLine("开始强制清理所有数据...");

                // 获取清理前的数据摘要
                var beforeSummary = await GetCurrentDataSummaryAsync();
                System.Diagnostics.Debug.WriteLine("清理前数据状态:");
                System.Diagnostics.Debug.WriteLine(beforeSummary);

                // 执行多种清理方法确保彻底清理
                await _database!.DeleteAllAsync<WorkRecord>();
                await _database!.DeleteAllAsync<PaymentRecord>();
                await _database!.DeleteAllAsync<Shift>();
                await _database!.DeleteAllAsync<Employer>();
                await _database!.DeleteAllAsync<NotificationRecord>();

                // 使用原生 SQL 确保清理
                await _database!.ExecuteAsync("DELETE FROM WorkRecords");
                await _database!.ExecuteAsync("DELETE FROM PaymentRecords");
                await _database!.ExecuteAsync("DELETE FROM Shifts");
                await _database!.ExecuteAsync("DELETE FROM Employers");
                await _database!.ExecuteAsync("DELETE FROM NotificationRecords");

                // 重置自增ID
                await _database!.ExecuteAsync("DELETE FROM sqlite_sequence WHERE name IN ('Employers', 'Shifts', 'WorkRecords', 'PaymentRecords', 'NotificationRecords')");

                // 获取清理后的数据摘要
                var afterSummary = await GetCurrentDataSummaryAsync();
                System.Diagnostics.Debug.WriteLine("清理后数据状态:");
                System.Diagnostics.Debug.WriteLine(afterSummary);

                // 验证是否清理成功
                var totalRecords = await _database!.Table<Employer>().CountAsync() +
                                 await _database!.Table<Shift>().CountAsync() +
                                 await _database!.Table<WorkRecord>().CountAsync() +
                                 await _database!.Table<PaymentRecord>().CountAsync() +
                                 await _database!.Table<NotificationRecord>().CountAsync();

                var success = totalRecords == 0;
                System.Diagnostics.Debug.WriteLine($"强制清理结果: {(success ? "成功" : "失败")} - 剩余记录数: {totalRecords}");

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"强制清理失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
