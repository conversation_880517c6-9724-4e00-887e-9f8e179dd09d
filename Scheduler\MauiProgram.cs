// 引入必要的命名空间 / Import necessary namespaces
using Microsoft.Extensions.Logging;  // 日志记录功能 / Logging functionality
using CommunityToolkit.Maui;         // MAUI社区工具包 / MAUI Community Toolkit
using Plugin.LocalNotification;      // 本地通知插件 / Local notification plugin
using Scheduler.Services;            // 应用服务层 / Application service layer
using Scheduler.ViewModels;          // 视图模型层 / View model layer
using Scheduler.Views;               // 视图层 / View layer

namespace Scheduler;

/// <summary>
/// MAUI程序配置类 / MAUI Program Configuration Class
/// 负责配置和初始化整个应用程序的依赖注入容器和服务
/// Responsible for configuring and initializing the entire application's dependency injection container and services
/// </summary>
public static class MauiProgram
{
    /// <summary>
    /// 创建MAUI应用程序实例 / Create MAUI Application Instance
    /// 配置所有必要的服务、视图模型和视图的依赖注入
    /// Configure dependency injection for all necessary services, view models and views
    /// </summary>
    /// <returns>配置完成的MAUI应用程序实例 / Configured MAUI application instance</returns>
    public static MauiApp CreateMauiApp()
    {
        // 创建MAUI应用程序构建器 / Create MAUI application builder
        var builder = MauiApp.CreateBuilder();

        // 配置基础MAUI应用程序设置 / Configure basic MAUI application settings
        builder
            .UseMauiApp<App>()                    // 使用App类作为主应用程序 / Use App class as main application
            .UseMauiCommunityToolkit()           // 启用MAUI社区工具包 / Enable MAUI Community Toolkit
            .UseLocalNotification()              // 启用本地通知功能 / Enable local notification functionality
            .ConfigureFonts(fonts =>             // 配置应用程序字体 / Configure application fonts
            {
                // 添加OpenSans常规字体 / Add OpenSans regular font
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                // 添加OpenSans半粗体字体 / Add OpenSans semibold font
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

        // ==================== 服务注册 / Service Registration ====================

        // 注册核心数据服务 / Register core data services
        // 使用单例模式确保整个应用程序共享同一个数据库连接
        // Use singleton pattern to ensure the entire application shares the same database connection
        builder.Services.AddSingleton<DatabaseService>();

        // 注册本地化服务 / Register localization service
        // 提供多语言支持功能 / Provide multi-language support functionality
        builder.Services.AddSingleton<LocalizationService>();

        // 注册业务逻辑服务 / Register business logic services
        // 工资计算服务 / Payroll calculation service
        builder.Services.AddTransient<PayrollService>();
        // 班次管理服务 / Shift management service
        builder.Services.AddTransient<ShiftManagementService>();
        // 演示数据服务 / Demo data service
        builder.Services.AddTransient<DemoDataService>();

        // 注册通知相关服务 / Register notification-related services
        // 通知服务接口和实现 / Notification service interface and implementation
        builder.Services.AddTransient<Scheduler.Services.INotificationService, Scheduler.Services.NotificationService>();
        builder.Services.AddTransient<Scheduler.Services.NotificationService>();
        // 通知设置服务接口和实现 / Notification settings service interface and implementation
        builder.Services.AddTransient<Scheduler.Services.INotificationSettingsService, Scheduler.Services.NotificationSettingsService>();
        builder.Services.AddTransient<Scheduler.Services.NotificationSettingsService>();
        // 通知调度器（单例） / Notification scheduler (singleton)
        builder.Services.AddSingleton<NotificationScheduler>();

        // 注册应用程序初始化和导航服务 / Register application initialization and navigation services
        // 应用程序初始化服务（单例） / Application initialization service (singleton)
        builder.Services.AddSingleton<AppInitializationService>();
        // 导航服务（单例） / Navigation service (singleton)
        builder.Services.AddSingleton<INavigationService, NavigationService>();

        // 注册辅助功能服务 / Register utility services
        // 权限管理服务 / Permission management service
        builder.Services.AddTransient<PermissionService>();
        // 通知测试服务 / Notification test service
        builder.Services.AddTransient<NotificationTestService>();
        // 数据验证服务 / Data validation service
        builder.Services.AddTransient<DataValidationService>();
        // 备份服务 / Backup service
        builder.Services.AddTransient<BackupService>();
        // 数据库连接测试器 / Database connection tester
        builder.Services.AddTransient<DatabaseConnectionTester>();
        // 待办事项服务 / Todo service
        builder.Services.AddTransient<TodoService>();

        // 注册UI辅助服务 / Register UI utility services
        // 自动关闭提示服务 / Auto-close alert service
        builder.Services.AddTransient<IAutoCloseAlertService, AutoCloseAlertService>();

        // 注册平台特定服务 / Register platform-specific services
        // 键盘服务（根据平台选择不同实现） / Keyboard service (different implementations based on platform)
#if ANDROID
        // Android平台键盘服务 / Android platform keyboard service
        builder.Services.AddTransient<IKeyboardService, Platforms.Android.KeyboardService>();
#else
        // 其他平台使用默认键盘服务实现 / Other platforms use default keyboard service implementation
        builder.Services.AddTransient<IKeyboardService, Services.DefaultKeyboardService>();
#endif

        // ==================== 视图模型注册 / ViewModel Registration ====================
        // 注册所有视图模型，使用瞬态生命周期确保每次导航都创建新实例
        // Register all view models with transient lifetime to ensure new instances are created for each navigation

        // 用户认证相关 / User authentication related
        builder.Services.AddTransient<LoginViewModel>();              // 登录视图模型 / Login view model

        // 主要功能页面 / Main functionality pages
        builder.Services.AddTransient<HomeViewModel>();               // 主页视图模型 / Home view model
        builder.Services.AddTransient<CalendarViewModel>();           // 日历视图模型 / Calendar view model
        builder.Services.AddTransient<LogViewModel>();                // 日志视图模型 / Log view model
        builder.Services.AddTransient<PayViewModel>();                // 工资视图模型 / Pay view model
        builder.Services.AddTransient<SetViewModel>();                // 设置视图模型 / Settings view model

        // 雇主管理相关 / Employer management related
        builder.Services.AddTransient<EmployerViewModel>();           // 雇主视图模型 / Employer view model
        builder.Services.AddTransient<AllEmployerViewModel>();        // 所有雇主视图模型 / All employers view model
        builder.Services.AddTransient<EditEmployerViewModel>();       // 编辑雇主视图模型 / Edit employer view model

        // 班次管理相关 / Shift management related
        builder.Services.AddTransient<SimpleShiftViewModel>();        // 简单班次视图模型 / Simple shift view model
        builder.Services.AddTransient<ShiftListViewModel>();          // 班次列表视图模型 / Shift list view model
        builder.Services.AddTransient<ShiftManagementViewModel>();    // 班次管理视图模型 / Shift management view model

        // 待办事项相关 / Todo related
        builder.Services.AddTransient<TodoListViewModel>();           // 待办事项列表视图模型 / Todo list view model
        builder.Services.AddTransient<PaymentTodoViewModel>();        // 付款待办视图模型 / Payment todo view model
        builder.Services.AddTransient<AllPaymentRecordsViewModel>();  // 所有付款记录视图模型 / All payment records view model

        // 通知相关 / Notification related
        builder.Services.AddTransient<NotificationSettingsViewModel>(); // 通知设置视图模型 / Notification settings view model
        builder.Services.AddTransient<NotificationHistoryViewModel>();  // 通知历史视图模型 / Notification history view model
        builder.Services.AddTransient<NotificationTestViewModel>();     // 通知测试视图模型 / Notification test view model

        // 测试和调试相关 / Testing and debugging related
        builder.Services.AddTransient<DatabaseTestViewModel>();       // 数据库测试视图模型 / Database test view model
        builder.Services.AddTransient<AutoCloseTestViewModel>();      // 自动关闭测试视图模型 / Auto-close test view model

        // ==================== 视图注册 / View Registration ====================
        // 注册所有视图页面，使用瞬态生命周期确保每次导航都创建新实例
        // Register all view pages with transient lifetime to ensure new instances are created for each navigation

        // 用户认证相关 / User authentication related
        builder.Services.AddTransient<LoginView>();                   // 登录视图 / Login view

        // 主要功能页面 / Main functionality pages
        builder.Services.AddTransient<HomeView>();                    // 主页视图 / Home view
        builder.Services.AddTransient<CalendarView>();                // 日历视图 / Calendar view
        builder.Services.AddTransient<LogView>();                     // 日志视图 / Log view
        builder.Services.AddTransient<PayView>();                     // 工资视图 / Pay view
        builder.Services.AddTransient<SetView>();                     // 设置视图 / Settings view

        // 雇主管理相关 / Employer management related
        builder.Services.AddTransient<EmployerView>();                // 雇主视图 / Employer view
        builder.Services.AddTransient<AllEmployer>();                 // 所有雇主视图 / All employers view
        builder.Services.AddTransient<EditEmployerView>();            // 编辑雇主视图 / Edit employer view

        // 班次管理相关 / Shift management related
        builder.Services.AddTransient<SimpleShiftView>();             // 简单班次视图 / Simple shift view
        builder.Services.AddTransient<ShiftListView>();               // 班次列表视图 / Shift list view
        builder.Services.AddTransient<ShiftManagementView>();         // 班次管理视图 / Shift management view

        // 待办事项相关 / Todo related
        builder.Services.AddTransient<TodoListView>();                // 待办事项列表视图 / Todo list view
        builder.Services.AddTransient<PaymentTodoView>();             // 付款待办视图 / Payment todo view
        builder.Services.AddTransient<AllPaymentRecordsView>();       // 所有付款记录视图 / All payment records view

        // 通知相关 / Notification related
        builder.Services.AddTransient<NotificationSettingsView>();    // 通知设置视图 / Notification settings view
        builder.Services.AddTransient<NotificationTestView>();        // 通知测试视图 / Notification test view
        builder.Services.AddTransient<NotificationHistoryView>();     // 通知历史视图 / Notification history view

        // 测试和调试相关 / Testing and debugging related
        builder.Services.AddTransient<DatabaseTestView>();            // 数据库测试视图 / Database test view
        builder.Services.AddTransient<AutoCloseTestView>();           // 自动关闭测试视图 / Auto-close test view

        // ==================== 开发环境配置 / Development Environment Configuration ====================
#if DEBUG
        // 在调试模式下启用调试日志记录 / Enable debug logging in debug mode
        builder.Logging.AddDebug();
#endif

        // 构建并返回配置完成的MAUI应用程序实例 / Build and return the configured MAUI application instance
        return builder.Build();
    }
}
