﻿using Android.App;
using Android.Runtime;

namespace Scheduler
{
    [Application]
    public class MainApplication : MauiApplication
    {
        public MainApplication(IntPtr handle, JniHandleOwnership ownership)
            : base(handle, ownership)
        {
        }

        protected override MauiApp CreateMauiApp()
        {
#if !DESIGN
            // 初始化Android平台滚动优化
            // Initialize Android platform scroll optimization
            Platforms.Android.ScrollOptimization.Initialize();
#endif
            return MauiProgram.CreateMauiApp();
        }
    }
}
