// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 班次列表视图 / Shift List View
/// 显示所有工作班次的列表，支持搜索、筛选和管理功能
/// Display list of all work shifts with search, filter and management functions
/// </summary>
public partial class ShiftListView : ContentPage
{
    // 班次列表视图模型实例 / Shift list view model instance
    private readonly ShiftListViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化班次列表视图并配置数据绑定 / Initialize shift list view and configure data binding
    /// </summary>
    /// <param name="viewModel">班次列表视图模型 / Shift list view model</param>
    public ShiftListView(ShiftListViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 初始化班次数据和筛选选项 / Initialize shift data and filter options
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        // 初始化视图模型数据 / Initialize view model data
        await _viewModel.InitializeAsync();
    }
}
