// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 所有雇主列表视图模型 / All Employers List ViewModel
    /// 管理雇主列表的显示、搜索、选择和批量操作
    /// Manages employer list display, search, selection and batch operations
    /// </summary>
    public partial class AllEmployerViewModel : BaseViewModel, IRecipient<EmployerUpdatedMessage>, IRecipient<LanguageChangedMessage>, IRecipient<DataResetMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 雇主集合 / Employers collection
        [ObservableProperty]
        private ObservableCollection<EmployerWrapper> employers = new();

        // 选中的雇主 / Selected employer
        [ObservableProperty]
        private EmployerWrapper? selectedEmployer;

        // 是否有雇主数据 / Whether has employer data
        [ObservableProperty]
        private bool hasEmployers;

        // 是否可以编辑 / Whether can edit
        [ObservableProperty]
        private bool canEdit;

        // 是否可以删除 / Whether can delete
        [ObservableProperty]
        private bool canDelete;

        // 搜索文本 / Search text
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 是否有选中的雇主 / Whether has selected employers
        /// </summary>
        [ObservableProperty]
        private bool hasSelectedEmployers;

        /// <summary>
        /// 全选按钮文本 / Select all button text
        /// </summary>
        [ObservableProperty]
        private string selectAllButtonText = string.Empty;

        /// <summary>
        /// 批量删除按钮文本 / Batch delete button text
        /// </summary>
        [ObservableProperty]
        private string batchDeleteButtonText = string.Empty;

        private List<Employer> _allEmployers = new();

        /// <summary>
        /// 选中的雇主列表
        /// </summary>
        public List<EmployerWrapper> SelectedEmployers =>
            Employers.Where(e => e.IsSelected).ToList();

        public AllEmployerViewModel(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            _localizationService = LocalizationService.Instance;

            // 初始化按钮文本
            InitializeButtonTexts();

            // 注册消息接收
            WeakReferenceMessenger.Default.Register<EmployerUpdatedMessage>(this);
            WeakReferenceMessenger.Default.Register<LanguageChangedMessage>(this);
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
        }

        /// <summary>
        /// 本地化文本集合
        /// </summary>
        public LocalizedTexts LocalizedTexts => new(_localizationService);

        /// <summary>
        /// 初始化按钮文本
        /// </summary>
        private void InitializeButtonTexts()
        {
            SelectAllButtonText = _localizationService.GetLocalizedString("SelectAll");
            BatchDeleteButtonText = _localizationService.GetLocalizedString("DeleteSelected");
        }

        /// <summary>
        /// 页面出现时初始化
        /// </summary>
        public async Task InitializeAsync()
        {
            await LoadEmployersAsync();
        }

        /// <summary>
        /// 加载雇主列表
        /// </summary>
        [RelayCommand]
        private async Task LoadEmployersAsync()
        {
            if (IsBusy) return;

            try
            {
                IsBusy = true;

                _allEmployers = await _databaseService.GetEmployersAsync();
                FilterEmployers();
                
                HasEmployers = Employers.Any();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync(_localizationService.GetLocalizedString("LoadFailed"), ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 筛选雇主列表
        /// </summary>
        private void FilterEmployers()
        {
            var filteredEmployers = _allEmployers.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchTerm = SearchText.Trim().ToLower();
                filteredEmployers = filteredEmployers.Where(e =>
                    e.Name.ToLower().Contains(searchTerm) ||
                    (e.ContactInfo?.ToLower().Contains(searchTerm) ?? false) ||
                    (e.WorkLocation?.ToLower().Contains(searchTerm) ?? false));
            }

            Employers.Clear();
            foreach (var employer in filteredEmployers.OrderBy(e => e.Name))
            {
                Employers.Add(new EmployerWrapper(employer));
            }

            UpdateSelectionStates();
        }

        /// <summary>
        /// 搜索文本变化时触发
        /// </summary>
        partial void OnSearchTextChanged(string value)
        {
            FilterEmployers();
            HasEmployers = Employers.Any();
        }

        /// <summary>
        /// 更新选择状态
        /// </summary>
        private void UpdateSelectionStates()
        {
            HasSelectedEmployers = SelectedEmployers.Any();
            var selectedCount = SelectedEmployers.Count;
            var totalCount = Employers.Count;

            if (selectedCount == 0)
            {
                SelectAllButtonText = _localizationService.GetLocalizedString("SelectAll");
                BatchDeleteButtonText = _localizationService.GetLocalizedString("DeleteSelected");
            }
            else if (selectedCount == totalCount)
            {
                SelectAllButtonText = _localizationService.GetLocalizedString("UnselectAll");
                BatchDeleteButtonText = _localizationService.GetString("DeleteSelectedCount", selectedCount);
            }
            else
            {
                SelectAllButtonText = _localizationService.GetLocalizedString("SelectAll");
                BatchDeleteButtonText = _localizationService.GetString("DeleteSelectedCount", selectedCount);
            }
        }

        /// <summary>
        /// 切换雇主选择状态
        /// </summary>
        [RelayCommand]
        private void ToggleEmployerSelection(EmployerWrapper employerWrapper)
        {
            if (employerWrapper != null)
            {
                employerWrapper.IsSelected = !employerWrapper.IsSelected;
                UpdateSelectionStates();
            }
        }

        /// <summary>
        /// 全选/取消全选
        /// </summary>
        [RelayCommand]
        private void ToggleSelectAll()
        {
            var hasAnySelected = SelectedEmployers.Any();
            var allSelected = SelectedEmployers.Count == Employers.Count;

            // 如果全部选中，则取消全选；否则全选
            var newState = !allSelected;

            foreach (var employer in Employers)
            {
                employer.IsSelected = newState;
            }

            UpdateSelectionStates();
        }

        /// <summary>
        /// 选中雇主变化时触发
        /// </summary>
        partial void OnSelectedEmployerChanged(EmployerWrapper? value)
        {
            UpdateButtonStates();
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            CanEdit = SelectedEmployer != null;
            CanDelete = SelectedEmployer != null;
        }

        /// <summary>
        /// 批量删除选中的雇主
        /// </summary>
        [RelayCommand]
        private async Task BatchDeleteAsync()
        {
            var selectedEmployers = SelectedEmployers;
            if (!selectedEmployers.Any()) return;

            try
            {
                // 确认删除
                var employerNames = string.Join("、", selectedEmployers.Select(e => e.Name));
                var confirmTitle = _localizationService.GetLocalizedString("ConfirmDelete");
                var confirmMessage = _localizationService.GetString("ConfirmDeleteEmployers", employerNames);
                var deleteText = _localizationService.GetLocalizedString("Delete");
                var cancelText = _localizationService.GetLocalizedString("Cancel");

                bool confirmed = await Application.Current!.MainPage!.DisplayAlert(
                    confirmTitle,
                    confirmMessage,
                    deleteText,
                    cancelText);

                if (!confirmed) return;

                IsBusy = true;

                // 批量删除雇主
                foreach (var employerWrapper in selectedEmployers)
                {
                    await _databaseService.DeleteEmployerAsync(employerWrapper.Employer);
                }

                // 发送雇主更新消息
                WeakReferenceMessenger.Default.Send(new EmployerUpdatedMessage());

                var successTitle = _localizationService.GetLocalizedString("DeleteSuccess");
                var successMessage = _localizationService.GetString("SuccessfullyDeletedEmployers", selectedEmployers.Count);
                await ShowSuccessAsync(successMessage, successTitle);

                // 重新加载列表
                await LoadEmployersAsync();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync(_localizationService.GetLocalizedString("DeleteFailed"), ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 编辑单个雇主
        /// </summary>
        [RelayCommand]
        private async Task EditSingleEmployerAsync(EmployerWrapper employerWrapper)
        {
            if (employerWrapper?.Employer == null) return;

            await Shell.Current.GoToAsync($"EditEmployer?employerId={employerWrapper.Employer.Id}");
        }

        /// <summary>
        /// 新增雇主
        /// </summary>
        [RelayCommand]
        private async Task AddEmployerAsync()
        {
            await Shell.Current.GoToAsync("EmployerView");
        }

        /// <summary>
        /// 编辑选中的雇主
        /// </summary>
        [RelayCommand]
        private async Task EditEmployerAsync()
        {
            if (SelectedEmployer?.Employer == null) return;

            await Shell.Current.GoToAsync($"EditEmployer?employerId={SelectedEmployer.Employer.Id}");
        }

        /// <summary>
        /// 删除选中的雇主
        /// </summary>
        [RelayCommand]
        private async Task DeleteEmployerAsync()
        {
            if (SelectedEmployer?.Employer == null) return;

            try
            {
                // 确认删除
                var confirmTitle = _localizationService.GetLocalizedString("ConfirmDelete");
                var confirmMessage = _localizationService.GetString("ConfirmDeleteEmployer", SelectedEmployer.Name);
                var deleteText = _localizationService.GetLocalizedString("Delete");
                var cancelText = _localizationService.GetLocalizedString("Cancel");

                bool confirmed = await Application.Current!.MainPage!.DisplayAlert(
                    confirmTitle,
                    confirmMessage,
                    deleteText,
                    cancelText);

                if (!confirmed) return;

                IsBusy = true;

                await _databaseService.DeleteEmployerAsync(SelectedEmployer.Employer);

                // 发送雇主更新消息
                WeakReferenceMessenger.Default.Send(new EmployerUpdatedMessage());

                var successTitle = _localizationService.GetLocalizedString("DeleteSuccess");
                var successMessage = _localizationService.GetString("EmployerDeleted", SelectedEmployer.Name);
                await ShowSuccessAsync(successTitle, successMessage);

                // 重新加载列表
                await LoadEmployersAsync();

                // 清除选择
                SelectedEmployer = null;
            }
            catch (Exception ex)
            {
                await ShowErrorAsync(_localizationService.GetLocalizedString("DeleteFailed"), ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 返回Home页面
        /// </summary>
        [RelayCommand]
        private async Task GoToHomeAsync()
        {
            await Shell.Current.GoToAsync("//Main/Home");
        }

        /// <summary>
        /// 刷新列表
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadEmployersAsync();
        }

        /// <summary>
        /// 接收雇主更新消息
        /// </summary>
        public void Receive(EmployerUpdatedMessage message)
        {
            // 在UI线程上重新加载数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await LoadEmployersAsync();
            });
        }

        /// <summary>
        /// 接收语言更改消息
        /// </summary>
        public void Receive(LanguageChangedMessage message)
        {
            // 语言更改时，更新按钮文本
            InitializeButtonTexts();
            UpdateButtonStates();
            OnPropertyChanged(nameof(LocalizedTexts));
        }

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            // 当数据被重置时，刷新雇主列表
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"AllEmployerViewModel: Received data reset message at {message.ResetTime}");

                // 清空当前数据
                Employers.Clear();
                _allEmployers.Clear();
                SelectedEmployer = null;

                // 重置状态
                HasEmployers = false;
                CanEdit = false;
                CanDelete = false;
                SearchText = string.Empty;

                // 重新加载数据（应该为空）
                await LoadEmployersAsync();

                System.Diagnostics.Debug.WriteLine($"AllEmployerViewModel: Data refreshed after reset - Employers: {Employers.Count}");
            });
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            WeakReferenceMessenger.Default.Unregister<EmployerUpdatedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<LanguageChangedMessage>(this);
            WeakReferenceMessenger.Default.Unregister<DataResetMessage>(this);
        }
    }
}
