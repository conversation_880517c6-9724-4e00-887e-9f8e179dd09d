using System.Text.Json;
using DishMeAPP.Models;

namespace DishMeAPP.Services
{
    /// <summary>
    /// 数据迁移服务 - 将JSON文件数据迁移到SQLite数据库
    /// Data Migration Service - Migrate JSON file data to SQLite database
    /// 用于从旧的JSON存储系统平滑迁移到新的SQLite数据库系统
    /// </summary>
    public class DataMigrationService
    {
        private readonly RecipeStorageService _storageService;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// 构造函数
        /// Constructor
        /// </summary>
        /// <param name="storageService">SQLite存储服务</param>
        public DataMigrationService(RecipeStorageService storageService)
        {
            _storageService = storageService;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
        }

        /// <summary>
        /// 从JSON文件迁移数据到SQLite数据库
        /// Migrate data from JSON files to SQLite database
        /// </summary>
        /// <returns>迁移结果</returns>
        public async Task<(bool Success, int MigratedCount, List<string> Errors)> MigrateFromJsonToSqliteAsync()
        {
            var errors = new List<string>();
            var migratedCount = 0;

            try
            {
                System.Diagnostics.Debug.WriteLine("开始数据迁移：从JSON到SQLite...");

                // 检查旧的JSON存储目录
                // Check old JSON storage directory
                var oldJsonPath = Path.Combine(FileSystem.AppDataDirectory, "Recipes");
                if (!Directory.Exists(oldJsonPath))
                {
                    System.Diagnostics.Debug.WriteLine("未找到旧的JSON数据目录，跳过迁移");
                    return (true, 0, errors);
                }

                // 获取所有JSON文件
                // Get all JSON files
                var jsonFiles = Directory.GetFiles(oldJsonPath, "recipe_*.json");
                System.Diagnostics.Debug.WriteLine($"找到 {jsonFiles.Length} 个JSON文件");

                if (jsonFiles.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine("没有找到需要迁移的JSON文件");
                    return (true, 0, errors);
                }

                // 检查数据库中是否已有数据
                // Check if database already has data
                var existingRecipes = await _storageService.GetAllRecipesAsync();
                if (existingRecipes.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"数据库中已有 {existingRecipes.Count} 个菜谱，跳过迁移");
                    return (true, 0, new List<string> { "数据库中已有数据，跳过迁移" });
                }

                // 逐个迁移JSON文件
                // Migrate JSON files one by one
                foreach (var jsonFile in jsonFiles)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"正在迁移文件: {Path.GetFileName(jsonFile)}");

                        // 读取JSON文件
                        // Read JSON file
                        var jsonContent = await File.ReadAllTextAsync(jsonFile);
                        if (string.IsNullOrWhiteSpace(jsonContent))
                        {
                            errors.Add($"文件 {Path.GetFileName(jsonFile)} 为空");
                            continue;
                        }

                        // 反序列化JSON
                        // Deserialize JSON
                        var recipe = JsonSerializer.Deserialize<Recipe>(jsonContent, _jsonOptions);
                        if (recipe == null)
                        {
                            errors.Add($"无法解析文件 {Path.GetFileName(jsonFile)}");
                            continue;
                        }

                        // 重置ID让数据库自动生成
                        // Reset ID to let database auto-generate
                        recipe.Id = 0;

                        // 确保时间字段有效
                        // Ensure time fields are valid
                        if (recipe.CreatedAt == default)
                        {
                            recipe.CreatedAt = DateTime.Now;
                        }
                        if (recipe.UpdatedAt == default)
                        {
                            recipe.UpdatedAt = DateTime.Now;
                        }

                        // 保存到SQLite数据库
                        // Save to SQLite database
                        var savedRecipe = await _storageService.SaveRecipeAsync(recipe);
                        
                        migratedCount++;
                        System.Diagnostics.Debug.WriteLine($"成功迁移菜谱: {savedRecipe.Name} (新ID: {savedRecipe.Id})");
                    }
                    catch (Exception ex)
                    {
                        var error = $"迁移文件 {Path.GetFileName(jsonFile)} 失败: {ex.Message}";
                        errors.Add(error);
                        System.Diagnostics.Debug.WriteLine(error);
                    }
                }

                // 迁移完成后的处理
                // Post-migration processing
                if (migratedCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"数据迁移完成: 成功迁移 {migratedCount} 个菜谱");
                    
                    // 可选：备份原JSON目录
                    // Optional: Backup original JSON directory
                    await BackupOriginalJsonFilesAsync(oldJsonPath);
                }

                return (true, migratedCount, errors);
            }
            catch (Exception ex)
            {
                var error = $"数据迁移过程中发生错误: {ex.Message}";
                errors.Add(error);
                System.Diagnostics.Debug.WriteLine(error);
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex.StackTrace}");
                
                return (false, migratedCount, errors);
            }
        }

        /// <summary>
        /// 备份原始JSON文件
        /// Backup original JSON files
        /// </summary>
        /// <param name="originalPath">原始路径</param>
        private async Task BackupOriginalJsonFilesAsync(string originalPath)
        {
            try
            {
                var backupPath = Path.Combine(FileSystem.AppDataDirectory, "Recipes_Backup_JSON");
                
                if (Directory.Exists(backupPath))
                {
                    Directory.Delete(backupPath, true);
                }
                
                Directory.CreateDirectory(backupPath);

                // 复制所有文件到备份目录
                // Copy all files to backup directory
                var files = Directory.GetFiles(originalPath);
                foreach (var file in files)
                {
                    var fileName = Path.GetFileName(file);
                    var destFile = Path.Combine(backupPath, fileName);
                    File.Copy(file, destFile, true);
                }

                // 创建备份说明文件
                // Create backup description file
                var readmeContent = $@"JSON数据备份说明
Backup Description for JSON Data

备份时间 / Backup Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
原始路径 / Original Path: {originalPath}
备份文件数量 / Number of Backup Files: {files.Length}

这些文件是从JSON存储系统迁移到SQLite数据库之前的备份。
These files are backups from before migrating from JSON storage system to SQLite database.

如果需要恢复数据，请联系开发人员。
If you need to restore data, please contact the developer.
";

                await File.WriteAllTextAsync(Path.Combine(backupPath, "README.txt"), readmeContent);
                
                System.Diagnostics.Debug.WriteLine($"原始JSON文件已备份到: {backupPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"备份原始JSON文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否需要进行数据迁移
        /// Check if data migration is needed
        /// </summary>
        /// <returns>是否需要迁移</returns>
        public async Task<bool> IsMigrationNeededAsync()
        {
            try
            {
                // 检查是否有旧的JSON文件
                // Check if there are old JSON files
                var oldJsonPath = Path.Combine(FileSystem.AppDataDirectory, "Recipes");
                if (!Directory.Exists(oldJsonPath))
                {
                    return false;
                }

                var jsonFiles = Directory.GetFiles(oldJsonPath, "recipe_*.json");
                if (jsonFiles.Length == 0)
                {
                    return false;
                }

                // 检查数据库是否为空
                // Check if database is empty
                var existingRecipes = await _storageService.GetAllRecipesAsync();
                return existingRecipes.Count == 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查迁移需求失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取迁移统计信息
        /// Get migration statistics
        /// </summary>
        /// <returns>迁移统计信息</returns>
        public async Task<(int JsonFileCount, int DatabaseRecipeCount)> GetMigrationStatsAsync()
        {
            try
            {
                // 统计JSON文件数量
                // Count JSON files
                var oldJsonPath = Path.Combine(FileSystem.AppDataDirectory, "Recipes");
                var jsonFileCount = 0;
                if (Directory.Exists(oldJsonPath))
                {
                    jsonFileCount = Directory.GetFiles(oldJsonPath, "recipe_*.json").Length;
                }

                // 统计数据库中的菜谱数量
                // Count recipes in database
                var recipes = await _storageService.GetAllRecipesAsync();
                var databaseRecipeCount = recipes.Count;

                return (jsonFileCount, databaseRecipeCount);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取迁移统计信息失败: {ex.Message}");
                return (0, 0);
            }
        }
    }
}
