<?xml version="1.0" encoding="utf-8" ?>
<!--
    通知设置视图 / Notification Settings View
    用于配置应用程序通知偏好和提醒设置
    Used to configure application notification preferences and reminder settings
-->
<ContentPage x:Class="Scheduler.Views.NotificationSettingsView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:DataType="viewmodels:NotificationSettingsViewModel"
             Title="{Binding LocalizedTexts.NotificationSettings}">

    <!-- 页面内容 / Page content -->
    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!-- 加载指示器 / Loading indicator -->
            <ActivityIndicator IsVisible="{Binding IsLoading}" IsRunning="{Binding IsLoading}" />

            <!-- 状态消息 / Status message -->
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource StringToBoolConverter}}"
                   TextColor="Blue"
                   FontSize="14"
                   HorizontalOptions="Center" />

            <!-- 通知总开关区域 / Main notification toggle section -->
            <Frame BackgroundColor="#E3F2FD" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.MainNotificationToggle}" FontSize="18" FontAttributes="Bold" TextColor="#1976D2" />

                    <!-- 启用通知 / Enable notifications -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableNotifications}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsNotificationEnabled}" />
                    </Grid>

                    <!-- 启用声音 / Enable sound -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableSound}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsSoundEnabled}" />
                    </Grid>

                    <!-- 启用振动 / Enable vibration -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableVibration}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsVibrationEnabled}" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 班次提醒设置 / Shift reminder settings -->
            <Frame BackgroundColor="#E8F5E8" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.ShiftReminderSettings}" FontSize="18" FontAttributes="Bold" TextColor="#388E3C" />

                    <!-- 启用班次提醒 / Enable shift reminders -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableShiftReminders}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsShiftReminderEnabled}" />
                    </Grid>

                    <!-- 提前提醒时间 / Advance reminder time -->
                    <StackLayout>
                        <Label Text="{Binding LocalizedTexts.AdvanceReminderTime}" />
                        <Picker ItemsSource="{Binding ShiftReminderOptions}"
                                ItemDisplayBinding="{Binding Display}"
                                SelectedIndex="{Binding Settings.ShiftReminderMinutes, Converter={StaticResource MinutesToIndexConverter}}" />
                    </StackLayout>
                    
                    <!-- 启用每日工作安排 / Enable daily schedule -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableDailyScheduleNotifications}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsDailyScheduleEnabled}" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 休息提醒设置 / Break reminder settings -->
            <Frame BackgroundColor="#FFF3E0" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.BreakReminderSettings}" FontSize="18" FontAttributes="Bold" TextColor="#F57C00" />

                    <!-- 启用休息提醒 / Enable break reminders -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableBreakReminders}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsBreakReminderEnabled}" />
                    </Grid>

                    <!-- 提醒间隔 / Reminder interval -->
                    <StackLayout>
                        <Label Text="{Binding LocalizedTexts.ReminderInterval}" />
                        <Picker ItemsSource="{Binding BreakReminderOptions}"
                                ItemDisplayBinding="{Binding Display}"
                                SelectedIndex="{Binding Settings.BreakReminderIntervalHours, Converter={StaticResource HoursToIndexConverter}}" />
                    </StackLayout>
                    
                    <!-- 仅在工作时间提醒 / Only during work hours -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.OnlyDuringWorkHours}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.BreakReminderOnlyDuringWork}" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 薪资提醒设置 / Payment reminder settings -->
            <Frame BackgroundColor="#F3E5F5" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.PaymentReminderSettings}" FontSize="18" FontAttributes="Bold" TextColor="#7B1FA2" />

                    <!-- 启用薪资提醒 / Enable payment reminders -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnablePaymentReminders}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsPaymentReminderEnabled}" />
                    </Grid>

                    <!-- 提前提醒天数 / Advance reminder days -->
                    <StackLayout>
                        <Label Text="{Binding LocalizedTexts.AdvanceReminderDays}" />
                        <Picker ItemsSource="{Binding PaymentReminderOptions}"
                                ItemDisplayBinding="{Binding Display}"
                                SelectedIndex="{Binding Settings.PaymentReminderDaysBefore, Converter={StaticResource DaysToIndexConverter}}" />
                    </StackLayout>
                    
                    <!-- 启用薪资确认提醒 / Enable payment confirmation reminders -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnablePaymentConfirmationReminders}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsPaymentConfirmationEnabled}" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 免打扰时间设置 / Quiet hours settings -->
            <Frame BackgroundColor="#FFEBEE" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.QuietHoursSettings}" FontSize="18" FontAttributes="Bold" TextColor="#D32F2F" />

                    <!-- 启用免打扰时间 / Enable quiet hours -->
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Text="{Binding LocalizedTexts.EnableQuietHours}" VerticalOptions="Center" />
                        <Switch Grid.Column="1" IsToggled="{Binding Settings.IsQuietHoursEnabled}" />
                    </Grid>

                    <!-- 开始时间 / Start time -->
                    <StackLayout IsVisible="{Binding Settings.IsQuietHoursEnabled}">
                        <Label Text="{Binding LocalizedTexts.StartTime}" />
                        <Grid ColumnDefinitions="*,*">
                            <Picker Grid.Column="0" 
                                    ItemsSource="{Binding HourOptions}"
                                    SelectedItem="{Binding Settings.QuietHoursStartHour}"
                                    Title="{Binding LocalizedTexts.Hour}" />
                            <Picker Grid.Column="1"
                                    ItemsSource="{Binding MinuteOptions}"
                                    SelectedItem="{Binding Settings.QuietHoursStartMinute}"
                                    Title="{Binding LocalizedTexts.Minute}" />
                        </Grid>
                    </StackLayout>
                    
                    <!-- 结束时间 / End time -->
                    <StackLayout IsVisible="{Binding Settings.IsQuietHoursEnabled}">
                        <Label Text="{Binding LocalizedTexts.EndTime}" />
                        <Grid ColumnDefinitions="*,*">
                            <Picker Grid.Column="0"
                                    ItemsSource="{Binding HourOptions}"
                                    SelectedItem="{Binding Settings.QuietHoursEndHour}"
                                    Title="{Binding LocalizedTexts.Hour}" />
                            <Picker Grid.Column="1"
                                    ItemsSource="{Binding MinuteOptions}"
                                    SelectedItem="{Binding Settings.QuietHoursEndMinute}"
                                    Title="{Binding LocalizedTexts.Minute}" />
                        </Grid>
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- 操作按钮区域 / Action buttons area -->
            <StackLayout Spacing="10">
                <!-- 保存设置按钮 / Save settings button -->
                <Button Text="{Binding LocalizedTexts.SaveSettings}"
                        Command="{Binding SaveSettingsCommand}"
                        BackgroundColor="#4CAF50"
                        TextColor="White"
                        CornerRadius="8" />

                <!-- 测试通知按钮 / Test notification button -->
                <Button Text="{Binding LocalizedTexts.TestNotification}"
                        Command="{Binding TestNotificationCommand}"
                        BackgroundColor="#2196F3"
                        TextColor="White"
                        CornerRadius="8" />

                <!-- 请求权限按钮 / Request permission button -->
                <Button Text="{Binding LocalizedTexts.RequestNotificationPermission}"
                        Command="{Binding RequestPermissionCommand}"
                        BackgroundColor="#FF9800"
                        TextColor="White"
                        CornerRadius="8" />

                <!-- 重置为默认按钮 / Reset to default button -->
                <Button Text="{Binding LocalizedTexts.ResetToDefaultSettings}"
                        Command="{Binding ResetToDefaultCommand}"
                        BackgroundColor="#F44336"
                        TextColor="White" 
                        CornerRadius="8" />
            </StackLayout>
            
        </StackLayout>
    </ScrollView>
</ContentPage>
