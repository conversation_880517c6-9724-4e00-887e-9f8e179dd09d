// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 通知历史视图 / Notification History View
/// 显示应用程序发送的通知历史记录
/// Display history of notifications sent by the application
/// </summary>
public partial class NotificationHistoryView : ContentPage
{
    // 通知历史视图模型实例 / Notification history view model instance
    private readonly NotificationHistoryViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化通知历史视图并配置数据绑定 / Initialize notification history view and configure data binding
    /// </summary>
    /// <param name="viewModel">通知历史视图模型 / Notification history view model</param>
    public NotificationHistoryView(NotificationHistoryViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载通知历史记录和相关班次数据 / Load notification history records and related shift data
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        System.Diagnostics.Debug.WriteLine("NotificationHistoryView: 页面出现，开始加载通知历史和班次数据 / Page appearing, starting to load notification history and shift data");

        // 加载通知历史和班次数据 / Load notification history and shift data
        await _viewModel.LoadNotificationsCommand.ExecuteAsync(null);
        await _viewModel.LoadShiftsWithNotificationsCommand.ExecuteAsync(null);
    }
}
