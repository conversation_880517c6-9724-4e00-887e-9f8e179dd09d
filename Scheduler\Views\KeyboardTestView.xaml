<?xml version="1.0" encoding="utf-8" ?>
<!--
    键盘测试视图 / Keyboard Test View
    用于测试不同类型键盘输入和焦点管理的调试页面
    Debug page for testing different keyboard input types and focus management
-->
<ContentPage x:Class="Scheduler.Views.KeyboardTestView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="{Binding KeyboardTestTitle}">

    <ScrollView>
        <VerticalStackLayout Spacing="20" Margin="20">

            <!-- 页面标题区域 / Page title area -->
            <Frame BackgroundColor="#007ACC"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <Label Text="{Binding KeyboardTestHeader}"
                       FontSize="24"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"/>
            </Frame>

            <!-- 输入框测试区域 / Entry test area -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="{Binding SimplifiedEntryTest}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>

                    <!-- 基本文本输入 / Basic text input -->
                    <Entry x:Name="BasicTextEntry"
                           Placeholder="{Binding BasicTextInput}"
                           Keyboard="Default"
                           ReturnType="Next"
                           Focused="OnEntryFocused"/>

                    <!-- 数字输入 / Numeric input -->
                    <Entry x:Name="NumericEntry"
                           Placeholder="{Binding NumericInput}"
                           Keyboard="Numeric"
                           ReturnType="Next"
                           Focused="OnEntryFocused"/>

                    <!-- 邮箱输入 -->
                    <Entry x:Name="EmailEntry"
                           Placeholder="{Binding EmailInput}"
                           IsEnabled="True"
                           IsReadOnly="False"
                           InputTransparent="False"
                           Keyboard="Email"
                           ReturnType="Done"
                           Focused="OnEntryFocused"/>

                    <!-- 多行文本输入 -->
                    <Editor x:Name="BasicEditor"
                            Placeholder="{Binding MultilineTextInput}"
                            HeightRequest="100"
                            IsEnabled="True"
                            IsReadOnly="False"
                            InputTransparent="False"
                            Focused="OnEditorFocused"/>
                </VerticalStackLayout>
            </Frame>

            <!-- 测试结果显示 -->
            <Frame BackgroundColor="#F8F9FA"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding TestInstructions}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#333"/>
                    <Label Text="{Binding TestInstruction1}"
                           FontSize="14"
                           TextColor="#666"/>
                    <Label Text="{Binding TestInstruction2}"
                           FontSize="14"
                           TextColor="#666"/>
                    <Label Text="{Binding TestInstruction3}"
                           FontSize="14"
                           TextColor="#666"/>
                </VerticalStackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
