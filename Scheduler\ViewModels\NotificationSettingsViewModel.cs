// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using static Scheduler.ViewModels.SetViewModel;  // 设置视图模型静态成员 / Settings view model static members

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 通知设置页面视图模型 / Notification Settings Page ViewModel
    /// 管理通知偏好设置和权限配置
    /// Manages notification preferences and permission configuration
    /// </summary>
    public partial class NotificationSettingsViewModel : ObservableObject
    {
        // 通知设置服务实例 / Notification settings service instance
        private readonly INotificationSettingsService _notificationSettingsService;
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 通知设置对象 / Notification settings object
        [ObservableProperty]
        private NotificationSettings _settings = new();

        // 加载状态 / Loading state
        [ObservableProperty]
        private bool _isLoading;

        // 状态消息 / Status message
        [ObservableProperty]
        private string _statusMessage = string.Empty;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化通知设置视图模型 / Initialize notification settings view model
        /// </summary>
        /// <param name="notificationSettingsService">通知设置服务 / Notification settings service</param>
        /// <param name="notificationService">通知服务 / Notification service</param>
        /// <param name="notificationScheduler">通知调度器 / Notification scheduler</param>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        public NotificationSettingsViewModel(
            INotificationSettingsService notificationSettingsService,
            INotificationService notificationService,
            NotificationScheduler notificationScheduler,
            LocalizationService localizationService)
        {
            _notificationSettingsService = notificationSettingsService;
            _notificationService = notificationService;
            _notificationScheduler = notificationScheduler;
            _localizationService = localizationService;
        }

        /// <summary>
        /// 本地化文本属性 / Localized texts property
        /// 提供界面显示的本地化文本 / Provides localized texts for UI display
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);

        /// <summary>
        /// 页面出现时加载设置
        /// </summary>
        [RelayCommand]
        private async Task LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.LoadingSettings;

                Settings = await _notificationSettingsService.GetSettingsAsync();
                StatusMessage = LocalizedTexts.SettingsLoadedSuccessfully;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.LoadSettingsFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"加载通知设置失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        [RelayCommand]
        private async Task SaveSettingsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.SavingSettings;

                var success = await _notificationSettingsService.SaveSettingsAsync(Settings);

                if (success)
                {
                    StatusMessage = LocalizedTexts.SettingsSavedSuccessfully;

                    // 重新安排所有通知
                    await _notificationScheduler.RescheduleAllNotificationsAsync();
                }
                else
                {
                    StatusMessage = LocalizedTexts.SaveSettingsFailed;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.SaveSettingsFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"保存通知设置失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        [RelayCommand]
        private async Task ResetToDefaultAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = LocalizedTexts.ResettingSettings;

                var success = await _notificationSettingsService.ResetToDefaultAsync();

                if (success)
                {
                    Settings = await _notificationSettingsService.GetSettingsAsync();
                    StatusMessage = LocalizedTexts.SettingsResetSuccessfully;

                    // 重新安排所有通知
                    await _notificationScheduler.RescheduleAllNotificationsAsync();
                }
                else
                {
                    StatusMessage = LocalizedTexts.ResetSettingsFailed;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.ResetSettingsFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"重置通知设置失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 测试通知
        /// </summary>
        [RelayCommand]
        private async Task TestNotificationAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.SendingTestNotification;

                // 检查通知权限
                var hasPermission = await _notificationService.CheckNotificationPermissionAsync();
                if (!hasPermission)
                {
                    var granted = await _notificationService.RequestNotificationPermissionAsync();
                    if (!granted)
                    {
                        StatusMessage = LocalizedTexts.NotificationPermissionDeniedCannotSendTest;
                        return;
                    }
                }

                // 发送测试通知
                await _notificationService.SendBreakReminderAsync(LocalizedTexts.TestNotificationMessage);
                StatusMessage = LocalizedTexts.TestNotificationSent;
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.SendTestNotificationFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"发送测试通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 请求通知权限
        /// </summary>
        [RelayCommand]
        private async Task RequestPermissionAsync()
        {
            try
            {
                StatusMessage = LocalizedTexts.RequestingNotificationPermission;

                var granted = await _notificationService.RequestNotificationPermissionAsync();

                if (granted)
                {
                    StatusMessage = LocalizedTexts.NotificationPermissionGranted;
                }
                else
                {
                    StatusMessage = LocalizedTexts.NotificationPermissionDeniedManuallyEnable;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = string.Format(LocalizedTexts.RequestNotificationPermissionFailedFormat, ex.Message);
                System.Diagnostics.Debug.WriteLine($"请求通知权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除状态消息
        /// </summary>
        [RelayCommand]
        private void ClearStatusMessage()
        {
            StatusMessage = string.Empty;
        }

        /// <summary>
        /// 班次提醒时间选项
        /// </summary>
        public List<(int Minutes, string Display)> ShiftReminderOptions => new()
        {
            (5, LocalizedTexts.FiveMinutesBefore),
            (10, LocalizedTexts.TenMinutesBefore),
            (15, LocalizedTexts.FifteenMinutesBefore),
            (30, LocalizedTexts.ThirtyMinutesBefore),
            (60, LocalizedTexts.OneHourBefore)
        };

        /// <summary>
        /// 休息提醒间隔选项
        /// </summary>
        public List<(int Hours, string Display)> BreakReminderOptions => new()
        {
            (1, LocalizedTexts.EveryOneHour),
            (2, LocalizedTexts.EveryTwoHours),
            (3, LocalizedTexts.EveryThreeHours),
            (4, LocalizedTexts.EveryFourHours)
        };

        /// <summary>
        /// 支付提醒提前天数选项
        /// </summary>
        public List<(int Days, string Display)> PaymentReminderOptions => new()
        {
            (0, LocalizedTexts.SameDay),
            (1, LocalizedTexts.OneDayBefore),
            (2, LocalizedTexts.TwoDaysBefore),
            (3, LocalizedTexts.ThreeDaysBefore),
            (7, LocalizedTexts.OneWeekBefore)
        };

        /// <summary>
        /// 小时选项（用于免打扰时间设置）
        /// </summary>
        public List<int> HourOptions { get; } = Enumerable.Range(0, 24).ToList();

        /// <summary>
        /// 分钟选项（用于免打扰时间设置）
        /// </summary>
        public List<int> MinuteOptions { get; } = new() { 0, 15, 30, 45 };
    }
}
