<?xml version="1.0" encoding="utf-8" ?>
<!--
    付款待办视图 / Payment Todo View
    显示和管理待确认的付款记录和工资支付事项
    Display and manage pending payment records and salary payment items
-->
<ContentPage x:Class="Scheduler.Views.PaymentTodoView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             x:DataType="viewmodels:PaymentTodoViewModel"
             Title="{Binding Title}">

    <!-- 下拉刷新容器 / Pull-to-refresh container -->
    <RefreshView Command="{Binding RefreshCommand}"
                 IsRefreshing="{Binding IsBusy}">
        <ScrollView>
            <VerticalStackLayout Spacing="15" Padding="20">

                <!-- 支付统计信息卡片 / Payment statistics card -->
                <Frame BackgroundColor="#4CAF50"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <Grid RowDefinitions="Auto,Auto,Auto" ColumnDefinitions="*,*">
                        <!-- 统计标题 / Statistics title -->
                        <Label Grid.Row="0" Grid.ColumnSpan="2"
                               Text="{Binding LocalizedTexts.PaymentTodoStatistics}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="White"
                               HorizontalOptions="Center"
                               Margin="0,0,0,15"/>

                        <!-- 待确认数量统计 / Pending confirmation count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="0" HorizontalOptions="Center">
                            <Label Text="{Binding PendingCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="{Binding LocalizedTexts.PendingConfirmationCount}"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <!-- 逾期数量统计 / Overdue count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="1" HorizontalOptions="Center">
                            <Label Text="{Binding OverdueCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="#FF5722"
                                   HorizontalOptions="Center"/>
                            <Label Text="{Binding LocalizedTexts.OverdueCount}"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <!-- 金额统计 -->
                        <VerticalStackLayout Grid.Row="2" Grid.Column="0" HorizontalOptions="Center" Margin="0,10,0,0">
                            <Label Text="{Binding TotalPendingAmount, StringFormat='${0:F2}'}"
                                   FontSize="20"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="{Binding LocalizedTexts.PendingAmount}"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <VerticalStackLayout Grid.Row="2" Grid.Column="1" HorizontalOptions="Center" Margin="0,10,0,0">
                            <Label Text="{Binding TotalConfirmedAmount, StringFormat='${0:F2}'}"
                                   FontSize="20"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="{Binding LocalizedTexts.ConfirmedAmount}"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>
                    </Grid>
                </Frame>

                <!-- 操作按钮 -->
                <Grid ColumnDefinitions="*,*">
                    <Button Grid.Column="0"
                            Text="{Binding ShowCompletedItems, Converter={StaticResource BoolToTextConverter}, ConverterParameter='Hide Completed|Show Completed'}"
                            Command="{Binding ToggleShowCompletedCommand}"
                            BackgroundColor="#FF9800"
                            TextColor="White"
                            CornerRadius="8"
                            Margin="0,0,5,0"/>

                    <Button Grid.Column="1"
                            Text="{Binding LocalizedTexts.Refresh}"
                            Command="{Binding RefreshCommand}"
                            BackgroundColor="#607D8B"
                            TextColor="White"
                            CornerRadius="8"
                            Margin="5,0,0,0"/>
                </Grid>

                <!-- 逾期支付待办事项 -->
                <Frame BackgroundColor="#FFEBEE"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="15"
                       IsVisible="{Binding OverdueCount, Converter={StaticResource IntToBoolConverter}}">
                    <VerticalStackLayout Spacing="10">
                        <Label Text="{Binding LocalizedTexts.OverduePayments}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#D32F2F"/>

                        <CollectionView ItemsSource="{Binding OverduePaymentTodos}"
                                        EmptyView="{Binding LocalizedTexts.NoOverduePayments}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:TodoItem">
                                    <Frame BackgroundColor="#FFCDD2"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                            <!-- 紧急标记 -->
                                            <Label Grid.Column="0"
                                                   Text="⚠️"
                                                   FontSize="20"
                                                   VerticalOptions="Center"/>

                                            <!-- 支付信息 -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="3" Margin="10,0,0,0">
                                                <Label Text="{Binding Title}"
                                                       FontSize="16"
                                                       FontAttributes="Bold"
                                                       TextColor="#D32F2F"/>
                                                <Label Text="{Binding Description}"
                                                       FontSize="12"
                                                       TextColor="#666"
                                                       MaxLines="2"/>
                                                <Label Text="{Binding StatusText}"
                                                       FontSize="12"
                                                       TextColor="#D32F2F"
                                                       FontAttributes="Bold"/>
                                            </VerticalStackLayout>

                                            <!-- 查看详情按钮 -->
                                            <Button Grid.Column="2"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.ViewDetails}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=ViewPaymentDetailsCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#2196F3"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    Margin="5,0,5,0"
                                                    VerticalOptions="Center"/>

                                            <!-- 确认支付按钮 -->
                                            <Button Grid.Column="3"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.Confirm}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=ConfirmPaymentTodoCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#4CAF50"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- 待确认支付待办事项 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="15">
                    <VerticalStackLayout Spacing="10">
                        <Label Text="{Binding LocalizedTexts.PendingPayments}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <CollectionView ItemsSource="{Binding PendingPaymentTodos}"
                                        EmptyView="{Binding LocalizedTexts.NoPendingPayments}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:TodoItem">
                                    <Frame BackgroundColor="#F8F9FA"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                            <!-- 支付图标 -->
                                            <Label Grid.Column="0"
                                                   Text="💰"
                                                   FontSize="20"
                                                   VerticalOptions="Center"/>

                                            <!-- 支付信息 -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="3" Margin="10,0,0,0">
                                                <Label Text="{Binding Title}"
                                                       FontSize="16"
                                                       FontAttributes="Bold"
                                                       TextColor="#333"/>
                                                <Label Text="{Binding Description}"
                                                       FontSize="12"
                                                       TextColor="#666"
                                                       MaxLines="2"/>
                                                <Label Text="{Binding StatusText}"
                                                       FontSize="12"
                                                       TextColor="{Binding StatusColor}"
                                                       FontAttributes="Bold"/>
                                            </VerticalStackLayout>

                                            <!-- 查看详情按钮 -->
                                            <Button Grid.Column="2"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.ViewDetails}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=ViewPaymentDetailsCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#2196F3"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    Margin="5,0,5,0"
                                                    VerticalOptions="Center"/>

                                            <!-- 确认支付按钮 -->
                                            <Button Grid.Column="3"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.Confirm}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=ConfirmPaymentTodoCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#4CAF50"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- 已确认支付待办事项 -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="15"
                       IsVisible="{Binding ShowCompletedItems}">
                    <VerticalStackLayout Spacing="10">
                        <Label Text="{Binding LocalizedTexts.ConfirmedPayments}"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <CollectionView ItemsSource="{Binding CompletedPaymentTodos}"
                                        EmptyView="{Binding LocalizedTexts.NoConfirmedPayments}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:TodoItem">
                                    <Frame BackgroundColor="#E8F5E8"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                            <!-- 完成标记 -->
                                            <Label Grid.Column="0"
                                                   Text="✅"
                                                   FontSize="20"
                                                   VerticalOptions="Center"/>

                                            <!-- 支付信息 -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="3" Margin="10,0,0,0">
                                                <Label Text="{Binding Title}"
                                                       FontSize="16"
                                                       TextColor="#333"
                                                       TextDecorations="Strikethrough"/>
                                                <Label Text="{Binding Description}"
                                                       FontSize="12"
                                                       TextColor="#666"
                                                       MaxLines="1"/>
                                                <Label Text="{Binding CompletedDate, StringFormat='Completed: {0:MM/dd HH:mm}'}"
                                                       FontSize="12"
                                                       TextColor="#4CAF50"/>
                                            </VerticalStackLayout>

                                            <!-- 查看详情按钮 -->
                                            <Button Grid.Column="2"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.ViewDetails}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=ViewPaymentDetailsCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#2196F3"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    Margin="5,0,5,0"
                                                    VerticalOptions="Center"/>

                                            <!-- 取消确认按钮 -->
                                            <Button Grid.Column="3"
                                                    Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=LocalizedTexts.Cancel}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:PaymentTodoViewModel}}, Path=CancelPaymentConfirmationCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#FF5722"
                                                    TextColor="White"
                                                    CornerRadius="6"
                                                    FontSize="12"
                                                    VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsVisible="{Binding IsBusy}"
                                   IsRunning="{Binding IsBusy}"
                                   Color="#4CAF50"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   Margin="20"/>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>
