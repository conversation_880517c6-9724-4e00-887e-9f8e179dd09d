<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Settings Page -->
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="WorkSettings" xml:space="preserve">
    <value>Work Settings</value>
  </data>
  <data name="LogConfirmation" xml:space="preserve">
    <value>Log Confirmation</value>
  </data>
  <data name="LogConfirmationDesc" xml:space="preserve">
    <value>Enable work log confirmation</value>
  </data>
  <data name="PayConfirmation" xml:space="preserve">
    <value>Pay Confirmation</value>
  </data>
  <data name="PayConfirmationDesc" xml:space="preserve">
    <value>Enable payment confirmation</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="WorkReminders" xml:space="preserve">
    <value>Work Reminders</value>
  </data>
  <data name="WorkRemindersDesc" xml:space="preserve">
    <value>Get notified before shifts start</value>
  </data>
  <data name="BreakReminders" xml:space="preserve">
    <value>Break Reminders</value>
  </data>
  <data name="BreakRemindersDesc" xml:space="preserve">
    <value>Get reminded to take breaks during work</value>
  </data>
  <data name="PaymentNotifications" xml:space="preserve">
    <value>Payment Notifications</value>
  </data>
  <data name="PaymentNotificationsDesc" xml:space="preserve">
    <value>Get notified about payment confirmations</value>
  </data>
  <data name="DisplayTime" xml:space="preserve">
    <value>Display &amp; Time</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Time Zone</value>
  </data>
  <data name="TimeFormat" xml:space="preserve">
    <value>Time Format</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="SystemIntegration" xml:space="preserve">
    <value>System Integration</value>
  </data>
  <data name="AlarmSettings" xml:space="preserve">
    <value>Alarm Settings</value>
  </data>
  <data name="AlarmSettingsDesc" xml:space="preserve">
    <value>Link to phone alarm app</value>
  </data>
  <data name="ExportToCalendar" xml:space="preserve">
    <value>Export to Calendar</value>
  </data>
  <data name="ExportToCalendarDesc" xml:space="preserve">
    <value>Sync with system calendar</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>Data Management</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>Export Data</value>
  </data>
  <data name="ExportDataDesc" xml:space="preserve">
    <value>Export work data to file</value>
  </data>
  <data name="BackupSync" xml:space="preserve">
    <value>Backup &amp; Sync</value>
  </data>
  <data name="BackupSyncDesc" xml:space="preserve">
    <value>Cloud backup settings</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>About App</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version {0}</value>
  </data>
  
  <!-- Messages -->
  <data name="SettingsUpdated" xml:space="preserve">
    <value>Settings Updated</value>
  </data>
  <data name="NotificationSettings" xml:space="preserve">
    <value>Notification Settings</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>enabled</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>disabled</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="SelectTimeZone" xml:space="preserve">
    <value>Select Time Zone</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Select Language</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Hour24" xml:space="preserve">
    <value>24 Hour</value>
  </data>
  <data name="Hour12" xml:space="preserve">
    <value>12 Hour</value>
  </data>

  <!-- Employer Management -->
  <data name="EmployerManagement" xml:space="preserve">
    <value>Employer Management</value>
  </data>
  <data name="AddEmployer" xml:space="preserve">
    <value>Add Employer</value>
  </data>
  <data name="EditEmployer" xml:space="preserve">
    <value>Edit Employer</value>
  </data>
  <data name="ViewAllEmployers" xml:space="preserve">
    <value>View All Employers</value>
  </data>
  <data name="EmployerName" xml:space="preserve">
    <value>Employer Name</value>
  </data>
  <data name="EmployerNameRequired" xml:space="preserve">
    <value>Employer Name *</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>Contact Info</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>Work Location</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>Hourly Rate ($/hour)</value>
  </data>
  <data name="HourlyRateRequired" xml:space="preserve">
    <value>Hourly Rate ($/hour) *</value>
  </data>
  <data name="PaymentCycle" xml:space="preserve">
    <value>Payment Cycle (days)</value>
  </data>
  <data name="IdentificationColor" xml:space="preserve">
    <value>Identification Color</value>
  </data>
  <data name="EmploymentStatus" xml:space="preserve">
    <value>Employment Status</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="BasicInfo" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="SalaryInfo" xml:space="preserve">
    <value>Salary Information</value>
  </data>
  <data name="DisplaySettings" xml:space="preserve">
    <value>Display Settings</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="ReturnHome" xml:space="preserve">
    <value>Return Home</value>
  </data>
  <data name="NoEmployersFound" xml:space="preserve">
    <value>No employers found</value>
  </data>
  <data name="AddFirstEmployer" xml:space="preserve">
    <value>Click 'Add Employer' button to add your first employer</value>
  </data>
  <data name="SearchEmployers" xml:space="preserve">
    <value>Search employer name, contact info or work location...</value>
  </data>
  <data name="FillEmployerInfo" xml:space="preserve">
    <value>Fill in employer basic information</value>
  </data>
  <data name="EnterEmployerName" xml:space="preserve">
    <value>Please enter employer name</value>
  </data>
  <data name="EnterContactInfo" xml:space="preserve">
    <value>Phone, email or other contact information</value>
  </data>
  <data name="EnterWorkLocation" xml:space="preserve">
    <value>Please enter work location</value>
  </data>
  <data name="EnterNotes" xml:space="preserve">
    <value>Please enter notes...</value>
  </data>

  <!-- Validation Messages -->
  <data name="ValidationFailed" xml:space="preserve">
    <value>Validation Failed</value>
  </data>
  <data name="PleaseEnterEmployerName" xml:space="preserve">
    <value>Please enter employer name</value>
  </data>
  <data name="PleaseEnterValidHourlyRate" xml:space="preserve">
    <value>Please enter valid hourly rate</value>
  </data>
  <data name="PleaseEnterValidOvertimeMultiplier" xml:space="preserve">
    <value>Please enter valid overtime multiplier</value>
  </data>
  <data name="PleaseEnterValidPaymentCycle" xml:space="preserve">
    <value>Please enter valid payment cycle days</value>
  </data>

  <!-- Success Messages -->
  <data name="SaveSuccess" xml:space="preserve">
    <value>Save Success</value>
  </data>
  <data name="EmployerInfoUpdated" xml:space="preserve">
    <value>Employer information has been updated</value>
  </data>
  <data name="NewEmployerAdded" xml:space="preserve">
    <value>New employer has been added</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>Delete Success</value>
  </data>
  <data name="EmployerDeleted" xml:space="preserve">
    <value>Employer "{0}" has been deleted</value>
  </data>

  <!-- Error Messages -->
  <data name="SaveFailed" xml:space="preserve">
    <value>Save Failed</value>
  </data>
  <data name="LoadFailed" xml:space="preserve">
    <value>Load Failed</value>
  </data>
  <data name="DeleteFailed" xml:space="preserve">
    <value>Delete Failed</value>
  </data>
  <data name="InitializationFailed" xml:space="preserve">
    <value>Initialization Failed</value>
  </data>

  <!-- Confirmation Dialogs -->
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="ConfirmDeleteEmployer" xml:space="preserve">
    <value>Are you sure you want to delete employer "{0}"?

Note: This will soft delete the employer, related shifts and work records will be preserved.</value>
  </data>

  <!-- Pay Page Employer Directory -->
  <data name="EmployerDirectory" xml:space="preserve">
    <value>Employer Directory</value>
  </data>
  <data name="NoEmployersFoundInPay" xml:space="preserve">
    <value>No employers found</value>
  </data>
  <data name="UnknownEmployer" xml:space="preserve">
    <value>Unknown Employer</value>
  </data>

  <!-- Pay Page Additional Texts -->
  <data name="FiveYearIncomeHistory" xml:space="preserve">
    <value>5-Year Income History</value>
  </data>
  <data name="PaymentTodoList" xml:space="preserve">
    <value>Payment Todo List</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="TotalIncome" xml:space="preserve">
    <value>Total Income</value>
  </data>
  <data name="NoPaymentRecords" xml:space="preserve">
    <value>No payment records</value>
  </data>
  <data name="Toggle" xml:space="preserve">
    <value>Toggle</value>
  </data>

  <!-- Login Page -->
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Remember me</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Forgot Password?</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="WorkManagementSystem" xml:space="preserve">
    <value>Work Management System</value>
  </data>

  <!-- TodoList Page -->
  <data name="SyncStatus" xml:space="preserve">
    <value>Sync Status</value>
  </data>
  <data name="ToggleView" xml:space="preserve">
    <value>Toggle View</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="NoPendingTodoItems" xml:space="preserve">
    <value>No pending todo items</value>
  </data>
  <data name="RecentlyCompletedTasks" xml:space="preserve">
    <value>Recently Completed Tasks</value>
  </data>

  <!-- AllEmployer Page -->
  <data name="EmployerManagement" xml:space="preserve">
    <value>Employer Management</value>
  </data>
  <data name="AddEmployer" xml:space="preserve">
    <value>Add Employer</value>
  </data>
  <data name="NoEmployerInfo" xml:space="preserve">
    <value>No employer information</value>
  </data>
  <data name="ClickAddEmployerToStart" xml:space="preserve">
    <value>Click 'Add Employer' button to add your first employer</value>
  </data>
  <data name="EditSelected" xml:space="preserve">
    <value>Edit Selected</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>Delete Selected</value>
  </data>
  <data name="BackToHome" xml:space="preserve">
    <value>Back to Home</value>
  </data>
  <data name="EditSelected" xml:space="preserve">
    <value>Edit Selected</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>Delete Selected</value>
  </data>
  <data name="RecentlyConfirmed" xml:space="preserve">
    <value>Recently Confirmed (Latest 5)</value>
  </data>
  <data name="HourlyRateFormat" xml:space="preserve">
    <value>${0:F2}/hour</value>
  </data>

  <!-- PayView Page -->
  <data name="ManageEmployers" xml:space="preserve">
    <value>Manage Employers</value>
  </data>
  <data name="PendingPayments" xml:space="preserve">
    <value>Pending Payments</value>
  </data>
  <data name="RecentlyConfirmed" xml:space="preserve">
    <value>Recently Confirmed (Latest 5)</value>
  </data>

  <!-- Common Actions -->
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>

  <!-- CalendarView -->
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>

  <!-- SetView -->
  <data name="WorkSettings" xml:space="preserve">
    <value>Work Settings</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="ShiftReminderTime" xml:space="preserve">
    <value>Shift Reminder Time</value>
  </data>
  <data name="ShiftReminderDescription" xml:space="preserve">
    <value>How many minutes before shift starts</value>
  </data>
  <data name="MinutesFormat" xml:space="preserve">
    <value>{0} minutes</value>
  </data>
  <data name="DailySchedule" xml:space="preserve">
    <value>Daily Schedule</value>
  </data>
  <data name="DailyScheduleDescription" xml:space="preserve">
    <value>Send daily work schedule every morning</value>
  </data>
  <data name="DailyNotificationTime" xml:space="preserve">
    <value>Daily Notification Time</value>
  </data>
  <data name="DailyNotificationDescription" xml:space="preserve">
    <value>Time to send daily work schedule</value>
  </data>
  <data name="BreakReminderInterval" xml:space="preserve">
    <value>Break Reminder Interval</value>
  </data>
  <data name="BreakReminderDescription" xml:space="preserve">
    <value>How often to remind for breaks</value>
  </data>
  <data name="HoursFormat" xml:space="preserve">
    <value>{0} hours</value>
  </data>
  <data name="NotificationBehavior" xml:space="preserve">
    <value>Notification Behavior</value>
  </data>
  <data name="NotificationSound" xml:space="preserve">
    <value>Notification Sound</value>
  </data>
  <data name="NotificationSoundDescription" xml:space="preserve">
    <value>Play notification alert sound</value>
  </data>
  <data name="VibrationReminder" xml:space="preserve">
    <value>Vibration Reminder</value>
  </data>
  <data name="VibrationDescription" xml:space="preserve">
    <value>Vibrate device when notifying</value>
  </data>
  <data name="QuietHours" xml:space="preserve">
    <value>Quiet Hours</value>
  </data>
  <data name="QuietHoursDescription" xml:space="preserve">
    <value>Mute notifications during specified hours</value>
  </data>
  <data name="QuietHoursPeriod" xml:space="preserve">
    <value>Quiet Hours Period</value>
  </data>
  <data name="QuietHoursPeriodDescription" xml:space="preserve">
    <value>Set silent time range</value>
  </data>
  <data name="PermissionManagement" xml:space="preserve">
    <value>Permission Management</value>
  </data>
  <data name="NotificationPermission" xml:space="preserve">
    <value>Notification Permission</value>
  </data>
  <data name="NotificationPermissionDescription" xml:space="preserve">
    <value>Allow app to send notification reminders</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>Check</value>
  </data>
  <data name="LocationPermission" xml:space="preserve">
    <value>Location Permission</value>
  </data>
  <data name="LocationPermissionDescription" xml:space="preserve">
    <value>For location-based work reminders</value>
  </data>
  <data name="OpenAppSettings" xml:space="preserve">
    <value>Open App Settings</value>
  </data>
  <data name="TestNotification" xml:space="preserve">
    <value>Test Notification Function</value>
  </data>
  <data name="DisplayTime" xml:space="preserve">
    <value>Display &amp; Time</value>
  </data>
  <data name="SystemIntegration" xml:space="preserve">
    <value>System Integration</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>Data Management</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>About App</value>
  </data>

  <!-- ShiftManagementView -->
  <data name="SmartTimeSelection" xml:space="preserve">
    <value>Smart Time Selection</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start time</value>
  </data>
  <data name="WorkHours" xml:space="preserve">
    <value>Work hours</value>
  </data>
  <data name="EndTimeAutoCalculated" xml:space="preserve">
    <value>End time (automatically calculated)</value>
  </data>
  <data name="SmartWorkPreview" xml:space="preserve">
    <value>Smart Work Preview</value>
  </data>
  <data name="JobPreview" xml:space="preserve">
    <value>Job preview</value>
  </data>
  <data name="WorkHoursLabel" xml:space="preserve">
    <value>Work hours</value>
  </data>
  <data name="HourlyRateLabel" xml:space="preserve">
    <value>Hourly rate</value>
  </data>
  <data name="EstimatedRevenue" xml:space="preserve">
    <value>Estimated revenue</value>
  </data>
  <data name="TimeRangeDisplay" xml:space="preserve">
    <value>Time Range Display</value>
  </data>
  <data name="RecurringShiftSettings" xml:space="preserve">
    <value>Recurring Shift Settings</value>
  </data>
  <data name="RecurringShiftTitleAndSwitch" xml:space="preserve">
    <value>Recurring Shift Title and Switch</value>
  </data>
  <data name="RecurringSettingsDetails" xml:space="preserve">
    <value>Recurring Settings Details</value>
  </data>
  <data name="CyclePeriod" xml:space="preserve">
    <value>Cycle period</value>
  </data>
  <data name="RecurringEndSettings" xml:space="preserve">
    <value>Recurring End Settings</value>
  </data>
  <data name="RecurringPreviewInfo" xml:space="preserve">
    <value>Recurring Preview Info</value>
  </data>
  <data name="NotificationSettings" xml:space="preserve">
    <value>Notification Settings</value>
  </data>
  <data name="RecurringShiftNotificationRestriction" xml:space="preserve">
    <value>⚠️ Recurring Shift Notification Restriction</value>
  </data>
  <data name="RecurringShiftNotificationMessage" xml:space="preserve">
    <value>Recurring shifts don't support notification settings. Please set notifications for individual shifts separately.</value>
  </data>
  <data name="NotificationSettingsTitleAndSwitch" xml:space="preserve">
    <value>Notification Settings Title and Switch</value>
  </data>
  <data name="NotificationSettingsDetails" xml:space="preserve">
    <value>Notification Settings Details</value>
  </data>
  <data name="ReminderTimeSelection" xml:space="preserve">
    <value>Reminder Time Selection</value>
  </data>
  <data name="ReminderTime" xml:space="preserve">
    <value>Reminder Time</value>
  </data>
  <data name="NotificationPreviewInfo" xml:space="preserve">
    <value>Notification Preview Info</value>
  </data>

  <!-- NotificationHistoryView -->
  <data name="NotificationHistory" xml:space="preserve">
    <value>Notification History</value>
  </data>
  <data name="ShiftNotificationManagement" xml:space="preserve">
    <value>🔔 Shift Notification Management</value>
  </data>
  <data name="TestNotification" xml:space="preserve">
    <value>📱 Test Notification</value>
  </data>
  <data name="NoShiftsWithNotifications" xml:space="preserve">
    <value>📅 No shifts with enabled notifications</value>
  </data>
  <data name="ReminderMinutesFormat" xml:space="preserve">
    <value>🔔 Remind {0} minutes in advance</value>
  </data>
  <data name="CancelNotification" xml:space="preserve">
    <value>❌ Cancel Notification</value>
  </data>
  <data name="RefreshData" xml:space="preserve">
    <value>🔄 Refresh Data</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>Search notification title or content...</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="NotificationType" xml:space="preserve">
    <value>Notification Type</value>
  </data>
  <data name="NotificationStatus" xml:space="preserve">
    <value>Notification Status</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>Clear Filters</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="NoNotificationRecords" xml:space="preserve">
    <value>No notification records</value>
  </data>
  <data name="TryAdjustingFilters" xml:space="preserve">
    <value>Try adjusting filter conditions or creating new notifications</value>
  </data>
  <data name="ScheduledTime" xml:space="preserve">
    <value>Scheduled time: </value>
  </data>
  <data name="ReadTime" xml:space="preserve">
    <value>Read time: </value>
  </data>
  <data name="MarkAsRead" xml:space="preserve">
    <value>Mark as Read</value>
  </data>
  <data name="Resend" xml:space="preserve">
    <value>Resend</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>

  <!-- NotificationSettingsView -->
  <data name="NotificationSettings" xml:space="preserve">
    <value>Notification Settings</value>
  </data>
  <data name="MainNotificationToggle" xml:space="preserve">
    <value>Main Notification Toggle</value>
  </data>
  <data name="EnableNotifications" xml:space="preserve">
    <value>Enable Notifications</value>
  </data>
  <data name="EnableSound" xml:space="preserve">
    <value>Enable Sound</value>
  </data>
  <data name="EnableVibration" xml:space="preserve">
    <value>Enable Vibration</value>
  </data>
  <data name="ShiftReminderSettings" xml:space="preserve">
    <value>Shift Reminder Settings</value>
  </data>
  <data name="EnableShiftReminders" xml:space="preserve">
    <value>Enable Shift Reminders</value>
  </data>
  <data name="AdvanceReminderTime" xml:space="preserve">
    <value>Advance Reminder Time</value>
  </data>
  <data name="EnableDailyScheduleNotifications" xml:space="preserve">
    <value>Enable Daily Schedule Notifications</value>
  </data>
  <data name="BreakReminderSettings" xml:space="preserve">
    <value>Break Reminder Settings</value>
  </data>
  <data name="EnableBreakReminders" xml:space="preserve">
    <value>Enable Break Reminders</value>
  </data>
  <data name="ReminderInterval" xml:space="preserve">
    <value>Reminder Interval</value>
  </data>
  <data name="OnlyDuringWorkHours" xml:space="preserve">
    <value>Only During Work Hours</value>
  </data>
  <data name="PaymentReminderSettings" xml:space="preserve">
    <value>Payment Reminder Settings</value>
  </data>
  <data name="EnablePaymentReminders" xml:space="preserve">
    <value>Enable Payment Reminders</value>
  </data>
  <data name="AdvanceReminderDays" xml:space="preserve">
    <value>Advance Reminder Days</value>
  </data>
  <data name="EnablePaymentConfirmationReminders" xml:space="preserve">
    <value>Enable Payment Confirmation Reminders</value>
  </data>
  <data name="QuietHoursSettings" xml:space="preserve">
    <value>Quiet Hours Settings</value>
  </data>
  <data name="EnableQuietHours" xml:space="preserve">
    <value>Enable Quiet Hours</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>Hour</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="SaveSettings" xml:space="preserve">
    <value>Save Settings</value>
  </data>
  <data name="RequestNotificationPermission" xml:space="preserve">
    <value>Request Notification Permission</value>
  </data>
  <data name="ResetToDefaultSettings" xml:space="preserve">
    <value>Reset to Default Settings</value>
  </data>

  <!-- NotificationTestView -->
  <data name="NotificationFunctionTest" xml:space="preserve">
    <value>Notification Function Test</value>
  </data>
  <data name="NotificationPermissionManagement" xml:space="preserve">
    <value>Notification Permission Management</value>
  </data>
  <data name="CheckPermission" xml:space="preserve">
    <value>Check Permission</value>
  </data>
  <data name="RequestPermission" xml:space="preserve">
    <value>Request Permission</value>
  </data>
  <data name="CustomNotificationTest" xml:space="preserve">
    <value>Custom Notification Test</value>
  </data>
  <data name="NotificationTitle" xml:space="preserve">
    <value>Notification Title</value>
  </data>
  <data name="EnterNotificationTitle" xml:space="preserve">
    <value>Enter notification title</value>
  </data>
  <data name="NotificationContent" xml:space="preserve">
    <value>Notification Content</value>
  </data>
  <data name="EnterNotificationContent" xml:space="preserve">
    <value>Enter notification content</value>
  </data>
  <data name="DelayTimeFormat" xml:space="preserve">
    <value>Delay time: {0} seconds</value>
  </data>
  <data name="InstantNotification" xml:space="preserve">
    <value>Instant Notification</value>
  </data>
  <data name="DelayedNotification" xml:space="preserve">
    <value>Delayed Notification</value>
  </data>
  <data name="FeatureNotificationTest" xml:space="preserve">
    <value>Feature Notification Test</value>
  </data>
  <data name="TestShiftReminder" xml:space="preserve">
    <value>Test Shift Reminder</value>
  </data>
  <data name="TestTodoReminder" xml:space="preserve">
    <value>Test Todo Reminder</value>
  </data>
  <data name="TestPaymentReminder" xml:space="preserve">
    <value>Test Payment Reminder</value>
  </data>
  <data name="TestBreakReminder" xml:space="preserve">
    <value>Test Break Reminder</value>
  </data>
  <data name="SystemManagement" xml:space="preserve">
    <value>System Management</value>
  </data>
  <data name="RescheduleAllNotifications" xml:space="preserve">
    <value>Reschedule All Notifications</value>
  </data>
  <data name="TestInstructions" xml:space="preserve">
    <value>Test Instructions</value>
  </data>
  <data name="InstantNotificationDesc" xml:space="preserve">
    <value>• Instant Notification: Send test notification immediately</value>
  </data>
  <data name="DelayedNotificationDesc" xml:space="preserve">
    <value>• Delayed Notification: Send with set delay time</value>
  </data>
  <data name="ShiftReminderDesc" xml:space="preserve">
    <value>• Shift Reminder: Simulate shift starting in 1 minute</value>
  </data>
  <data name="TodoReminderDesc" xml:space="preserve">
    <value>• Todo Reminder: Simulate task due in 2 minutes</value>
  </data>
  <data name="PaymentReminderDesc" xml:space="preserve">
    <value>• Payment Reminder: Simulate pending payment record</value>
  </data>
  <data name="BreakReminderDesc" xml:space="preserve">
    <value>• Break Reminder: Simulate work break reminder</value>
  </data>
  <data name="RescheduleDesc" xml:space="preserve">
    <value>• Reschedule: Reschedule all pending notifications</value>
  </data>
  <!-- AllPaymentRecordsView -->
  <data name="AllPaymentRecords" xml:space="preserve">
    <value>All Payment Records</value>
  </data>
  <data name="TotalRecords" xml:space="preserve">
    <value>Total Records</value>
  </data>
  <data name="PendingConfirmation" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="SelectAllToggle" xml:space="preserve">
    <value>Select All/Deselect All</value>
  </data>
  <data name="SelectedFormat" xml:space="preserve">
    <value>Selected: {0}</value>
  </data>
  <data name="NoPaymentRecords" xml:space="preserve">
    <value>No payment records</value>
  </data>
  <data name="WorkPeriodFormat" xml:space="preserve">
    <value>Work Period: {0:yyyy-MM-dd}</value>
  </data>
  <data name="ToFormat" xml:space="preserve">
    <value>To: {0:yyyy-MM-dd}</value>
  </data>
  <data name="HoursFormat" xml:space="preserve">
    <value>{0:F1} hours</value>
  </data>
  <data name="CancelConfirmation" xml:space="preserve">
    <value>Cancel Confirmation</value>
  </data>
  <!-- PaymentTodoView -->
  <data name="PaymentTodoStatistics" xml:space="preserve">
    <value>Payment Todo Statistics</value>
  </data>
  <data name="PendingConfirmationCount" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="OverdueCount" xml:space="preserve">
    <value>Overdue</value>
  </data>
  <data name="PendingAmount" xml:space="preserve">
    <value>Pending Amount</value>
  </data>
  <data name="ConfirmedAmount" xml:space="preserve">
    <value>Confirmed Amount</value>
  </data>
  <data name="HideCompleted" xml:space="preserve">
    <value>Hide Completed</value>
  </data>
  <data name="ShowCompleted" xml:space="preserve">
    <value>Show Completed</value>
  </data>
  <data name="OverduePayments" xml:space="preserve">
    <value>Overdue Payments ⚠️</value>
  </data>
  <data name="NoOverduePayments" xml:space="preserve">
    <value>No overdue payments</value>
  </data>
  <data name="UrgentMark" xml:space="preserve">
    <value>⚠️</value>
  </data>
  <data name="PaymentInfo" xml:space="preserve">
    <value>Payment Info</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="PendingPayments" xml:space="preserve">
    <value>Pending Payments</value>
  </data>
  <data name="NoPendingPayments" xml:space="preserve">
    <value>No pending payments</value>
  </data>
  <data name="PaymentIcon" xml:space="preserve">
    <value>💰</value>
  </data>
  <data name="ConfirmedPayments" xml:space="preserve">
    <value>Confirmed Payments</value>
  </data>
  <data name="NoConfirmedPayments" xml:space="preserve">
    <value>No confirmed payments</value>
  </data>
  <data name="CompletedMark" xml:space="preserve">
    <value>✅</value>
  </data>
  <data name="CompletedTimeFormat" xml:space="preserve">
    <value>Completed: {0:MM/dd HH:mm}</value>
  </data>
  <data name="LoadingIndicator" xml:space="preserve">
    <value>Loading Indicator</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <!-- EditEmployerView -->
  <data name="EditEmployer" xml:space="preserve">
    <value>Edit Employer</value>
  </data>
  <data name="EditEmployerDescription" xml:space="preserve">
    <value>Modify employer information and save changes</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="EmployerName" xml:space="preserve">
    <value>Employer Name</value>
  </data>
  <data name="EmployerNamePlaceholder" xml:space="preserve">
    <value>Please enter employer name</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="ContactInfoPlaceholder" xml:space="preserve">
    <value>Please enter contact information</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>Work Location</value>
  </data>
  <data name="WorkLocationPlaceholder" xml:space="preserve">
    <value>Please enter work location</value>
  </data>
  <data name="SalaryInformation" xml:space="preserve">
    <value>Salary Information</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>Hourly Rate</value>
  </data>
  <data name="PaymentCycleDays" xml:space="preserve">
    <value>Payment Cycle (Days)</value>
  </data>
  <data name="DisplaySettings" xml:space="preserve">
    <value>Display Settings</value>
  </data>
  <data name="IdentificationColor" xml:space="preserve">
    <value>Identification Color</value>
  </data>
  <data name="ColorSelectionGrid" xml:space="preserve">
    <value>Color Selection Grid</value>
  </data>
  <data name="ColorCircleButton" xml:space="preserve">
    <value>Color Circle Button</value>
  </data>
  <data name="SelectedMark" xml:space="preserve">
    <value>Selected Mark</value>
  </data>
  <data name="ColorName" xml:space="preserve">
    <value>Color Name</value>
  </data>
  <data name="CurrentColorDisplay" xml:space="preserve">
    <value>Current Color Display</value>
  </data>
  <data name="CurrentColor" xml:space="preserve">
    <value>Current Color:</value>
  </data>
  <data name="EmploymentStatus" xml:space="preserve">
    <value>Employment Status</value>
  </data>
  <data name="NotesInformation" xml:space="preserve">
    <value>Notes Information</value>
  </data>
  <data name="NotesPlaceholder" xml:space="preserve">
    <value>Please enter notes...</value>
  </data>
  <data name="ActionButtons" xml:space="preserve">
    <value>Action Buttons</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>Save Changes</value>
  </data>
  <data name="CancelEdit" xml:space="preserve">
    <value>Cancel Edit</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="NoShiftRecords" xml:space="preserve">
    <value>No shift records</value>
  </data>
  <data name="ClickAddToCreateFirstShift" xml:space="preserve">
    <value>Click the add button in the top right to create your first shift</value>
  </data>
  <data name="AddNow" xml:space="preserve">
    <value>Add Now</value>
  </data>
  <data name="MinimalShiftManagementPage" xml:space="preserve">
    <value>Minimal Shift Management Page</value>
  </data>
  <data name="SelectEmployer" xml:space="preserve">
    <value>Select Employer:</value>
  </data>
  <data name="HourlyRateLabel" xml:space="preserve">
    <value>Hourly Rate:</value>
  </data>
  <data name="Return" xml:space="preserve">
    <value>Return</value>
  </data>
  <data name="ShiftManagementSimplified" xml:space="preserve">
    <value>Shift Management (Simplified)</value>
  </data>
  <data name="PageLoadedSuccessfully" xml:space="preserve">
    <value>Page loaded successfully!</value>
  </data>
  <data name="SaveShift" xml:space="preserve">
    <value>Save Shift</value>
  </data>
  <data name="NavigationWorksMessage" xml:space="preserve">
    <value>If you can see this page, navigation is working properly!</value>
  </data>
  <data name="TryNavigateToFullPage" xml:space="preserve">
    <value>Now you can try navigating to the full SimpleShiftView page.</value>
  </data>
  <data name="OpenFullSchedulePage" xml:space="preserve">
    <value>Open Full Schedule Page</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>Return to Home</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>Quick Actions</value>
  </data>
  <data name="ClockIn" xml:space="preserve">
    <value>Clock In</value>
  </data>
  <data name="ClockOut" xml:space="preserve">
    <value>Clock Out</value>
  </data>
  <data name="AddNote" xml:space="preserve">
    <value>Add Note</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="LoadingIndicator" xml:space="preserve">
    <value>Loading Indicator</value>
  </data>
  <data name="TestShiftManagementPage" xml:space="preserve">
    <value>✅ Test Shift Management Page</value>
  </data>
  <data name="AutoCloseTestTitle" xml:space="preserve">
    <value>Auto Close Test</value>
  </data>
  <data name="AutoCloseTestHeader" xml:space="preserve">
    <value>Auto Close Alert Test</value>
  </data>
  <data name="AutoCloseTestInstructions" xml:space="preserve">
    <value>Click the buttons below to test different types of auto-close alerts:</value>
  </data>
  <data name="TestSuccessToast" xml:space="preserve">
    <value>Test Success Alert (Toast 5s)</value>
  </data>
  <data name="TestErrorSnackbar" xml:space="preserve">
    <value>Test Error Alert (Snackbar 5s)</value>
  </data>
  <data name="TestInfoToast" xml:space="preserve">
    <value>Test Info Alert (Toast 3s)</value>
  </data>
  <data name="TestWarningSnackbar" xml:space="preserve">
    <value>Test Warning Alert (Snackbar 7s)</value>
  </data>
  <data name="AutoCloseNote" xml:space="preserve">
    <value>Note: All alerts will auto-close, but you can also manually close them.</value>
  </data>
  <data name="EmployerCountFormat" xml:space="preserve">
    <value>Employer Count: {0}</value>
  </data>
  <data name="RecordCountFormat" xml:space="preserve">
    <value>Total {0} records</value>
  </data>
  <data name="WorkHoursFormat" xml:space="preserve">
    <value>Work Hours: {0:F1} hours</value>
  </data>
  <data name="DatabaseTestTitle" xml:space="preserve">
    <value>Database Connection Test</value>
  </data>
  <data name="DatabaseTestHeader" xml:space="preserve">
    <value>🔍 Database Connection Diagnostic Tool</value>
  </data>
  <data name="DatabaseTestDescription" xml:space="preserve">
    <value>Verify SQLite database connection status and reset functionality</value>
  </data>
  <data name="TestOptions" xml:space="preserve">
    <value>Test Options</value>
  </data>
  <data name="TestRunning" xml:space="preserve">
    <value>Test Running...</value>
  </data>
  <data name="RunConnectionTest" xml:space="preserve">
    <value>Run Connection Test</value>
  </data>
  <data name="TestResetFunction" xml:space="preserve">
    <value>Test Reset Function</value>
  </data>
  <data name="ReenableDemoData" xml:space="preserve">
    <value>Re-enable Demo Data</value>
  </data>
  <data name="ClearResults" xml:space="preserve">
    <value>Clear Results</value>
  </data>
  <data name="AllTestsPassed" xml:space="preserve">
    <value>All Tests Passed</value>
  </data>
  <data name="TestsFoundIssues" xml:space="preserve">
    <value>Tests Found Issues</value>
  </data>
  <data name="RunningTests" xml:space="preserve">
    <value>Running tests, please wait...</value>
  </data>
  <data name="TestResults" xml:space="preserve">
    <value>Test Results</value>
  </data>
  <data name="BackToSettings" xml:space="preserve">
    <value>Back to Settings</value>
  </data>
  <data name="KeyboardTestTitle" xml:space="preserve">
    <value>Keyboard Test Page</value>
  </data>
  <data name="KeyboardTestHeader" xml:space="preserve">
    <value>Keyboard Pop-up Test</value>
  </data>
  <data name="SimplifiedEntryTest" xml:space="preserve">
    <value>Simplified Entry Test</value>
  </data>
  <data name="BasicTextInput" xml:space="preserve">
    <value>Basic text input</value>
  </data>
  <data name="NumericInput" xml:space="preserve">
    <value>Numeric input</value>
  </data>
  <data name="EmailInput" xml:space="preserve">
    <value>Email input</value>
  </data>
  <data name="MultilineTextInput" xml:space="preserve">
    <value>Multiline text input</value>
  </data>
  <data name="TestInstructions" xml:space="preserve">
    <value>Test Instructions</value>
  </data>
  <data name="TestInstruction1" xml:space="preserve">
    <value>• Clicking each input box should normally bring up the keyboard</value>
  </data>
  <data name="TestInstruction2" xml:space="preserve">
    <value>• Keyboard type should match input content</value>
  </data>
  <data name="TestInstruction3" xml:space="preserve">
    <value>• There should be no delay or focus conflicts</value>
  </data>

  <!-- BaseViewModel Messages -->
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>Operation failed</value>
  </data>

  <!-- SimpleShiftViewModel Messages -->
  <data name="FailedToLoadShiftData" xml:space="preserve">
    <value>Failed to load shift data</value>
  </data>
  <data name="FailedToLoadEmployers" xml:space="preserve">
    <value>Failed to load employers</value>
  </data>
  <data name="FailedToNavigateToEmployerPage" xml:space="preserve">
    <value>Failed to navigate to employer page</value>
  </data>
  <data name="FailedToNavigateToShiftsPage" xml:space="preserve">
    <value>Failed to navigate to shifts page</value>
  </data>
  <data name="PleaseSelectEmployer" xml:space="preserve">
    <value>Please select an employer</value>
  </data>
  <data name="SaveFailed" xml:space="preserve">
    <value>Save failed</value>
  </data>

  <!-- EditEmployerViewModel Messages -->
  <data name="SaveSuccessful" xml:space="preserve">
    <value>Save successful</value>
  </data>
  <data name="EmployerInfoUpdatedSuccessfully" xml:space="preserve">
    <value>Employer information has been successfully updated</value>
  </data>

  <!-- Additional SimpleShiftViewModel Messages -->
  <data name="PleaseEnterValidHourlyRate" xml:space="preserve">
    <value>Please enter a valid hourly rate</value>
  </data>
  <data name="PleaseSetValidWorkHours" xml:space="preserve">
    <value>Please set valid work hours</value>
  </data>
  <data name="SavingShift" xml:space="preserve">
    <value>Saving shift...</value>
  </data>

  <!-- HomeViewModel Messages -->
  <data name="RefreshDataFailed" xml:space="preserve">
    <value>Failed to refresh data</value>
  </data>

  <!-- ShiftListViewModel Messages -->
  <data name="ShiftDetails" xml:space="preserve">
    <value>Shift Details</value>
  </data>
  <data name="Employer" xml:space="preserve">
    <value>Employer</value>
  </data>
  <data name="WorkHours" xml:space="preserve">
    <value>Work Hours</value>
  </data>
  <data name="EstimatedEarnings" xml:space="preserve">
    <value>Estimated Earnings</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>hours</value>
  </data>

  <!-- AutoCloseTestViewModel Messages -->
  <data name="ErrorTestMessage" xml:space="preserve">
    <value>This is an error message that will auto-close in 5 seconds!</value>
  </data>
  <data name="OperationFailedTitle" xml:space="preserve">
    <value>Operation Failed</value>
  </data>
  <data name="InfoTestMessage" xml:space="preserve">
    <value>This is an info message that will auto-close in 3 seconds!</value>
  </data>
  <data name="InfoTitle" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="WarningTestMessage" xml:space="preserve">
    <value>This is a warning message that will auto-close in 7 seconds!</value>
  </data>
  <data name="WarningTitle" xml:space="preserve">
    <value>Warning</value>
  </data>

  <!-- PayViewModel Messages -->
  <data name="LoadFailed" xml:space="preserve">
    <value>Load Failed</value>
  </data>
  <data name="UnableToLoadEmployerInfo" xml:space="preserve">
    <value>Unable to load employer information</value>
  </data>

  <!-- TodoListViewModel Messages -->
  <data name="FailedToUpdateTodoItemStatus" xml:space="preserve">
    <value>Failed to update todo item status</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="AreYouSureDeleteTodoItem" xml:space="preserve">
    <value>Are you sure you want to delete todo item "{0}"?</value>
  </data>
  <data name="SyncedTodoItemStatus" xml:space="preserve">
    <value>Synced {0} todo item status</value>
  </data>
  <data name="AllTodoItemStatusesUpToDate" xml:space="preserve">
    <value>All todo item statuses are up to date</value>
  </data>

  <!-- AllEmployerViewModel Messages -->
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="UnselectAll" xml:space="preserve">
    <value>Unselect All</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>Delete Selected</value>
  </data>
  <data name="DeleteSelectedCount" xml:space="preserve">
    <value>Delete Selected ({0})</value>
  </data>
  <data name="ConfirmDeleteEmployers" xml:space="preserve">
    <value>Are you sure you want to delete the following employers?\n\n{0}\n\nDeleting will also clean up related shift and work record data.</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>Delete Success</value>
  </data>
  <data name="SuccessfullyDeletedEmployers" xml:space="preserve">
    <value>Successfully deleted {0} employers</value>
  </data>
  <data name="DeleteFailed" xml:space="preserve">
    <value>Delete Failed</value>
  </data>
  <data name="ConfirmDeleteEmployer" xml:space="preserve">
    <value>Are you sure you want to delete employer "{0}"? This will also delete all related shifts and payment records.</value>
  </data>
  <data name="EmployerDeleted" xml:space="preserve">
    <value>Employer "{0}" has been deleted</value>
  </data>

  <!-- NotificationHistoryViewModel Messages -->
  <data name="LoadingNotificationHistory" xml:space="preserve">
    <value>Loading notification history...</value>
  </data>
  <data name="LoadedNotificationCount" xml:space="preserve">
    <value>Loaded {0} notification records</value>
  </data>
  <data name="LoadNotificationHistoryFailed" xml:space="preserve">
    <value>Failed to load notification history: {0}</value>
  </data>
  <data name="LoadingShiftsWithNotifications" xml:space="preserve">
    <value>Loading shifts with enabled notifications...</value>
  </data>
  <data name="FoundShiftsWithNotifications" xml:space="preserve">
    <value>Found {0} shifts with enabled notifications</value>
  </data>
  <data name="LoadShiftNotificationsFailed" xml:space="preserve">
    <value>Failed to load shift notifications: {0}</value>
  </data>
  <data name="ShiftNotificationCancelled" xml:space="preserve">
    <value>Shift notification cancelled: {0} - {1:MM/dd HH:mm}</value>
  </data>
  <data name="CancelShiftNotificationFailed" xml:space="preserve">
    <value>Failed to cancel shift notification: {0}</value>
  </data>
  <data name="SendingTestNotification" xml:space="preserve">
    <value>Sending test notification...</value>
  </data>
  <data name="TestNotificationFailedNoPermission" xml:space="preserve">
    <value>Test notification failed: No notification permission</value>
  </data>
  <data name="TestNotificationTitle" xml:space="preserve">
    <value>📱 Notification Function Test</value>
  </data>
  <data name="TestNotificationDescription" xml:space="preserve">
    <value>This is a test notification to verify that the notification function is working properly. If you see this notification, the notification system is working normally!</value>
  </data>
  <data name="TestNotificationSent" xml:space="preserve">
    <value>Test notification sent, please check the notification bar</value>
  </data>
  <data name="SendTestNotificationFailed" xml:space="preserve">
    <value>Failed to send test notification: {0}</value>
  </data>
  <data name="NotificationMarkedAsRead" xml:space="preserve">
    <value>Notification marked as read</value>
  </data>
  <data name="MarkNotificationFailed" xml:space="preserve">
    <value>Failed to mark notification: {0}</value>
  </data>
  <data name="NotificationRecordDeleted" xml:space="preserve">
    <value>Notification record deleted</value>
  </data>
  <data name="DeleteNotificationRecordFailed" xml:space="preserve">
    <value>Failed to delete notification record</value>
  </data>
  <data name="ClearingAllNotificationRecords" xml:space="preserve">
    <value>Clearing all notification records...</value>
  </data>
  <data name="AllNotificationRecordsCleared" xml:space="preserve">
    <value>All notification records cleared</value>
  </data>
  <data name="ClearNotificationRecordsFailed" xml:space="preserve">
    <value>Failed to clear notification records: {0}</value>
  </data>
  <data name="ResendingNotification" xml:space="preserve">
    <value>Resending notification...</value>
  </data>
  <data name="ResendPrefix" xml:space="preserve">
    <value>Resend: {0}</value>
  </data>
  <data name="NotificationResent" xml:space="preserve">
    <value>Notification resent</value>
  </data>
  <data name="ResendNotificationFailed" xml:space="preserve">
    <value>Failed to resend notification: {0}</value>
  </data>
  <data name="AllTypes" xml:space="preserve">
    <value>All Types</value>
  </data>
  <data name="WorkReminder" xml:space="preserve">
    <value>Work Reminder</value>
  </data>
  <data name="BreakReminder" xml:space="preserve">
    <value>Break Reminder</value>
  </data>
  <data name="PaymentDue" xml:space="preserve">
    <value>Payment Due</value>
  </data>
  <data name="ShiftConflict" xml:space="preserve">
    <value>Shift Conflict</value>
  </data>
  <data name="SystemUpdate" xml:space="preserve">
    <value>System Update</value>
  </data>
  <data name="UnknownType" xml:space="preserve">
    <value>Unknown Type</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>All Statuses</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Sent</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="Dismissed" xml:space="preserve">
    <value>Dismissed</value>
  </data>
  <data name="UnknownStatus" xml:space="preserve">
    <value>Unknown Status</value>
  </data>
  <data name="TestNotificationMessage" xml:space="preserve">
    <value>This is a test notification to verify that the notification function is working properly.</value>
  </data>

  <!-- NotificationSettingsViewModel Messages -->
  <data name="LoadingSettings" xml:space="preserve">
    <value>Loading settings...</value>
  </data>
  <data name="SettingsLoadComplete" xml:space="preserve">
    <value>Settings loaded successfully</value>
  </data>
  <data name="LoadSettingsFailed" xml:space="preserve">
    <value>Failed to load settings: {0}</value>
  </data>
  <data name="SavingSettings" xml:space="preserve">
    <value>Saving settings...</value>
  </data>
  <data name="SettingsSaveSuccess" xml:space="preserve">
    <value>Settings saved successfully</value>
  </data>
  <data name="SettingsSaveFailed" xml:space="preserve">
    <value>Failed to save settings</value>
  </data>
  <data name="SaveSettingsFailed" xml:space="preserve">
    <value>Failed to save settings: {0}</value>
  </data>
  <data name="ResettingSettings" xml:space="preserve">
    <value>Resetting settings...</value>
  </data>
  <data name="SettingsResetToDefault" xml:space="preserve">
    <value>Settings have been reset to default values</value>
  </data>
  <data name="ResetSettingsFailed" xml:space="preserve">
    <value>Failed to reset settings</value>
  </data>
  <data name="ResetSettingsFailedWithError" xml:space="preserve">
    <value>Failed to reset settings: {0}</value>
  </data>
  <data name="SendingTestNotificationSettings" xml:space="preserve">
    <value>Sending test notification...</value>
  </data>
  <data name="NotificationPermissionDeniedCannotTest" xml:space="preserve">
    <value>Notification permission denied, cannot send test notification</value>
  </data>
  <data name="TestNotificationSettingsMessage" xml:space="preserve">
    <value>This is a test notification to verify that the notification function is working properly.</value>
  </data>
  <data name="TestNotificationSettingsSent" xml:space="preserve">
    <value>Test notification sent</value>
  </data>
  <data name="SendTestNotificationSettingsFailed" xml:space="preserve">
    <value>Failed to send test notification: {0}</value>
  </data>
  <data name="RequestingNotificationPermission" xml:space="preserve">
    <value>Requesting notification permission...</value>
  </data>
  <data name="NotificationPermissionGranted" xml:space="preserve">
    <value>Notification permission granted</value>
  </data>
  <data name="NotificationPermissionDeniedManualEnable" xml:space="preserve">
    <value>Notification permission denied, please enable manually in system settings</value>
  </data>
  <data name="RequestNotificationPermissionFailed" xml:space="preserve">
    <value>Failed to request notification permission: {0}</value>
  </data>
  <data name="FiveMinutesBefore" xml:space="preserve">
    <value>5 minutes before</value>
  </data>
  <data name="TenMinutesBefore" xml:space="preserve">
    <value>10 minutes before</value>
  </data>
  <data name="FifteenMinutesBefore" xml:space="preserve">
    <value>15 minutes before</value>
  </data>
  <data name="ThirtyMinutesBefore" xml:space="preserve">
    <value>30 minutes before</value>
  </data>
  <data name="OneHourBefore" xml:space="preserve">
    <value>1 hour before</value>
  </data>
  <data name="EveryOneHour" xml:space="preserve">
    <value>Every 1 hour</value>
  </data>
  <data name="EveryTwoHours" xml:space="preserve">
    <value>Every 2 hours</value>
  </data>
  <data name="EveryThreeHours" xml:space="preserve">
    <value>Every 3 hours</value>
  </data>
  <data name="EveryFourHours" xml:space="preserve">
    <value>Every 4 hours</value>
  </data>
  <data name="SameDay" xml:space="preserve">
    <value>Same day</value>
  </data>
  <data name="OneDayBefore" xml:space="preserve">
    <value>1 day before</value>
  </data>
  <data name="TwoDaysBefore" xml:space="preserve">
    <value>2 days before</value>
  </data>
  <data name="ThreeDaysBefore" xml:space="preserve">
    <value>3 days before</value>
  </data>
  <data name="OneWeekBefore" xml:space="preserve">
    <value>1 week before</value>
  </data>

  <!-- NotificationTestViewModel Messages -->
  <data name="TestNotificationTitle" xml:space="preserve">
    <value>Test Notification</value>
  </data>
  <data name="TestNotificationDefaultMessage" xml:space="preserve">
    <value>This is a test notification message</value>
  </data>
  <data name="SendingInstantNotification" xml:space="preserve">
    <value>Sending instant notification...</value>
  </data>
  <data name="NotificationPermissionDeniedCannotSendTest" xml:space="preserve">
    <value>Notification permission denied, cannot send test notification</value>
  </data>
  <data name="InstantNotificationSent" xml:space="preserve">
    <value>Instant notification sent</value>
  </data>
  <data name="SendInstantNotificationFailed" xml:space="preserve">
    <value>Failed to send instant notification: {0}</value>
  </data>
  <data name="SchedulingDelayedNotification" xml:space="preserve">
    <value>Scheduling delayed notification in {0} seconds...</value>
  </data>
  <data name="DelayedNotificationScheduled" xml:space="preserve">
    <value>Delayed notification scheduled, will be sent in {0} seconds</value>
  </data>
  <data name="ScheduleDelayedNotificationFailed" xml:space="preserve">
    <value>Failed to schedule delayed notification: {0}</value>
  </data>
  <data name="TestingShiftReminder" xml:space="preserve">
    <value>Testing shift reminder...</value>
  </data>
  <data name="NoEmployersFoundCannotTestShiftReminder" xml:space="preserve">
    <value>No employers found, cannot test shift reminder</value>
  </data>
  <data name="TestLocation" xml:space="preserve">
    <value>Test Location</value>
  </data>
  <data name="TestShiftLocation" xml:space="preserve">
    <value>Test Shift Location</value>
  </data>
  <data name="ShiftReminderTestScheduled" xml:space="preserve">
    <value>Shift reminder test scheduled, will be sent in 1 minute</value>
  </data>
  <data name="TestShiftReminderFailed" xml:space="preserve">
    <value>Failed to test shift reminder: {0}</value>
  </data>
  <data name="TestingTodoReminder" xml:space="preserve">
    <value>Testing todo reminder...</value>
  </data>
  <data name="TestTodoItem" xml:space="preserve">
    <value>Test Todo Item</value>
  </data>
  <data name="TestTodoDescription" xml:space="preserve">
    <value>This is a test todo item for notification function testing</value>
  </data>
  <data name="TodoReminderTestScheduled" xml:space="preserve">
    <value>Todo reminder test scheduled, will be sent in 2 minutes</value>
  </data>
  <data name="TestTodoReminderFailed" xml:space="preserve">
    <value>Failed to test todo reminder: {0}</value>
  </data>
  <data name="TestingPaymentReminder" xml:space="preserve">
    <value>Testing payment reminder...</value>
  </data>
  <data name="NoEmployersFoundCannotTestPaymentReminder" xml:space="preserve">
    <value>No employers found, cannot test payment reminder</value>
  </data>
  <data name="PaymentReminderTestSent" xml:space="preserve">
    <value>Payment reminder test sent</value>
  </data>
  <data name="TestPaymentReminderFailed" xml:space="preserve">
    <value>Failed to test payment reminder: {0}</value>
  </data>
  <data name="TestingBreakReminder" xml:space="preserve">
    <value>Testing break reminder...</value>
  </data>
  <data name="TestBreakReminderMessage" xml:space="preserve">
    <value>Test break reminder: You have been working continuously for 2 hours, it is recommended to take a 10-15 minute break</value>
  </data>
  <data name="BreakReminderTestSent" xml:space="preserve">
    <value>Break reminder test sent</value>
  </data>
  <data name="TestBreakReminderFailed" xml:space="preserve">
    <value>Failed to test break reminder: {0}</value>
  </data>
  <data name="ReschedulingAllNotifications" xml:space="preserve">
    <value>Rescheduling all notifications...</value>
  </data>
  <data name="AllNotificationsRescheduled" xml:space="preserve">
    <value>All notifications have been rescheduled</value>
  </data>
  <data name="RescheduleNotificationsFailed" xml:space="preserve">
    <value>Failed to reschedule notifications: {0}</value>
  </data>
  <data name="CheckingNotificationPermission" xml:space="preserve">
    <value>Checking notification permission...</value>
  </data>
  <data name="NotificationPermissionObtained" xml:space="preserve">
    <value>Notification permission obtained</value>
  </data>
  <data name="NotificationPermissionNotObtained" xml:space="preserve">
    <value>Notification permission not obtained</value>
  </data>
  <data name="CheckNotificationPermissionFailed" xml:space="preserve">
    <value>Failed to check notification permission: {0}</value>
  </data>
  <data name="RequestingNotificationPermissionTest" xml:space="preserve">
    <value>Requesting notification permission...</value>
  </data>
  <data name="NotificationPermissionObtainedTest" xml:space="preserve">
    <value>Notification permission obtained</value>
  </data>
  <data name="NotificationPermissionDeniedTest" xml:space="preserve">
    <value>Notification permission denied</value>
  </data>
  <data name="RequestNotificationPermissionTestFailed" xml:space="preserve">
    <value>Failed to request notification permission: {0}</value>
  </data>
</root>
