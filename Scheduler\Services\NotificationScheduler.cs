// 引入数据模型命名空间 / Import data models namespace
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 通知调度管理器接口 / Notification Scheduler Interface
    /// 定义通知调度和管理的标准契约
    /// Defines standard contract for notification scheduling and management
    /// </summary>
    public interface INotificationScheduler
    {
        /// <summary>初始化调度器 / Initialize scheduler</summary>
        Task InitializeAsync();
        /// <summary>调度班次通知 / Schedule shift notifications</summary>
        Task ScheduleShiftNotificationsAsync(Shift shift);
        /// <summary>取消班次通知 / Cancel shift notifications</summary>
        Task CancelShiftNotificationsAsync(int shiftId);
        /// <summary>更新班次通知 / Update shift notifications</summary>
        Task UpdateShiftNotificationsAsync(Shift shift);
        /// <summary>调度每日通知 / Schedule daily notifications</summary>
        Task ScheduleDailyNotificationsAsync();
        /// <summary>调度支付提醒 / Schedule payment reminders</summary>
        Task SchedulePaymentRemindersAsync();
        /// <summary>调度待办事项提醒 / Schedule todo reminders</summary>
        Task ScheduleTodoRemindersAsync();
        /// <summary>清理过期通知 / Cleanup expired notifications</summary>
        Task CleanupExpiredNotificationsAsync();
        /// <summary>重新调度所有通知 / Reschedule all notifications</summary>
        Task RescheduleAllNotificationsAsync();
    }

    /// <summary>
    /// 通知调度管理器 / Notification Scheduler
    /// 负责管理和调度各种类型的通知
    /// Responsible for managing and scheduling various types of notifications
    /// </summary>
    public class NotificationScheduler : INotificationScheduler
    {
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;
        // 通知设置服务实例 / Notification settings service instance
        private readonly INotificationSettingsService _notificationSettingsService;
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 权限服务实例 / Permission service instance
        private readonly PermissionService _permissionService;
        // 每日定时器 / Daily timer
        private readonly Timer? _dailyTimer;
        // 清理定时器 / Cleanup timer
        private readonly Timer? _cleanupTimer;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化通知调度管理器 / Initialize notification scheduler
        /// </summary>
        /// <param name="notificationService">通知服务 / Notification service</param>
        /// <param name="notificationSettingsService">通知设置服务 / Notification settings service</param>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="permissionService">权限服务 / Permission service</param>
        public NotificationScheduler(
            INotificationService notificationService,
            INotificationSettingsService notificationSettingsService,
            DatabaseService databaseService,
            PermissionService permissionService)
        {
            _notificationService = notificationService;
            _notificationSettingsService = notificationSettingsService;
            _databaseService = databaseService;
            _permissionService = permissionService;
        }

        /// <summary>
        /// 初始化调度器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                // 检查通知权限
                bool hasPermission = await _permissionService.EnsureNotificationPermissionAsync();

                if (!hasPermission)
                {
                    System.Diagnostics.Debug.WriteLine("通知权限被拒绝，无法初始化通知调度器");
                    return;
                }

                // 安排所有班次提醒
                await ScheduleAllShiftRemindersAsync();

                // 安排每日工作安排通知
                await ScheduleDailyNotificationsAsync();

                // 安排薪资提醒
                await SchedulePaymentRemindersAsync();

                // 安排待办事项提醒
                await ScheduleTodoRemindersAsync();

                // 设置定时清理过期通知
                await SetupCleanupTimerAsync();

                System.Diagnostics.Debug.WriteLine("通知调度器初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通知调度器初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为班次安排通知
        /// </summary>
        public async Task ScheduleShiftNotificationsAsync(Shift shift)
        {
            try
            {
                var settings = await _notificationSettingsService.GetSettingsAsync();

                // 检查全局班次提醒设置和班次级别的提醒设置
                if (!settings.IsShiftReminderEnabled || !shift.IsReminderEnabled)
                {
                    System.Diagnostics.Debug.WriteLine($"班次 {shift.Id} 的通知已禁用（全局: {settings.IsShiftReminderEnabled}, 班次: {shift.IsReminderEnabled}）");
                    return;
                }

                // 安排班次提醒，使用班次级别的提醒时间设置
                await _notificationService.ScheduleShiftReminderAsync(shift);

                System.Diagnostics.Debug.WriteLine($"已为班次 {shift.Id} 安排通知，提前 {shift.ReminderMinutes} 分钟");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"为班次安排通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消班次相关通知
        /// </summary>
        public async Task CancelShiftNotificationsAsync(int shiftId)
        {
            try
            {
                await _notificationService.CancelShiftNotificationsAsync(shiftId);
                System.Diagnostics.Debug.WriteLine($"已取消班次 {shiftId} 的通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消班次通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新班次通知
        /// </summary>
        public async Task UpdateShiftNotificationsAsync(Shift shift)
        {
            try
            {
                // 先取消原有通知
                await CancelShiftNotificationsAsync(shift.Id);
                
                // 重新安排通知
                await ScheduleShiftNotificationsAsync(shift);
                
                System.Diagnostics.Debug.WriteLine($"已更新班次 {shift.Id} 的通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新班次通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排每日工作安排通知
        /// </summary>
        public async Task ScheduleDailyNotificationsAsync()
        {
            try
            {
                var settings = await _notificationSettingsService.GetSettingsAsync();
                
                // 检查是否启用每日工作安排通知
                if (!settings.IsDailyScheduleEnabled)
                    return;

                // 为未来7天安排每日通知
                for (int i = 0; i < 7; i++)
                {
                    var date = DateTime.Today.AddDays(i);
                    await _notificationService.ScheduleDailyWorkScheduleNotificationAsync(date);
                }

                System.Diagnostics.Debug.WriteLine("已安排每日工作安排通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排每日通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排薪资提醒
        /// </summary>
        public async Task SchedulePaymentRemindersAsync()
        {
            try
            {
                var settings = await _notificationSettingsService.GetSettingsAsync();

                // 检查是否启用薪资提醒
                if (!settings.IsPaymentReminderEnabled)
                    return;

                await _notificationService.SchedulePaymentRemindersAsync();
                System.Diagnostics.Debug.WriteLine("已安排薪资提醒");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排薪资提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排待办事项提醒
        /// </summary>
        public async Task ScheduleTodoRemindersAsync()
        {
            try
            {
                var settings = await _notificationSettingsService.GetSettingsAsync();

                // 检查是否启用通知
                if (!settings.IsNotificationEnabled)
                    return;

                await _notificationService.ScheduleAllTodoRemindersAsync();
                System.Diagnostics.Debug.WriteLine("已安排待办事项提醒");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排待办事项提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理过期通知
        /// </summary>
        public async Task CleanupExpiredNotificationsAsync()
        {
            try
            {
                await _notificationService.CleanupExpiredNotificationsAsync();
                System.Diagnostics.Debug.WriteLine("已清理过期通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理过期通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重新安排所有通知
        /// </summary>
        public async Task RescheduleAllNotificationsAsync()
        {
            try
            {
                // 取消所有现有通知
                await _notificationService.CancelAllNotificationsAsync();
                
                // 重新初始化
                await InitializeAsync();
                
                System.Diagnostics.Debug.WriteLine("已重新安排所有通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重新安排通知失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安排所有班次提醒
        /// </summary>
        private async Task ScheduleAllShiftRemindersAsync()
        {
            try
            {
                var settings = await _notificationSettingsService.GetSettingsAsync();
                
                if (!settings.IsShiftReminderEnabled)
                    return;

                // 获取未来7天的班次
                var startDate = DateTime.Today;
                var endDate = startDate.AddDays(7);
                var shifts = await _databaseService.GetShiftsByDateRangeAsync(startDate, endDate);

                foreach (var shift in shifts)
                {
                    if (shift.Status == ShiftStatus.Scheduled && shift.StartTime > DateTime.Now)
                    {
                        await _notificationService.ScheduleShiftReminderAsync(shift);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已安排 {shifts.Count} 个班次的提醒通知");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"安排班次提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置清理定时器
        /// </summary>
        private async Task SetupCleanupTimerAsync()
        {
            try
            {
                // 每天凌晨2点清理过期通知
                var now = DateTime.Now;
                var nextCleanup = DateTime.Today.AddDays(1).AddHours(2);
                var timeUntilCleanup = nextCleanup - now;

                // 创建定时器，每24小时执行一次清理
                var cleanupTimer = new Timer(async _ =>
                {
                    await CleanupExpiredNotificationsAsync();
                }, null, timeUntilCleanup, TimeSpan.FromDays(1));

                System.Diagnostics.Debug.WriteLine($"清理定时器已设置，下次清理时间: {nextCleanup}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置清理定时器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _dailyTimer?.Dispose();
            _cleanupTimer?.Dispose();
        }
    }
}
