<?xml version="1.0" encoding="utf-8" ?>
<!--
    所有雇主列表页面 / All Employers List Page
    显示所有雇主信息，支持搜索、编辑、删除等操作
    Display all employer information with search, edit, delete operations
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             x:Class="Scheduler.Views.AllEmployer"
             x:DataType="viewmodels:AllEmployerViewModel"
             Title="All Employers"
             BackgroundColor="#F8F9FA"
             Shell.FlyoutBehavior="Disabled"
             Shell.TabBarIsVisible="False">

    <!-- 返回按钮配置 / Back button configuration -->
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="True" IsVisible="True" />
    </Shell.BackButtonBehavior>

    <!-- 主布局：顶部操作区、列表区、底部操作区 / Main layout: top operations, list, bottom operations -->
    <Grid RowDefinitions="Auto,*,Auto">

        <!-- 顶部搜索和操作区域 / Top search and operation area -->
        <Border Grid.Row="0"
                BackgroundColor="#007ACC"
                StrokeShape="Rectangle"
                Padding="20">
            <Border.Shadow>
                <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
            </Border.Shadow>
            <VerticalStackLayout Spacing="15">
                <!-- 页面标题 / Page title -->
                <Label Text="{Binding LocalizedTexts.EmployerManagement}"
                       FontSize="24"
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center"/>

                <!-- 搜索框 / Search box -->
                <Border BackgroundColor="White"
                        StrokeShape="RoundRectangle 25"
                        Padding="15,10">
                    <Entry Text="{Binding SearchText}"
                           Placeholder="搜索雇主姓名、联系方式或工作地点..."
                           BackgroundColor="Transparent"
                           TextColor="#333"
                           PlaceholderColor="#999"
                           ReturnType="Search"
                           ClearButtonVisibility="WhileEditing"
                           x:Name="SearchEntry"/>
                </Border>

                <!-- 操作按钮行 / Operation buttons row -->
                <HorizontalStackLayout Spacing="10"
                                       HorizontalOptions="Center">
                    <!-- 新增雇主按钮 / Add employer button -->
                    <Button Text="{Binding LocalizedTexts.AddEmployer}"
                            Command="{Binding AddEmployerCommand}"
                            BackgroundColor="#28A745"
                            TextColor="White"
                            FontSize="14"
                            FontAttributes="Bold"
                            WidthRequest="100"
                            HeightRequest="40"
                            CornerRadius="20"/>

                    <!-- 刷新按钮 / Refresh button -->
                    <Button Text="{Binding LocalizedTexts.Refresh}"
                            Command="{Binding RefreshCommand}"
                            BackgroundColor="#17A2B8"
                            TextColor="White"
                            FontSize="14"
                            WidthRequest="80"
                            HeightRequest="40"
                            CornerRadius="20"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </Border>

        <!-- 雇主列表区域 / Employer list area -->
        <RefreshView Grid.Row="1"
                     IsRefreshing="{Binding IsBusy}"
                     Command="{Binding RefreshCommand}">

            <!-- 内容容器 / Content container -->
            <Grid>
                <!-- 空状态显示 / Empty state display -->
                <StackLayout IsVisible="{Binding HasEmployers, Converter={StaticResource InverseBoolConverter}}"
                             VerticalOptions="CenterAndExpand"
                             HorizontalOptions="CenterAndExpand"
                             Spacing="20"
                             Margin="40">
                    <Label Text="📋"
                           FontSize="60"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding LocalizedTexts.NoEmployerInfo}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#6C757D"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding LocalizedTexts.ClickAddEmployerToStart}"
                           FontSize="14"
                           TextColor="#6C757D"
                           HorizontalOptions="Center"/>
                </StackLayout>

                <!-- 雇主列表 / Employer list -->
                <CollectionView ItemsSource="{Binding Employers}"
                                SelectedItem="{Binding SelectedEmployer}"
                                SelectionMode="Single"
                                IsVisible="{Binding HasEmployers}"
                                Margin="10,0">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:EmployerWrapper">
                        <!-- 雇主卡片 / Employer card -->
                        <Border BackgroundColor="White"
                                StrokeShape="RoundRectangle 12"
                                Padding="15"
                                Margin="5">
                            <Border.Shadow>
                                <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
                            </Border.Shadow>
                            <Grid ColumnDefinitions="Auto,Auto,*,Auto,Auto"
                                  RowDefinitions="Auto,Auto,Auto"
                                  ColumnSpacing="15"
                                  RowSpacing="8">

                                <!-- 选择复选框 / Selection checkbox -->
                                <CheckBox Grid.Column="0"
                                          Grid.RowSpan="3"
                                          IsChecked="{Binding IsSelected}"
                                          Color="#007ACC"
                                          VerticalOptions="Center">
                                    <CheckBox.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AllEmployerViewModel}}, Path=ToggleEmployerSelectionCommand}"
                                                              CommandParameter="{Binding .}"/>
                                    </CheckBox.GestureRecognizers>
                                </CheckBox>

                                <!-- 颜色标识 / Color indicator -->
                                <Border Grid.Column="1"
                                        Grid.RowSpan="3"
                                        BackgroundColor="{Binding Color}"
                                        WidthRequest="8"
                                        StrokeShape="RoundRectangle 4"
                                        Padding="0"
                                        VerticalOptions="Fill"/>

                                <!-- 雇主姓名 / Employer name -->
                                <Label Grid.Column="2"
                                       Grid.Row="0"
                                       Text="{Binding Name}"
                                       FontSize="18"
                                       FontAttributes="Bold"
                                       TextColor="#333"/>

                                <!-- 联系方式 / Contact information -->
                                <Label Grid.Column="2"
                                       Grid.Row="1"
                                       Text="{Binding ContactInfo}"
                                       FontSize="14"
                                       TextColor="#666"
                                       IsVisible="{Binding ContactInfo, Converter={StaticResource StringToBoolConverter}}"/>

                                <!-- 工作地点和时薪 / Work location and hourly rate -->
                                <HorizontalStackLayout Grid.Column="2"
                                                       Grid.Row="2"
                                                       Spacing="15">
                                    <Label Text="{Binding WorkLocation}"
                                           FontSize="12"
                                           TextColor="#666"
                                           IsVisible="{Binding WorkLocation, Converter={StaticResource StringToBoolConverter}}"/>
                                    <Label FontSize="12"
                                           FontAttributes="Bold"
                                           TextColor="#007ACC">
                                        <Label.Text>
                                            <MultiBinding StringFormat="¥{0}/hour">
                                                <Binding Path="HourlyRate"/>
                                            </MultiBinding>
                                        </Label.Text>
                                    </Label>
                                </HorizontalStackLayout>

                                <!-- 编辑按钮 / Edit button -->
                                <Button Grid.Column="3"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AllEmployerViewModel}}, Path=LocalizedTexts.Edit}"
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:AllEmployerViewModel}}, Path=EditSingleEmployerCommand}"
                                        CommandParameter="{Binding .}"
                                        BackgroundColor="#FFC107"
                                        TextColor="White"
                                        FontSize="12"
                                        FontAttributes="Bold"
                                        WidthRequest="50"
                                        HeightRequest="30"
                                        CornerRadius="15"
                                        VerticalOptions="Center"/>

                                <!-- 在职状态指示器 / Active status indicator -->
                                <Border Grid.Column="4"
                                        Grid.RowSpan="3"
                                        BackgroundColor="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}"
                                        WidthRequest="12"
                                        HeightRequest="12"
                                        StrokeShape="RoundRectangle 6"
                                        Padding="0"
                                        VerticalOptions="Center"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            </Grid>
        </RefreshView>

        <!-- 底部操作按钮区域 / Bottom operation buttons area -->
        <Border Grid.Row="2"
                BackgroundColor="White"
                StrokeShape="Rectangle"
                Padding="20">
            <Border.Shadow>
                <Shadow Brush="Black" Opacity="0.1" Radius="8" Offset="0,2"/>
            </Border.Shadow>
            <StackLayout Spacing="15">
                <!-- 多选操作区域 / Multi-selection operation area -->
                <HorizontalStackLayout Spacing="15"
                                       HorizontalOptions="Center"
                                       IsVisible="{Binding HasEmployers}">

                    <!-- 全选/取消全选按钮 / Select all/deselect all button -->
                    <Button Text="{Binding SelectAllButtonText}"
                            Command="{Binding ToggleSelectAllCommand}"
                            BackgroundColor="#17A2B8"
                            TextColor="White"
                            FontSize="12"
                            FontAttributes="Bold"
                            WidthRequest="80"
                            HeightRequest="35"
                            CornerRadius="17"/>

                    <!-- 批量删除按钮 / Batch delete button -->
                    <Button Text="{Binding BatchDeleteButtonText}"
                            Command="{Binding BatchDeleteCommand}"
                            BackgroundColor="#DC3545"
                            TextColor="White"
                            FontSize="12"
                            FontAttributes="Bold"
                            WidthRequest="100"
                            HeightRequest="35"
                            CornerRadius="17"
                            IsEnabled="{Binding HasSelectedEmployers}"/>
                </HorizontalStackLayout>

                <!-- 单选操作区域 / Single selection operation area -->
                <HorizontalStackLayout Spacing="15"
                                       HorizontalOptions="Center">

                    <!-- 编辑按钮 / Edit button -->
                    <Button Text="{Binding LocalizedTexts.EditSelected}"
                            Command="{Binding EditEmployerCommand}"
                            BackgroundColor="#FFC107"
                            TextColor="White"
                            FontSize="14"
                            FontAttributes="Bold"
                            WidthRequest="80"
                            HeightRequest="45"
                            CornerRadius="22"
                            IsEnabled="{Binding CanEdit}"/>

                    <!-- 删除按钮 / Delete button -->
                    <Button Text="{Binding LocalizedTexts.DeleteSelected}"
                            Command="{Binding DeleteEmployerCommand}"
                            BackgroundColor="#DC3545"
                            TextColor="White"
                            FontSize="14"
                            FontAttributes="Bold"
                            WidthRequest="80"
                            HeightRequest="45"
                            CornerRadius="22"
                            IsEnabled="{Binding CanDelete}"/>

                    <!-- 返回Home按钮 / Back to home button -->
                    <Button Text="{Binding LocalizedTexts.BackToHome}"
                            Command="{Binding GoToHomeCommand}"
                            BackgroundColor="#6C757D"
                            TextColor="White"
                            FontSize="14"
                            FontAttributes="Bold"
                            WidthRequest="100"
                            HeightRequest="45"
                            CornerRadius="22"/>
                </HorizontalStackLayout>
            </StackLayout>
        </Border>

        <!-- 加载指示器 / Loading indicator -->
        <ActivityIndicator Grid.Row="1"
                           IsVisible="{Binding IsBusy}"
                           IsRunning="{Binding IsBusy}"
                           Color="#007ACC"
                           VerticalOptions="Center"
                           HorizontalOptions="Center"/>
    </Grid>
</ContentPage>
