// 引入视图模型和扩展方法 / Import ViewModels and Extensions
using Scheduler.ViewModels;
using Scheduler.Extensions;

namespace Scheduler.Views;

/// <summary>
/// 所有雇主列表页面 / All Employers List Page
/// 显示所有雇主信息，支持搜索、编辑、删除等操作
/// Display all employer information with search, edit, delete operations
/// </summary>
public partial class AllEmployer : ContentPage
{
    // 视图模型实例 / ViewModel instance
    private readonly AllEmployerViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化页面并配置数据绑定 / Initialize page and configure data binding
    /// </summary>
    /// <param name="viewModel">雇主列表视图模型 / Employer list view model</param>
    public AllEmployer(AllEmployerViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;

        // 配置搜索框键盘处理 / Configure search entry keyboard handling
        ConfigureSearchKeyboardHandling();
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 初始化数据和刷新列表 / Initialize data and refresh list
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        // 初始化视图模型数据 / Initialize view model data
        await _viewModel.InitializeAsync();
    }

    /// <summary>
    /// 页面消失时的处理 / Handle page disappearing
    /// 清理选择状态和资源 / Clean up selection state and resources
    /// </summary>
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        // 清理选择状态 / Clear selection state
        _viewModel.SelectedEmployer = null;
    }

    /// <summary>
    /// 配置搜索框键盘处理 / Configure search entry keyboard handling
    /// 设置搜索框的文本变化和完成事件 / Set up text change and completion events for search entry
    /// </summary>
    private void ConfigureSearchKeyboardHandling()
    {
        try
        {
            if (SearchEntry != null)
            {
                // 搜索文本变化事件 / Search text change event
                SearchEntry.TextChanged += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"AllEmployer: 搜索文本变化 / Search text changed: '{e.NewTextValue}'");
                };

                // 搜索完成事件 / Search completion event
                SearchEntry.Completed += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine("AllEmployer: 搜索完成 / Search completed");
                    SearchEntry.Unfocus(); // 取消焦点 / Remove focus
                };
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"AllEmployer: 配置搜索键盘处理失败 / Failed to configure search keyboard handling - {ex.Message}");
        }
    }
}
