// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 通知测试视图 / Notification Test View
/// 用于测试通知功能和权限管理的调试页面
/// Debug page for testing notification functionality and permission management
/// </summary>
public partial class NotificationTestView : ContentPage
{
    // 通知测试视图模型实例 / Notification test view model instance
    private readonly NotificationTestViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化通知测试视图并配置数据绑定 / Initialize notification test view and configure data binding
    /// </summary>
    /// <param name="viewModel">通知测试视图模型 / Notification test view model</param>
    public NotificationTestView(NotificationTestViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 记录页面出现事件用于调试 / Log page appearing event for debugging
    /// </summary>
    protected override void OnAppearing()
    {
        base.OnAppearing();

        System.Diagnostics.Debug.WriteLine("NotificationTestView: 页面出现 / Page appearing");
    }
}
