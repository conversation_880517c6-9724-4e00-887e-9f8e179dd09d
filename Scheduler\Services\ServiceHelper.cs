namespace Scheduler.Services
{
    /// <summary>
    /// 键盘服务帮助类 / Keyboard Service Helper Class
    /// 用于在非DI环境中获取键盘服务和其他服务
    /// Used to get keyboard service and other services in non-DI environments
    /// </summary>
    public static class KeyboardServiceHelper
    {
        /// <summary>
        /// 获取指定类型的服务 / Get service of specified type
        /// 从应用程序服务提供者中安全获取服务实例
        /// Safely get service instance from application service provider
        /// </summary>
        /// <typeparam name="T">服务类型 / Service type</typeparam>
        /// <returns>服务实例，如果未找到则返回null / Service instance, returns null if not found</returns>
        public static T? GetService<T>() where T : class
        {
            try
            {
                // 尝试从当前应用程序的服务提供者获取服务 / Try to get service from current application service provider
                var serviceProvider = IPlatformApplication.Current?.Services;
                return serviceProvider?.GetService<T>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardServiceHelper: 获取服务 {typeof(T).Name} 失败 / Failed to get service {typeof(T).Name} - {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取必需的服务（如果未找到会抛出异常） / Get required service (throws exception if not found)
        /// 用于获取必须存在的服务，如果服务未注册会抛出异常
        /// Used to get services that must exist, throws exception if service is not registered
        /// </summary>
        /// <typeparam name="T">服务类型 / Service type</typeparam>
        /// <returns>服务实例 / Service instance</returns>
        /// <exception cref="InvalidOperationException">当服务未找到时抛出 / Thrown when service is not found</exception>
        public static T GetRequiredService<T>() where T : class
        {
            var service = GetService<T>();
            if (service == null)
            {
                throw new InvalidOperationException($"无法获取必需的服务 / Cannot get required service: {typeof(T).Name}");
            }
            return service;
        }
    }
}
