using Android.Content;
using Android.Views.InputMethods;
using Scheduler.Services;

namespace Scheduler.Platforms.Android
{
    /// <summary>
    /// Android平台键盘服务实现 - 强制显示软键盘
    /// </summary>
    public class KeyboardService : IKeyboardService
    {
        /// <summary>
        /// 强制显示软键盘
        /// </summary>
        public void ForceShowKeyboard()
        {
            try
            {
                var context = Platform.CurrentActivity ?? Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.ApplicationContext;
                var inputMethodManager = (InputMethodManager?)context.GetSystemService(Context.InputMethodService);
                
                if (inputMethodManager != null)
                {
                    // 方法1：强制显示键盘
                    inputMethodManager.ToggleSoftInput(ShowFlags.Forced, HideSoftInputFlags.ImplicitOnly);
                    
                    // 方法2：如果有当前焦点控件，直接显示
                    if (Platform.CurrentActivity?.CurrentFocus != null)
                    {
                        inputMethodManager.ShowSoftInput(Platform.CurrentActivity.CurrentFocus, ShowFlags.Forced);
                    }
                    
                    System.Diagnostics.Debug.WriteLine("KeyboardService: 强制显示软键盘");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardService: 强制显示软键盘失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 强制隐藏软键盘
        /// </summary>
        public void ForceHideKeyboard()
        {
            try
            {
                var context = Platform.CurrentActivity ?? Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.ApplicationContext;
                var inputMethodManager = (InputMethodManager?)context.GetSystemService(Context.InputMethodService);
                
                if (inputMethodManager != null && Platform.CurrentActivity?.CurrentFocus != null)
                {
                    inputMethodManager.HideSoftInputFromWindow(
                        Platform.CurrentActivity.CurrentFocus.WindowToken, 
                        HideSoftInputFlags.None);
                    
                    System.Diagnostics.Debug.WriteLine("KeyboardService: 强制隐藏软键盘");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardService: 强制隐藏软键盘失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 检查软键盘是否可见
        /// </summary>
        public bool IsKeyboardVisible()
        {
            try
            {
                var context = Platform.CurrentActivity ?? Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.ApplicationContext;
                var inputMethodManager = (InputMethodManager?)context.GetSystemService(Context.InputMethodService);
                
                if (inputMethodManager != null)
                {
                    return inputMethodManager.IsAcceptingText;
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardService: 检查键盘可见性失败 - {ex.Message}");
            }
            
            return false;
        }
    }
}
