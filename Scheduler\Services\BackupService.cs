// 引入必要的命名空间 / Import necessary namespaces
using System.Text.Json;        // JSON序列化 / JSON serialization
using System.IO.Compression;   // 文件压缩 / File compression
using Scheduler.Models;        // 数据模型 / Data models

namespace Scheduler.Services
{
    /// <summary>
    /// 备份恢复服务 / Backup and Restore Service
    /// 负责数据备份、恢复和导出功能
    /// Responsible for data backup, restore and export functionality
    /// </summary>
    public class BackupService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 备份目录路径 / Backup directory path
        private readonly string _backupDirectory;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化备份恢复服务 / Initialize backup and restore service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public BackupService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            _backupDirectory = Path.Combine(FileSystem.AppDataDirectory, "Backups");

            // 确保备份目录存在 / Ensure backup directory exists
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        /// <summary>
        /// 创建完整备份 / Create complete backup
        /// 备份所有应用数据到压缩文件
        /// Backup all application data to compressed file
        /// </summary>
        /// <returns>备份文件路径 / Backup file path</returns>
        public async Task<string> CreateBackupAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"scheduler_backup_{timestamp}.zip";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                var backupData = new BackupData
                {
                    Version = "1.0",
                    CreatedAt = DateTime.Now,
                    Employers = await _databaseService.GetEmployersAsync(),
                    Shifts = await GetAllShiftsAsync(),
                    WorkRecords = await _databaseService.GetWorkRecordsAsync(),
                    PaymentRecords = await _databaseService.GetPaymentRecordsAsync(),
                    NotificationRecords = await _databaseService.GetUserNotificationsAsync(limit: int.MaxValue)
                };

                // 序列化为JSON / Serialize to JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var jsonData = JsonSerializer.Serialize(backupData, jsonOptions);

                // 创建压缩文件
                using (var fileStream = new FileStream(backupPath, FileMode.Create))
                using (var archive = new ZipArchive(fileStream, ZipArchiveMode.Create))
                {
                    var entry = archive.CreateEntry("backup.json");
                    using (var entryStream = entry.Open())
                    using (var writer = new StreamWriter(entryStream))
                    {
                        await writer.WriteAsync(jsonData);
                    }

                    // 添加元数据文件
                    var metadataEntry = archive.CreateEntry("metadata.json");
                    var metadata = new BackupMetadata
                    {
                        Version = backupData.Version,
                        CreatedAt = backupData.CreatedAt,
                        RecordCounts = new Dictionary<string, int>
                        {
                            ["Employers"] = backupData.Employers.Count,
                            ["Shifts"] = backupData.Shifts.Count,
                            ["WorkRecords"] = backupData.WorkRecords.Count,
                            ["PaymentRecords"] = backupData.PaymentRecords.Count,
                            ["NotificationRecords"] = backupData.NotificationRecords.Count
                        }
                    };

                    using (var metadataStream = metadataEntry.Open())
                    using (var metadataWriter = new StreamWriter(metadataStream))
                    {
                        var metadataJson = JsonSerializer.Serialize(metadata, jsonOptions);
                        await metadataWriter.WriteAsync(metadataJson);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"备份创建成功: {backupPath}");
                return backupPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建备份失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 从备份文件恢复数据
        /// </summary>
        public async Task<bool> RestoreFromBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException($"备份文件不存在: {backupPath}");
                }

                BackupData? backupData = null;

                // 读取备份文件
                using (var fileStream = new FileStream(backupPath, FileMode.Open))
                using (var archive = new ZipArchive(fileStream, ZipArchiveMode.Read))
                {
                    var backupEntry = archive.GetEntry("backup.json");
                    if (backupEntry == null)
                    {
                        throw new InvalidDataException("备份文件格式无效：缺少backup.json");
                    }

                    using (var entryStream = backupEntry.Open())
                    using (var reader = new StreamReader(entryStream))
                    {
                        var jsonData = await reader.ReadToEndAsync();
                        var jsonOptions = new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        };
                        backupData = JsonSerializer.Deserialize<BackupData>(jsonData, jsonOptions);
                    }
                }

                if (backupData == null)
                {
                    throw new InvalidDataException("无法解析备份数据");
                }

                // 验证备份数据
                var validationResult = await ValidateBackupDataAsync(backupData);
                if (!validationResult.IsValid)
                {
                    throw new InvalidDataException($"备份数据验证失败: {validationResult.GetErrorsAsString()}");
                }

                // 开始恢复过程
                await RestoreDataAsync(backupData);

                System.Diagnostics.Debug.WriteLine("数据恢复成功");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据恢复失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 自动备份
        /// </summary>
        public async Task<bool> AutoBackupAsync()
        {
            try
            {
                // 检查是否需要自动备份（例如，每周一次）
                var lastBackup = await GetLastBackupDateAsync();
                var daysSinceLastBackup = (DateTime.Now - lastBackup).Days;

                if (daysSinceLastBackup >= 7) // 7天备份一次
                {
                    await CreateBackupAsync();
                    await SaveLastBackupDateAsync(DateTime.Now);
                    
                    // 清理旧备份（保留最近10个）
                    await CleanupOldBackupsAsync(10);
                    
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动备份失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有备份文件
        /// </summary>
        public async Task<List<BackupInfo>> GetBackupListAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, "*.zip");
                var backupInfos = new List<BackupInfo>();

                foreach (var file in backupFiles)
                {
                    try
                    {
                        var metadata = await ReadBackupMetadataAsync(file);
                        backupInfos.Add(new BackupInfo
                        {
                            FilePath = file,
                            FileName = Path.GetFileName(file),
                            CreatedAt = metadata?.CreatedAt ?? File.GetCreationTime(file),
                            FileSize = new FileInfo(file).Length,
                            Version = metadata?.Version ?? "Unknown",
                            RecordCounts = metadata?.RecordCounts ?? new Dictionary<string, int>()
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"读取备份文件元数据失败 {file}: {ex.Message}");
                    }
                }

                return backupInfos.OrderByDescending(b => b.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取备份列表失败: {ex.Message}");
                return new List<BackupInfo>();
            }
        }

        /// <summary>
        /// 删除备份文件
        /// </summary>
        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                    System.Diagnostics.Debug.WriteLine($"备份文件已删除: {backupPath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除备份文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有班次（包括已删除的）
        /// </summary>
        private async Task<List<Shift>> GetAllShiftsAsync()
        {
            // 这里需要一个获取所有班次的方法，包括已删除的
            // 暂时使用现有方法
            var startDate = DateTime.Today.AddYears(-2);
            var endDate = DateTime.Today.AddYears(1);
            return await _databaseService.GetShiftsAsync(startDate, endDate);
        }

        /// <summary>
        /// 验证备份数据
        /// </summary>
        private async Task<ValidationResult> ValidateBackupDataAsync(BackupData backupData)
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(backupData.Version))
            {
                result.AddError("备份版本信息缺失");
            }

            if (backupData.CreatedAt == default)
            {
                result.AddError("备份创建时间无效");
            }

            // 验证数据完整性
            if (backupData.Employers == null)
            {
                result.AddError("雇主数据缺失");
            }

            if (backupData.Shifts == null)
            {
                result.AddError("班次数据缺失");
            }

            if (backupData.WorkRecords == null)
            {
                result.AddError("工作记录数据缺失");
            }

            if (backupData.PaymentRecords == null)
            {
                result.AddError("支付记录数据缺失");
            }

            return result;
        }

        /// <summary>
        /// 恢复数据到数据库
        /// </summary>
        private async Task RestoreDataAsync(BackupData backupData)
        {
            // 注意：这是一个危险操作，会清空现有数据
            // 在实际应用中应该提供更安全的恢复选项

            // 恢复雇主数据
            if (backupData.Employers?.Any() == true)
            {
                await _databaseService.SaveEmployersBatchAsync(backupData.Employers);
            }

            // 恢复班次数据
            if (backupData.Shifts?.Any() == true)
            {
                await _databaseService.SaveShiftsBatchAsync(backupData.Shifts);
            }

            // 恢复工作记录
            if (backupData.WorkRecords?.Any() == true)
            {
                foreach (var record in backupData.WorkRecords)
                {
                    await _databaseService.SaveWorkRecordAsync(record);
                }
            }

            // 恢复支付记录
            if (backupData.PaymentRecords?.Any() == true)
            {
                foreach (var payment in backupData.PaymentRecords)
                {
                    await _databaseService.SavePaymentRecordAsync(payment);
                }
            }

            // 恢复通知记录
            if (backupData.NotificationRecords?.Any() == true)
            {
                foreach (var notification in backupData.NotificationRecords)
                {
                    await _databaseService.SaveNotificationRecordAsync(notification);
                }
            }
        }

        /// <summary>
        /// 读取备份元数据
        /// </summary>
        private async Task<BackupMetadata?> ReadBackupMetadataAsync(string backupPath)
        {
            try
            {
                using (var fileStream = new FileStream(backupPath, FileMode.Open))
                using (var archive = new ZipArchive(fileStream, ZipArchiveMode.Read))
                {
                    var metadataEntry = archive.GetEntry("metadata.json");
                    if (metadataEntry == null) return null;

                    using (var entryStream = metadataEntry.Open())
                    using (var reader = new StreamReader(entryStream))
                    {
                        var jsonData = await reader.ReadToEndAsync();
                        var jsonOptions = new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        };
                        return JsonSerializer.Deserialize<BackupMetadata>(jsonData, jsonOptions);
                    }
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 清理旧备份文件
        /// </summary>
        private async Task CleanupOldBackupsAsync(int keepCount)
        {
            try
            {
                var backups = await GetBackupListAsync();
                var toDelete = backups.Skip(keepCount).ToList();

                foreach (var backup in toDelete)
                {
                    await DeleteBackupAsync(backup.FilePath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理旧备份失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取最后备份日期
        /// </summary>
        private async Task<DateTime> GetLastBackupDateAsync()
        {
            try
            {
                var lastBackupStr = await SecureStorage.GetAsync("LastBackupDate");
                if (DateTime.TryParse(lastBackupStr, out var lastBackup))
                {
                    return lastBackup;
                }
            }
            catch { }
            
            return DateTime.MinValue;
        }

        /// <summary>
        /// 保存最后备份日期
        /// </summary>
        private async Task SaveLastBackupDateAsync(DateTime date)
        {
            try
            {
                await SecureStorage.SetAsync("LastBackupDate", date.ToString("O"));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存备份日期失败: {ex.Message}");
            }
        }
    }


}
