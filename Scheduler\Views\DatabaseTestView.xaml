<?xml version="1.0" encoding="utf-8" ?>
<!--
    数据库测试视图 / Database Test View
    用于测试数据库连接和执行数据库诊断操作
    Used for testing database connections and performing database diagnostic operations
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:Class="Scheduler.Views.DatabaseTestView"
             x:DataType="viewmodels:DatabaseTestViewModel"
             Title="{Binding LocalizedTexts.DatabaseTestTitle}">

    <!-- 页面资源 / Page resources -->
    <ContentPage.Resources>
        <ResourceDictionary>
            <!-- 文本资源 / Text resources -->
            <x:String x:Key="TestRunningText">{Binding LocalizedTexts.TestRunning}</x:String>
            <x:String x:Key="TestIdleText">{Binding LocalizedTexts.RunConnectionTest}</x:String>
        </ResourceDictionary>
    </ContentPage.Resources>

    <ScrollView>
        <VerticalStackLayout Spacing="20" Margin="20">

            <!-- 页面标题区域 / Page title area -->
            <Frame BackgroundColor="#F8F9FA"
                   CornerRadius="12"
                   Padding="20"
                   HasShadow="False">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding LocalizedTexts.DatabaseTestHeader}"
                           FontSize="20"
                           FontAttributes="Bold"
                           TextColor="#2C3E50"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding LocalizedTexts.DatabaseTestDescription}"
                           FontSize="14"
                           TextColor="#7F8C8D"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <!-- 测试按钮区域 -->
            <Frame BackgroundColor="White"
                   CornerRadius="12"
                   Padding="20"
                   HasShadow="True">
                <VerticalStackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.TestOptions}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#2C3E50"/>

                    <!-- 数据库连接测试按钮 -->
                    <Button Text="{Binding IsTestRunning, Converter={StaticResource BoolToObjectConverter}, ConverterParameter='Test Running...|Run Connection Test'}"
                            Command="{Binding RunDatabaseTestCommand}"
                            BackgroundColor="#3498DB"
                            TextColor="White"
                            FontSize="16"
                            FontAttributes="Bold"
                            HeightRequest="50"
                            CornerRadius="25"
                            IsEnabled="{Binding IsTestRunning, Converter={StaticResource InverseBoolConverter}}">
                        <Button.Shadow>
                            <Shadow Brush="#3498DB" Opacity="0.3" Radius="8" Offset="0,4"/>
                        </Button.Shadow>
                    </Button>

                    <!-- 重置功能测试按钮 -->
                    <Button Text="{Binding LocalizedTexts.TestResetFunction}"
                            Command="{Binding RunResetTestCommand}"
                            BackgroundColor="#E74C3C"
                            TextColor="White"
                            FontSize="16"
                            FontAttributes="Bold"
                            HeightRequest="50"
                            CornerRadius="25"
                            IsEnabled="{Binding IsTestRunning, Converter={StaticResource InverseBoolConverter}}">
                        <Button.Shadow>
                            <Shadow Brush="#E74C3C" Opacity="0.3" Radius="8" Offset="0,4"/>
                        </Button.Shadow>
                    </Button>

                    <!-- 重新启用演示数据按钮 -->
                    <Button Text="{Binding LocalizedTexts.ReenableDemoData}"
                            Command="{Binding EnableDemoDataCommand}"
                            BackgroundColor="#27AE60"
                            TextColor="White"
                            FontSize="14"
                            HeightRequest="40"
                            CornerRadius="20"
                            IsEnabled="{Binding IsTestRunning, Converter={StaticResource InverseBoolConverter}}"/>

                    <!-- 清空结果按钮 -->
                    <Button Text="{Binding LocalizedTexts.ClearResults}"
                            Command="{Binding ClearResultsCommand}"
                            BackgroundColor="#95A5A6"
                            TextColor="White"
                            FontSize="14"
                            HeightRequest="40"
                            CornerRadius="20"
                            IsEnabled="{Binding IsTestRunning, Converter={StaticResource InverseBoolConverter}}"/>
                </VerticalStackLayout>
            </Frame>

            <!-- 测试状态指示器 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="12" 
                   Padding="20"
                   HasShadow="True"
                   IsVisible="{Binding TestCompleted}">
                <HorizontalStackLayout Spacing="15" HorizontalOptions="Center">
                    <Label Text="{Binding AllTestsPassed, Converter={StaticResource BoolToObjectConverter}, ConverterParameter='✅|❌'}"
                           FontSize="24"/>
                    <Label Text="{Binding AllTestsPassed, Converter={StaticResource BoolToObjectConverter}, ConverterParameter='All Tests Passed|Tests Found Issues'}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="{Binding AllTestsPassed, Converter={StaticResource BoolToObjectConverter}, ConverterParameter='#27AE60|#E74C3C'}"
                           VerticalOptions="Center"/>
                </HorizontalStackLayout>
            </Frame>

            <!-- 加载指示器 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="12" 
                   Padding="20"
                   HasShadow="True"
                   IsVisible="{Binding IsTestRunning}">
                <HorizontalStackLayout Spacing="15" HorizontalOptions="Center">
                    <ActivityIndicator IsRunning="{Binding IsTestRunning}"
                                     Color="#3498DB"
                                     WidthRequest="24"
                                     HeightRequest="24"/>
                    <Label Text="{Binding LocalizedTexts.RunningTests}"
                           FontSize="16"
                           TextColor="#3498DB"
                           VerticalOptions="Center"/>
                </HorizontalStackLayout>
            </Frame>

            <!-- 测试结果显示区域 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="12" 
                   Padding="20"
                   HasShadow="True">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding LocalizedTexts.TestResults}"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#2C3E50"/>
                    
                    <Frame BackgroundColor="#F8F9FA"
                           CornerRadius="8"
                           Padding="15"
                           HasShadow="False">
                        <ScrollView MaximumHeightRequest="400">
                            <Label Text="{Binding TestResults}"
                                   FontSize="12"
                                   FontFamily="Courier"
                                   TextColor="#2C3E50"
                                   LineBreakMode="WordWrap"/>
                        </ScrollView>
                    </Frame>
                </VerticalStackLayout>
            </Frame>

            <!-- 返回按钮 -->
            <Button Text="{Binding LocalizedTexts.BackToSettings}"
                    Command="{Binding GoBackCommand}"
                    BackgroundColor="#34495E"
                    TextColor="White"
                    FontSize="16"
                    HeightRequest="50"
                    CornerRadius="25"
                    Margin="0,20,0,0">
                <Button.Shadow>
                    <Shadow Brush="#34495E" Opacity="0.3" Radius="8" Offset="0,4"/>
                </Button.Shadow>
            </Button>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
