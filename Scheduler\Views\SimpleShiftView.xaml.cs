// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 简单班次视图 / Simple Shift View
/// 用于快速创建和编辑工作班次的简化表单页面
/// Simplified form page for quickly creating and editing work shifts
/// </summary>
[QueryProperty(nameof(ShiftId), "shiftId")]
public partial class SimpleShiftView : ContentPage
{
    // 简单班次视图模型实例 / Simple shift view model instance
    private readonly SimpleShiftViewModel _viewModel;

    /// <summary>
    /// 班次ID查询参数 / Shift ID query parameter
    /// 用于编辑模式时传递班次ID / Used to pass shift ID in edit mode
    /// </summary>
    public string? ShiftId { get; set; }

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化简单班次视图并配置数据绑定 / Initialize simple shift view and configure data binding
    /// </summary>
    /// <param name="viewModel">简单班次视图模型 / Simple shift view model</param>
    public SimpleShiftView(SimpleShiftViewModel viewModel)
    {
        System.Diagnostics.Debug.WriteLine("📋 SimpleShiftView: 开始构造 / Starting construction");
        try
        {
            // 初始化XAML组件 / Initialize XAML components
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = viewModel;
            System.Diagnostics.Debug.WriteLine("✅ SimpleShiftView: 构造完成 / Construction completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ SimpleShiftView: 构造失败 / Construction failed - {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"   异常类型 / Exception type: {ex.GetType().Name}");
            throw;
        }
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 初始化班次数据和雇主列表 / Initialize shift data and employer list
    /// </summary>
    protected override async void OnAppearing()
    {
        System.Diagnostics.Debug.WriteLine("📋 SimpleShiftView: OnAppearing 开始 / OnAppearing starting");
        base.OnAppearing();
        try
        {
            System.Diagnostics.Debug.WriteLine("📋 SimpleShiftView: 开始初始化视图模型 / Starting view model initialization");

            // 解析班次ID（如果有的话） / Parse shift ID (if provided)
            int? shiftId = null;
            if (!string.IsNullOrEmpty(ShiftId) && int.TryParse(ShiftId, out var parsedId))
            {
                shiftId = parsedId;
                System.Diagnostics.Debug.WriteLine($"📋 SimpleShiftView: 编辑模式，班次ID / Edit mode, shift ID: {shiftId}");
            }

            // 初始化视图模型 / Initialize view model
            await _viewModel.InitializeAsync(shiftId);
            System.Diagnostics.Debug.WriteLine($"✅ SimpleShiftView: 初始化完成，雇主数量 / Initialization completed, employer count: {_viewModel.Employers.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ SimpleShiftView: OnAppearing 失败 / OnAppearing failed - {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"   异常类型 / Exception type: {ex.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"   堆栈跟踪 / Stack trace: {ex.StackTrace}");
        }
    }
}
