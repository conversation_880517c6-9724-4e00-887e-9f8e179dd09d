// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel; // MVVM组件模型 / MVVM component model

namespace Scheduler.Models
{
    /// <summary>
    /// 颜色选项模型 / Color Option Model
    /// 用于雇主颜色选择和主题配置
    /// Used for employer color selection and theme configuration
    /// </summary>
    public partial class ColorOption : ObservableObject
    {
        /// <summary>
        /// 颜色的十六进制值 / Hexadecimal Color Value
        /// 颜色的标准十六进制表示（如 #FF5722）
        /// Standard hexadecimal representation of the color (e.g., #FF5722)
        /// </summary>
        public string HexValue { get; set; } = string.Empty;

        /// <summary>
        /// 颜色的显示名称 / Display Name of Color
        /// 用户界面中显示的颜色名称
        /// Color name displayed in the user interface
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 颜色的英文名称 / English Name of Color
        /// 颜色的英文标准名称
        /// Standard English name of the color
        /// </summary>
        public string EnglishName { get; set; } = string.Empty;

        /// <summary>
        /// 是否被选中 / Is Selected
        /// 标识该颜色选项是否当前被选中
        /// Indicates whether this color option is currently selected
        /// </summary>
        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// 颜色分类 / Color Category
        /// 颜色的分类标签（如工作场景、情绪等）
        /// Category label for the color (e.g., work scenario, mood, etc.)
        /// </summary>
        public string Category { get; set; } = string.Empty;
    }
}
