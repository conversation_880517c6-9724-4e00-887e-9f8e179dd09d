// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using Scheduler.Services;                     // 应用服务 / Application services

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 登录页面视图模型 / Login Page ViewModel
    /// 处理用户身份验证和登录逻辑
    /// Handles user authentication and login logic
    /// </summary>
    public partial class LoginViewModel : BaseViewModel
    {
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化登录视图模型和相关服务 / Initialize login view model and related services
        /// </summary>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        /// <param name="autoCloseAlertService">自动关闭提示服务（可选） / Auto-close alert service (optional)</param>
        public LoginViewModel(LocalizationService localizationService, IAutoCloseAlertService? autoCloseAlertService = null) : base(autoCloseAlertService)
        {
            _localizationService = localizationService;
            Title = "Login";

            // 开发环境默认值，生产环境应移除 / Development default values, should be removed in production
            Username = "test";
            Password = "text";

            // 监听属性变化以更新UI状态 / Listen to property changes to update UI state
            PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(IsBusy))
                {
                    OnPropertyChanged(nameof(IsLoginEnabled));
                }
            };
        }

        /// <summary>
        /// 本地化文本集合 / Localized texts collection
        /// 提供界面显示的本地化文本 / Provides localized texts for UI display
        /// </summary>
        public LocalizedTexts LocalizedTexts => new(_localizationService);

        #region Observable Properties

        /// <summary>
        /// 用户名
        /// </summary>
        [ObservableProperty]
        private string username = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [ObservableProperty]
        private string password = string.Empty;

        /// <summary>
        /// 是否显示密码
        /// </summary>
        [ObservableProperty]
        private bool isPasswordVisible = false;

        /// <summary>
        /// 记住我选项
        /// </summary>
        [ObservableProperty]
        private bool rememberMe = false;

        /// <summary>
        /// 登录按钮是否可用
        /// </summary>
        public bool IsLoginEnabled => !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password) && !IsBusy;

        #endregion

        #region Commands

        /// <summary>
        /// 登录命令
        /// </summary>
        [RelayCommand]
        private async Task LoginAsync()
        {
            if (IsBusy) return;

            await SafeExecuteAsync(async () =>
            {
                // 测试阶段：简单验证，任何非空用户名密码都可以登录
                if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
                {
                    await ShowErrorAsync("Please enter both username and password.", "Login Required");
                    return;
                }

                // 模拟登录过程
                await Task.Delay(1000); // 模拟网络请求延迟

                // 测试阶段：直接导航到主页面
                await Shell.Current.GoToAsync("//Home");
                
                // 清空密码（安全考虑）
                Password = "";
                
                await ShowSuccessAsync("Login successful!", "Welcome");
            });
        }

        /// <summary>
        /// 切换密码可见性命令
        /// </summary>
        [RelayCommand]
        private void TogglePasswordVisibility()
        {
            IsPasswordVisible = !IsPasswordVisible;
        }

        /// <summary>
        /// 忘记密码命令
        /// </summary>
        [RelayCommand]
        private async Task ForgotPasswordAsync()
        {
            await ShowSuccessAsync("Password reset feature coming soon!", "Forgot Password");
        }

        /// <summary>
        /// 注册命令
        /// </summary>
        [RelayCommand]
        private async Task RegisterAsync()
        {
            await ShowSuccessAsync("Registration feature coming soon!", "Register");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 属性变化处理
        /// </summary>
        partial void OnUsernameChanged(string value)
        {
            OnPropertyChanged(nameof(IsLoginEnabled));
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        partial void OnPasswordChanged(string value)
        {
            OnPropertyChanged(nameof(IsLoginEnabled));
        }



        #endregion
    }
}
