// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Services;
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 键盘测试视图 / Keyboard Test View
/// 用于测试不同类型键盘输入和焦点管理的调试页面
/// Debug page for testing different keyboard input types and focus management
/// </summary>
public partial class KeyboardTestView : ContentPage
{
    // 键盘服务实例 / Keyboard service instance
    private readonly IKeyboardService? _keyboardService;
    // 本地化服务实例 / Localization service instance
    private readonly LocalizationService _localizationService;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化键盘测试视图并配置服务 / Initialize keyboard test view and configure services
    /// </summary>
    public KeyboardTestView()
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();

        // 尝试获取键盘服务 / Try to get keyboard service
        try
        {
            _keyboardService = KeyboardServiceHelper.GetService<IKeyboardService>();
        }
        catch
        {
            // 如果获取失败，继续运行但不使用键盘服务 / If retrieval fails, continue without keyboard service
            _keyboardService = null;
        }

        // 获取本地化服务 / Get localization service
        _localizationService = DependencyService.Get<LocalizationService>();

        // 设置本地化文本 / Set localized texts
        BindingContext = new LocalizedTexts(_localizationService);

        // 配置简化的事件处理 / Configure simplified event handling
        ConfigureSimpleLogging();
    }

    /// <summary>
    /// 配置简单的日志记录 / Configure simple logging
    /// 为输入控件添加焦点事件记录 / Add focus event logging for input controls
    /// </summary>
    private void ConfigureSimpleLogging()
    {
        try
        {
            // 为每个输入控件添加获得焦点事件记录 / Add focus gained event logging for each input control
            BasicTextEntry.Focused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: BasicTextEntry 获得焦点 / BasicTextEntry gained focus");

            NumericEntry.Focused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: NumericEntry 获得焦点 / NumericEntry gained focus");

            EmailEntry.Focused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: EmailEntry 获得焦点 / EmailEntry gained focus");

            BasicEditor.Focused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: BasicEditor 获得焦点 / BasicEditor gained focus");

            // 为每个输入控件添加失去焦点事件记录 / Add focus lost event logging for each input control
            BasicTextEntry.Unfocused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: BasicTextEntry 失去焦点 / BasicTextEntry lost focus");

            NumericEntry.Unfocused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: NumericEntry 失去焦点 / NumericEntry lost focus");

            EmailEntry.Unfocused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: EmailEntry 失去焦点 / EmailEntry lost focus");

            BasicEditor.Unfocused += (s, e) =>
                System.Diagnostics.Debug.WriteLine("KeyboardTest: BasicEditor 失去焦点 / BasicEditor lost focus");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"KeyboardTest: 配置日志记录失败 / Failed to configure logging - {ex.Message}");
        }
    }

    /// <summary>
    /// Entry控件获得焦点时的处理 / Handle Entry control focus gained
    /// 强制显示软键盘 / Force show soft keyboard
    /// </summary>
    private void OnEntryFocused(object sender, FocusEventArgs e)
    {
        if (e.IsFocused && sender is Entry entry)
        {
            try
            {
                // 强制显示软键盘界面 / Force show soft keyboard
                _keyboardService?.ForceShowKeyboard();
                System.Diagnostics.Debug.WriteLine($"KeyboardTest: {entry.Placeholder} 获得焦点，强制显示键盘 / {entry.Placeholder} gained focus, forced keyboard display");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardTest: Entry焦点处理失败 / Entry focus handling failed - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Editor控件获得焦点时的处理 / Handle Editor control focus gained
    /// 强制显示软键盘 / Force show soft keyboard
    /// </summary>
    private void OnEditorFocused(object sender, FocusEventArgs e)
    {
        if (e.IsFocused && sender is Editor editor)
        {
            try
            {
                // 强制显示软键盘界面 / Force show soft keyboard
                _keyboardService?.ForceShowKeyboard();
                System.Diagnostics.Debug.WriteLine($"KeyboardTest: {editor.Placeholder} 获得焦点，强制显示键盘 / {editor.Placeholder} gained focus, forced keyboard display");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"KeyboardTest: Editor焦点处理失败 / Editor focus handling failed - {ex.Message}");
            }
        }
    }
}
