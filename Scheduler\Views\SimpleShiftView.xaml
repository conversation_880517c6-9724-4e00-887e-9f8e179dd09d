<?xml version="1.0" encoding="utf-8" ?>
<!--
    简单班次视图 / Simple Shift View
    用于快速创建和编辑工作班次的简化表单页面
    Simplified form page for quickly creating and editing work shifts
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:Class="Scheduler.Views.SimpleShiftView"
             x:DataType="viewmodels:SimpleShiftViewModel"
             Title="{Binding PageTitle}"
             BackgroundColor="#F8F9FA"
             Shell.FlyoutBehavior="Disabled"
             Shell.TabBarIsVisible="False">

    <!-- 返回按钮配置 / Back button configuration -->
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="True" IsVisible="True" />
    </Shell.BackButtonBehavior>

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="20">

            <!-- 页面标题区域 / Page title area -->
            <Frame BackgroundColor="#007ACC"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Shift Management"
                           FontSize="24"
                           FontAttributes="Bold"
                           TextColor="White"
                           HorizontalOptions="Center"/>
                    <Label Text="Create and manage your work shifts"
                           FontSize="14"
                           TextColor="White"
                           Opacity="0.8"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <!-- 班次创建表单 / Shift creation form -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">

                <VerticalStackLayout Spacing="15">
                    <Label Text="Shift Information"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>

                    <!-- 雇主选择 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="Employer *"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                            <Picker Grid.Column="0"
                                    ItemsSource="{Binding Employers}"
                                    ItemDisplayBinding="{Binding Name}"
                                    SelectedItem="{Binding SelectedEmployer}"
                                    Title="Select an employer"
                                    BackgroundColor="White"
                                    TextColor="#333"/>
                            <Button Grid.Column="1"
                                    Text="+"
                                    BackgroundColor="#28A745"
                                    TextColor="White"
                                    FontSize="18"
                                    FontAttributes="Bold"
                                    WidthRequest="40"
                                    HeightRequest="40"
                                    CornerRadius="20"
                                    Command="{Binding AddEmployerCommand}"
                                    ToolTipProperties.Text="Add new employer"/>
                        </Grid>
                    </VerticalStackLayout>

                    <!-- 时薪 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="Hourly Rate ($/hour) *"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Entry Placeholder="Enter hourly rate"
                               Text="{Binding HourlyRate}"
                               Keyboard="Numeric"
                               BackgroundColor="White"
                               TextColor="#333"/>
                    </VerticalStackLayout>

                    <!-- 开始时间 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="Start Time *"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                            <DatePicker Grid.Column="0"
                                       Date="{Binding StartDate}"
                                       BackgroundColor="White"
                                       TextColor="#333"/>
                            <TimePicker Grid.Column="1"
                                       Time="{Binding StartTime}"
                                       BackgroundColor="White"
                                       TextColor="#333"/>
                        </Grid>
                    </VerticalStackLayout>

                    <!-- 结束时间 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="End Time *"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                            <DatePicker Grid.Column="0"
                                       Date="{Binding EndDate}"
                                       BackgroundColor="White"
                                       TextColor="#333"/>
                            <TimePicker Grid.Column="1"
                                       Time="{Binding EndTime}"
                                       BackgroundColor="White"
                                       TextColor="#333"/>
                        </Grid>
                    </VerticalStackLayout>

                    <!-- 预览信息 -->
                    <Frame BackgroundColor="#EBF8FF"
                           HasShadow="False"
                           CornerRadius="8"
                           Padding="15">
                        <VerticalStackLayout Spacing="5">
                            <Label Text="{Binding WorkHours, StringFormat='Work Hours: {0:F1} hours'}"
                                   FontSize="14"
                                   TextColor="#1E40AF"/>
                            <Label Text="{Binding EstimatedEarnings, StringFormat='Estimated Earnings: ${0:F2}'}"
                                   FontSize="14"
                                   FontAttributes="Bold"
                                   TextColor="#1E40AF"/>
                        </VerticalStackLayout>
                    </Frame>
                </VerticalStackLayout>
            </Frame>

            <!-- 通知设置 (Notification Settings) -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="16">
                    <!-- 通知设置标题和开关 -->
                    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="16">
                        <Label Grid.Column="0"
                               Text="🔔 Shift Reminder"
                               FontSize="18"
                               FontAttributes="Bold"
                               VerticalOptions="Center"
                               TextColor="#007ACC"/>
                        <Switch Grid.Column="1"
                                IsToggled="{Binding IsReminderEnabled}"
                                OnColor="#28A745"
                                ThumbColor="White"/>
                    </Grid>

                    <!-- 通知设置详细选项 -->
                    <VerticalStackLayout IsVisible="{Binding IsReminderEnabled}" Spacing="16">

                        <!-- 提醒时间选择 -->
                        <VerticalStackLayout Spacing="8">
                            <Label Text="Reminder Time *"
                                   FontAttributes="Bold"
                                   FontSize="14"
                                   TextColor="#007ACC"/>
                            <Picker ItemsSource="{Binding ReminderTimeOptions}"
                                    ItemDisplayBinding="{Binding DisplayName}"
                                    SelectedItem="{Binding SelectedReminderTimeOption}"
                                    Title="Select reminder time"
                                    BackgroundColor="White"
                                    TextColor="#333"/>
                            <Label Text="{Binding SelectedReminderTimeOption.Description}"
                                   FontSize="12"
                                   TextColor="#6B7280"/>
                        </VerticalStackLayout>

                        <!-- 通知预览信息 -->
                        <Frame BackgroundColor="#F0FDF4"
                               HasShadow="False"
                               CornerRadius="8"
                               Padding="12">
                            <VerticalStackLayout Spacing="4">
                                <Label Text="📱 Notification Preview"
                                       FontAttributes="Bold"
                                       FontSize="14"
                                       TextColor="#15803D"/>
                                <Label Text="{Binding SelectedReminderMinutes, StringFormat='You will receive a reminder {0} minutes before the shift starts'}"
                                       FontSize="12"
                                       TextColor="#15803D"/>
                                <Label Text="Notification will respect your quiet hours settings"
                                       FontSize="11"
                                       FontAttributes="Italic"
                                       TextColor="#6B7280"/>
                            </VerticalStackLayout>
                        </Frame>

                    </VerticalStackLayout>
                </VerticalStackLayout>
            </Frame>

            <!-- 操作按钮 -->
            <VerticalStackLayout Spacing="15">
                <!-- 保存按钮 -->
                <Button Text="{Binding SaveButtonText}"
                        Command="{Binding SaveShiftCommand}"
                        BackgroundColor="#28A745"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        CornerRadius="25"/>

                <!-- 查看班次按钮 -->
                <Button Text="View All Shifts"
                        Command="{Binding ViewShiftsCommand}"
                        BackgroundColor="#007ACC"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"/>

                <!-- 取消按钮 -->
                <Button Text="Don't Save and Return"
                        Command="{Binding CancelCommand}"
                        BackgroundColor="#6C757D"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"/>
            </VerticalStackLayout>

            <!-- 状态信息 -->
            <Label Text="{Binding StatusMessage}"
                   FontSize="14"
                   HorizontalOptions="Center"
                   TextColor="#6B7280"
                   IsVisible="{Binding HasStatusMessage}"/>

            <!-- 加载指示器 -->
            <ActivityIndicator IsVisible="{Binding IsBusy}"
                               IsRunning="{Binding IsBusy}"
                               Color="#007ACC"
                               HeightRequest="50"/>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>