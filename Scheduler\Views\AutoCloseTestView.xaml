<?xml version="1.0" encoding="utf-8" ?>
<!--
    自动关闭测试视图 / Auto Close Test View
    用于测试Toast和Snackbar自动关闭功能的调试页面
    Debug page for testing Toast and Snackbar auto-close functionality
-->
<ContentPage x:Class="Scheduler.Views.AutoCloseTestView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="{Binding LocalizedTexts.AutoCloseTestTitle}">
    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="20">
            <!-- 页面标题 / Page title -->
            <Label Text="{Binding LocalizedTexts.AutoCloseTestHeader}"
                   FontSize="24"
                   FontAttributes="Bold"
                   HorizontalOptions="Center" />

            <!-- 说明文本 / Instructions text -->
            <Label Text="{Binding LocalizedTexts.AutoCloseTestInstructions}"
                   FontSize="16"
                   HorizontalOptions="Center" />

            <!-- 成功Toast测试按钮 / Success toast test button -->
            <Button Text="{Binding LocalizedTexts.TestSuccessToast}"
                    Command="{Binding TestSuccessCommand}"
                    BackgroundColor="Green"
                    TextColor="White" />

            <!-- 错误Snackbar测试按钮 / Error snackbar test button -->
            <Button Text="{Binding LocalizedTexts.TestErrorSnackbar}"
                    Command="{Binding TestErrorCommand}"
                    BackgroundColor="Red"
                    TextColor="White" />

            <!-- 信息Toast测试按钮 / Info toast test button -->
            <Button Text="{Binding LocalizedTexts.TestInfoToast}"
                    Command="{Binding TestInfoCommand}"
                    BackgroundColor="Blue"
                    TextColor="White" />

            <!-- 警告Snackbar测试按钮 / Warning snackbar test button -->
            <Button Text="{Binding LocalizedTexts.TestWarningSnackbar}"
                    Command="{Binding TestWarningCommand}"
                    BackgroundColor="Orange"
                    TextColor="White" />

            <!-- 自动关闭说明 / Auto close note -->
            <Label Text="{Binding LocalizedTexts.AutoCloseNote}"
                   FontSize="14"
                   TextColor="Gray"
                   HorizontalOptions="Center" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
