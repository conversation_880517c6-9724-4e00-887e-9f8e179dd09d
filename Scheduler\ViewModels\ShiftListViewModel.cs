// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 班次列表视图模型 / Shift List ViewModel
    /// 显示和管理班次列表，支持搜索和筛选
    /// Displays and manages shift list with search and filtering support
    /// </summary>
    public partial class ShiftListViewModel : BaseViewModel, IRecipient<ShiftCreatedMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知调度器实例（可选） / Notification scheduler instance (optional)
        private readonly NotificationScheduler? _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 班次集合 / Shifts collection
        [ObservableProperty]
        private ObservableCollection<ShiftDisplayModel> shifts = new();

        // 选中的班次 / Selected shift
        [ObservableProperty]
        private ShiftDisplayModel? selectedShift;

        // 搜索文本 / Search text
        [ObservableProperty]
        private string searchText = string.Empty;

        // 刷新状态 / Refresh state
        [ObservableProperty]
        private bool isRefreshing;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化班次列表视图模型 / Initialize shift list view model
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        /// <param name="notificationScheduler">通知调度器（可选） / Notification scheduler (optional)</param>
        public ShiftListViewModel(DatabaseService databaseService, LocalizationService localizationService, NotificationScheduler? notificationScheduler = null)
        {
            _databaseService = databaseService;
            _localizationService = localizationService;
            _notificationScheduler = notificationScheduler;
            Title = "班次列表";

            // 注册消息接收 / Register message reception
            WeakReferenceMessenger.Default.Register<ShiftCreatedMessage>(this);
        }

        public async Task InitializeAsync()
        {
            await LoadShiftsAsync();
        }

        private async Task LoadShiftsAsync()
        {
            try
            {
                IsBusy = true;
                
                var shiftList = await _databaseService.GetShiftsWithEmployerInfoAsync();
                
                Shifts.Clear();
                foreach (var shift in shiftList.OrderByDescending(s => s.StartTime))
                {
                    Shifts.Add(new ShiftDisplayModel
                    {
                        Id = shift.Id,
                        EmployerName = shift.EmployerName ?? "未知雇主",
                        StartTime = shift.StartTime,
                        EndTime = shift.EndTime,
                        HourlyRate = shift.HourlyRate,
                        WorkHours = (shift.EndTime - shift.StartTime).TotalHours,
                        EstimatedEarnings = (decimal)(shift.EndTime - shift.StartTime).TotalHours * shift.HourlyRate,
                        Status = shift.Status,
                        StatusText = GetStatusText(shift.Status),
                        StatusColor = GetStatusColor(shift.Status),
                        Notes = shift.Notes
                    });
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"加载班次列表失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private string GetStatusText(ShiftStatus status)
        {
            return status switch
            {
                ShiftStatus.Scheduled => "已安排",
                ShiftStatus.InProgress => "进行中",
                ShiftStatus.Completed => "已完成",
                ShiftStatus.Cancelled => "已取消",
                _ => "未知"
            };
        }

        private Color GetStatusColor(ShiftStatus status)
        {
            return status switch
            {
                ShiftStatus.Scheduled => Colors.Blue,
                ShiftStatus.InProgress => Colors.Orange,
                ShiftStatus.Completed => Colors.Green,
                ShiftStatus.Cancelled => Colors.Red,
                _ => Colors.Gray
            };
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsRefreshing = true;
            await LoadShiftsAsync();
            IsRefreshing = false;
        }

        [RelayCommand]
        private async Task AddShiftAsync()
        {
            await Shell.Current.GoToAsync("ShiftManagement");
        }

        [RelayCommand]
        private async Task ShowFilterAsync()
        {
            await ShowSuccessAsync("筛选功能即将推出！");
        }

        [RelayCommand]
        private async Task ShowShiftOptionsAsync(ShiftDisplayModel shift)
        {
            if (shift == null) return;

            var action = await Shell.Current.DisplayActionSheet(
                $"{shift.EmployerName} - {shift.StartTime:MM/dd HH:mm}",
                "取消",
                null,
                "编辑班次",
                "删除班次",
                "标记完成",
                "查看详情");

            switch (action)
            {
                case "编辑班次":
                    await EditShiftAsync(shift);
                    break;
                case "删除班次":
                    await DeleteShiftAsync(shift);
                    break;
                case "标记完成":
                    await MarkShiftCompletedAsync(shift);
                    break;
                case "查看详情":
                    await ShowShiftDetailsAsync(shift);
                    break;
            }
        }

        [RelayCommand]
        private async Task EditShift(ShiftDisplayModel shift)
        {
            await Shell.Current.GoToAsync($"ShiftManagement?shiftId={shift.Id}");
        }

        private async Task EditShiftAsync(ShiftDisplayModel shift)
        {
            await EditShift(shift);
        }

        private async Task DeleteShiftAsync(ShiftDisplayModel shift)
        {
            var confirm = await Shell.Current.DisplayAlert(
                "确认删除",
                $"确定要删除 {shift.EmployerName} 的班次吗？",
                "删除",
                "取消");

            if (confirm)
            {
                try
                {
                    // 取消相关通知
                    if (_notificationScheduler != null)
                    {
                        await _notificationScheduler.CancelShiftNotificationsAsync(shift.Id);
                    }

                    await _databaseService.DeleteShiftAsync(shift.Id);
                    Shifts.Remove(shift);
                    await ShowSuccessAsync("班次已删除");
                }
                catch (Exception ex)
                {
                    await ShowErrorAsync($"删除班次失败: {ex.Message}");
                }
            }
        }

        private async Task MarkShiftCompletedAsync(ShiftDisplayModel shift)
        {
            try
            {
                var shiftEntity = await _databaseService.GetShiftAsync(shift.Id);
                if (shiftEntity != null)
                {
                    shiftEntity.Status = ShiftStatus.Completed;
                    await _databaseService.UpdateShiftAsync(shiftEntity);
                    
                    shift.Status = ShiftStatus.Completed;
                    shift.StatusText = GetStatusText(ShiftStatus.Completed);
                    shift.StatusColor = GetStatusColor(ShiftStatus.Completed);
                    
                    await ShowSuccessAsync("班次已标记为完成");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"更新班次状态失败: {ex.Message}");
            }
        }

        private async Task ShowShiftDetailsAsync(ShiftDisplayModel shift)
        {
            var employerText = _localizationService.GetLocalizedString("Employer");
            var startTimeText = _localizationService.GetLocalizedString("StartTime");
            var endTimeText = _localizationService.GetLocalizedString("EndTime");
            var workHoursText = _localizationService.GetLocalizedString("WorkHours");
            var hourlyRateText = _localizationService.GetLocalizedString("HourlyRate");
            var estimatedEarningsText = _localizationService.GetLocalizedString("EstimatedEarnings");
            var statusText = _localizationService.GetLocalizedString("Status");
            var hoursText = _localizationService.GetLocalizedString("Hours");

            var details = $"{employerText}: {shift.EmployerName}\n" +
                         $"{startTimeText}: {shift.StartTime:yyyy/MM/dd HH:mm}\n" +
                         $"{endTimeText}: {shift.EndTime:yyyy/MM/dd HH:mm}\n" +
                         $"{workHoursText}: {shift.WorkHours:F1} {hoursText}\n" +
                         $"{hourlyRateText}: ¥{shift.HourlyRate:F2}\n" +
                         $"{estimatedEarningsText}: ¥{shift.EstimatedEarnings:F2}\n" +
                         $"{statusText}: {shift.StatusText}";

            if (!string.IsNullOrEmpty(shift.Notes))
            {
                var notesText = _localizationService.GetLocalizedString("Notes");
                details += $"\n{notesText}: {shift.Notes}";
            }

            var shiftDetailsTitle = _localizationService.GetLocalizedString("ShiftDetails");
            var okText = _localizationService.GetLocalizedString("OK");
            await Shell.Current.DisplayAlert(shiftDetailsTitle, details, okText);
        }

        // 实现消息接收接口
        public void Receive(ShiftCreatedMessage message)
        {
            // 当有新班次创建时，刷新列表
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await LoadShiftsAsync();
            });
        }

        public async Task OnPageAppearingAsync()
        {
            // 页面激活时刷新数据
            await LoadShiftsAsync();
        }

        /// <summary>
        /// 本地化文本 - Localized texts for XAML binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);
    }

    // 班次显示模型
    public partial class ShiftDisplayModel : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string employerName = string.Empty;

        [ObservableProperty]
        private DateTime startTime;

        [ObservableProperty]
        private DateTime endTime;

        [ObservableProperty]
        private decimal hourlyRate;

        [ObservableProperty]
        private double workHours;

        [ObservableProperty]
        private decimal estimatedEarnings;

        [ObservableProperty]
        private ShiftStatus status;

        [ObservableProperty]
        private string statusText = string.Empty;

        [ObservableProperty]
        private Color statusColor = Colors.Gray;

        [ObservableProperty]
        private string? notes;
    }
}
