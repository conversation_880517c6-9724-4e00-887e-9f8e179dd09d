namespace Scheduler.Models
{
    /// <summary>
    /// 日志确认设置变化消息 / Log Confirmation Setting Changed Message
    /// 用于通知日志确认设置的变更
    /// Used to notify changes in log confirmation settings
    /// </summary>
    public class LogConfirmationSettingChangedMessage
    {
        /// <summary>
        /// 是否启用 / Is Enabled
        /// 标识日志确认功能是否启用
        /// Indicates whether log confirmation feature is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化日志确认设置变化消息
        /// Initialize log confirmation setting changed message
        /// </summary>
        /// <param name="isEnabled">是否启用 / Is enabled</param>
        public LogConfirmationSettingChangedMessage(bool isEnabled)
        {
            IsEnabled = isEnabled;
        }
    }

    /// <summary>
    /// 雇主更新消息 / Employer Updated Message
    /// 用于通知雇主信息的变更
    /// Used to notify changes in employer information
    /// </summary>
    public class EmployerUpdatedMessage
    {
        /// <summary>雇主ID / Employer ID</summary>
        public int EmployerId { get; set; }
        /// <summary>操作类型 / Action Type - "Created", "Updated", "Deleted"</summary>
        public string Action { get; set; } = string.Empty;
    }

    /// <summary>
    /// 支付记录更新消息 / Payment Record Updated Message
    /// 用于通知支付记录的变更
    /// Used to notify changes in payment records
    /// </summary>
    public class PaymentRecordUpdatedMessage
    {
        /// <summary>支付记录ID / Payment Record ID</summary>
        public int PaymentRecordId { get; set; }
        /// <summary>操作类型 / Action Type - "Created", "Updated", "Confirmed", "Cancelled"</summary>
        public string Action { get; set; } = string.Empty;
        /// <summary>雇主ID / Employer ID</summary>
        public int EmployerId { get; set; }
        /// <summary>金额 / Amount</summary>
        public decimal Amount { get; set; }
        /// <summary>更新时间 / Update Time</summary>
        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 工作记录更新消息 / Work Record Updated Message
    /// 用于通知工作记录的变更
    /// Used to notify changes in work records
    /// </summary>
    public class WorkRecordUpdatedMessage
    {
        /// <summary>工作记录ID / Work Record ID</summary>
        public int WorkRecordId { get; set; }
        /// <summary>操作类型 / Action Type - "Created", "Updated", "Deleted"</summary>
        public string Action { get; set; } = string.Empty;
    }

    /// <summary>
    /// 班次创建消息
    /// </summary>
    public class ShiftCreatedMessage
    {
        public Shift Shift { get; }

        public ShiftCreatedMessage(Shift shift)
        {
            Shift = shift;
        }
    }

    /// <summary>
    /// 数据重置消息
    /// </summary>
    public class DataResetMessage
    {
        public DateTime ResetTime { get; }

        public DataResetMessage()
        {
            ResetTime = DateTime.Now;
        }
    }
}
