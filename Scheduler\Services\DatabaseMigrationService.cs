// 引入必要的命名空间 / Import necessary namespaces
using SQLite;           // SQLite数据库 / SQLite database
using Scheduler.Models; // 数据模型 / Data models

namespace Scheduler.Services
{
    /// <summary>
    /// 数据库迁移服务 / Database Migration Service
    /// 负责数据库版本管理和结构迁移
    /// Responsible for database version management and schema migration
    /// </summary>
    public class DatabaseMigrationService
    {
        // 数据库连接实例 / Database connection instance
        private readonly SQLiteAsyncConnection _database;
        // 当前数据库版本 / Current database version
        private const int CurrentVersion = 2;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化数据库迁移服务 / Initialize database migration service
        /// </summary>
        /// <param name="database">数据库连接 / Database connection</param>
        public DatabaseMigrationService(SQLiteAsyncConnection database)
        {
            _database = database;
        }

        /// <summary>
        /// 执行数据库迁移 / Execute database migration
        /// 检查并执行必要的数据库结构升级
        /// Check and execute necessary database schema upgrades
        /// </summary>
        public async Task MigrateAsync()
        {
            try
            {
                // 确保版本表存在 / Ensure version table exists
                await _database.CreateTableAsync<DatabaseVersion>();

                // 获取当前数据库版本 / Get current database version
                var currentVersion = await GetCurrentVersionAsync();

                // 执行迁移 / Execute migration
                if (currentVersion < CurrentVersion)
                {
                    await ExecuteMigrationsAsync(currentVersion);
                    await UpdateVersionAsync(CurrentVersion);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库迁移失败 / Database migration failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取当前数据库版本 / Get current database version
        /// 从版本表中读取当前数据库版本号
        /// Read current database version number from version table
        /// </summary>
        /// <returns>数据库版本号 / Database version number</returns>
        private async Task<int> GetCurrentVersionAsync()
        {
            try
            {
                var version = await _database.Table<DatabaseVersion>()
                    .OrderByDescending(v => v.Version)
                    .FirstOrDefaultAsync();
                return version?.Version ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 更新数据库版本
        /// </summary>
        private async Task UpdateVersionAsync(int version)
        {
            var versionRecord = new DatabaseVersion
            {
                Version = version,
                AppliedAt = DateTime.Now,
                Description = $"Migration to version {version}"
            };
            await _database.InsertAsync(versionRecord);
        }

        /// <summary>
        /// 执行迁移脚本
        /// </summary>
        private async Task ExecuteMigrationsAsync(int fromVersion)
        {
            // 版本1：创建新表
            if (fromVersion < 1)
            {
                await MigrateToVersion1Async();
            }

            // 版本2：添加新字段和索引
            if (fromVersion < 2)
            {
                await MigrateToVersion2Async();
            }
        }

        /// <summary>
        /// 迁移到版本1：创建新表
        /// </summary>
        private async Task MigrateToVersion1Async()
        {
            // 创建新表
            await _database.CreateTableAsync<User>();
            await _database.CreateTableAsync<AppSettings>();
            await _database.CreateTableAsync<NotificationRecord>();
            await _database.CreateTableAsync<AuditLog>();

            System.Diagnostics.Debug.WriteLine("数据库迁移到版本1完成");
        }

        /// <summary>
        /// 迁移到版本2：添加新字段（已移除加班费倍率字段）
        /// </summary>
        private async Task MigrateToVersion2Async()
        {
            try
            {
                // 原本用于添加OvertimeMultiplier字段，现已移除
                System.Diagnostics.Debug.WriteLine("数据库迁移到版本2完成（无需操作）");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库迁移到版本2失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建外键约束（SQLite需要手动处理）
        /// </summary>
        public async Task CreateForeignKeyConstraintsAsync()
        {
            try
            {
                // 启用外键约束
                await _database.ExecuteAsync("PRAGMA foreign_keys = ON");

                System.Diagnostics.Debug.WriteLine("外键约束已启用");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启用外键约束失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public async Task<bool> ValidateDataIntegrityAsync()
        {
            try
            {
                // 检查孤立的WorkRecord
                var orphanedWorkRecords = await _database.QueryAsync<WorkRecord>(
                    "SELECT * FROM WorkRecords WHERE ShiftId NOT IN (SELECT Id FROM Shifts)");

                // 检查孤立的Shift
                var orphanedShifts = await _database.QueryAsync<Shift>(
                    "SELECT * FROM Shifts WHERE EmployerId NOT IN (SELECT Id FROM Employers)");

                // 检查孤立的PaymentRecord
                var orphanedPayments = await _database.QueryAsync<PaymentRecord>(
                    "SELECT * FROM PaymentRecords WHERE EmployerId NOT IN (SELECT Id FROM Employers)");

                if (orphanedWorkRecords.Any() || orphanedShifts.Any() || orphanedPayments.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"发现数据完整性问题: " +
                        $"孤立WorkRecord: {orphanedWorkRecords.Count}, " +
                        $"孤立Shift: {orphanedShifts.Count}, " +
                        $"孤立PaymentRecord: {orphanedPayments.Count}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据完整性验证失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 数据库版本记录
    /// </summary>
    [Table("DatabaseVersions")]
    public class DatabaseVersion
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        public int Version { get; set; }

        public DateTime AppliedAt { get; set; }

        [SQLite.MaxLength(200)]
        public string? Description { get; set; }
    }
}
