// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 日历视图 / Calendar View
/// 显示工作班次的日历界面，支持月视图和周视图
/// Calendar interface for displaying work shifts, supports monthly and weekly views
/// </summary>
public partial class CalendarView : ContentPage
{
	// 日历视图模型实例 / Calendar view model instance
	private readonly CalendarViewModel _viewModel;

	/// <summary>
	/// 构造函数 / Constructor
	/// 初始化日历视图并配置数据绑定 / Initialize calendar view and configure data binding
	/// </summary>
	/// <param name="viewModel">日历视图模型 / Calendar view model</param>
	public CalendarView(CalendarViewModel viewModel)
	{
		// 初始化XAML组件 / Initialize XAML components
		InitializeComponent();
		_viewModel = viewModel;
		BindingContext = _viewModel;
	}

	/// <summary>
	/// 页面出现时的处理 / Handle page appearing
	/// 刷新日历数据和班次信息 / Refresh calendar data and shift information
	/// </summary>
	protected override async void OnAppearing()
	{
		base.OnAppearing();
		// 执行视图模型的出现逻辑 / Execute view model appearing logic
		await _viewModel.OnAppearingAsync();
	}
}