<?xml version="1.0" encoding="utf-8" ?>
<!--
    通知测试视图 / Notification Test View
    用于测试通知功能和权限管理的调试页面
    Debug page for testing notification functionality and permission management
-->
<ContentPage x:Class="Scheduler.Views.NotificationTestView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:DataType="viewmodels:NotificationTestViewModel"
             Title="{Binding LocalizedTexts.NotificationFunctionTest}">

    <!-- 页面内容 / Page content -->
    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!-- 加载指示器 / Loading indicator -->
            <ActivityIndicator IsVisible="{Binding IsLoading}" IsRunning="{Binding IsLoading}" />

            <!-- 状态消息区域 / Status message area -->
            <Frame IsVisible="{Binding StatusMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="#E3F2FD"
                   HasShadow="False"
                   CornerRadius="8"
                   Padding="15">
                <StackLayout Orientation="Horizontal">
                    <Label Text="{Binding StatusMessage}"
                           TextColor="#1976D2"
                           FontSize="14"
                           VerticalOptions="Center"
                           HorizontalOptions="FillAndExpand" />
                    <!-- 清除状态消息按钮 / Clear status message button -->
                    <Button Text="×"
                            Command="{Binding ClearStatusMessageCommand}"
                            BackgroundColor="Transparent"
                            TextColor="#1976D2"
                            FontSize="18"
                            Padding="5"
                            WidthRequest="30"
                            HeightRequest="30" />
                </StackLayout>
            </Frame>

            <!-- 权限检查区域 / Permission check section -->
            <Frame BackgroundColor="#FFF3E0" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.NotificationPermissionManagement}" FontSize="18" FontAttributes="Bold" TextColor="#F57C00" />

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <!-- 检查权限按钮 / Check permission button -->
                        <Button Grid.Column="0"
                                Text="{Binding LocalizedTexts.CheckPermission}"
                                Command="{Binding CheckPermissionCommand}"
                                BackgroundColor="#FF9800"
                                TextColor="White"
                                CornerRadius="8" />

                        <Button Grid.Column="1"
                                Text="{Binding LocalizedTexts.RequestPermission}"
                                Command="{Binding RequestPermissionCommand}"
                                BackgroundColor="#FF5722"
                                TextColor="White"
                                CornerRadius="8" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 自定义通知测试区域 / Custom notification test section -->
            <Frame BackgroundColor="#E8F5E8" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.CustomNotificationTest}" FontSize="18" FontAttributes="Bold" TextColor="#388E3C" />

                    <!-- 通知标题 / Notification title -->
                    <StackLayout>
                        <Label Text="{Binding LocalizedTexts.NotificationTitle}" />
                        <Entry Text="{Binding TestTitle}"
                               Placeholder="{Binding LocalizedTexts.EnterNotificationTitle}"
                               BackgroundColor="White" />
                    </StackLayout>

                    <!-- 通知内容 / Notification message -->
                    <StackLayout>
                        <Label Text="{Binding LocalizedTexts.NotificationContent}" />
                        <Editor Text="{Binding TestMessage}"
                                Placeholder="{Binding LocalizedTexts.EnterNotificationContent}"
                                HeightRequest="80"
                                BackgroundColor="White" />
                    </StackLayout>

                    <!-- 延迟时间 / Delay time -->
                    <StackLayout>
                        <Label>
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span Text="{Binding LocalizedTexts.DelayTimeFormat}" />
                                    <Span Text="{Binding DelaySeconds}" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Slider Value="{Binding DelaySeconds}"
                                Minimum="1"
                                Maximum="60" />
                    </StackLayout>
                    
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0"
                                Text="{Binding LocalizedTexts.InstantNotification}"
                                Command="{Binding TestInstantNotificationCommand}"
                                BackgroundColor="#4CAF50"
                                TextColor="White"
                                CornerRadius="8" />

                        <Button Grid.Column="1"
                                Text="{Binding LocalizedTexts.DelayedNotification}"
                                Command="{Binding TestDelayedNotificationCommand}"
                                BackgroundColor="#2196F3"
                                TextColor="White"
                                CornerRadius="8" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 功能通知测试区域 / Feature notification test section -->
            <Frame BackgroundColor="#F3E5F5" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.FeatureNotificationTest}" FontSize="18" FontAttributes="Bold" TextColor="#7B1FA2" />

                    <!-- 班次提醒测试 / Shift reminder test -->
                    <Button Text="{Binding LocalizedTexts.TestShiftReminder}"
                            Command="{Binding TestShiftReminderCommand}"
                            BackgroundColor="#9C27B0"
                            TextColor="White"
                            CornerRadius="8" />

                    <!-- 待办事项提醒测试 / Todo reminder test -->
                    <Button Text="{Binding LocalizedTexts.TestTodoReminder}"
                            Command="{Binding TestTodoReminderCommand}"
                            BackgroundColor="#673AB7"
                            TextColor="White"
                            CornerRadius="8" />

                    <!-- 支付提醒测试 / Payment reminder test -->
                    <Button Text="{Binding LocalizedTexts.TestPaymentReminder}"
                            Command="{Binding TestPaymentReminderCommand}"
                            BackgroundColor="#3F51B5"
                            TextColor="White"
                            CornerRadius="8" />

                    <!-- 休息提醒测试 / Break reminder test -->
                    <Button Text="{Binding LocalizedTexts.TestBreakReminder}"
                            Command="{Binding TestBreakReminderCommand}"
                            BackgroundColor="#2196F3"
                            TextColor="White"
                            CornerRadius="8" />
                </StackLayout>
            </Frame>

            <!-- 系统管理区域 / System management section -->
            <Frame BackgroundColor="#FFEBEE" HasShadow="True" CornerRadius="12" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.SystemManagement}" FontSize="18" FontAttributes="Bold" TextColor="#D32F2F" />

                    <!-- 重新安排所有通知 / Reschedule all notifications -->
                    <Button Text="{Binding LocalizedTexts.RescheduleAllNotifications}"
                            Command="{Binding RescheduleAllNotificationsCommand}"
                            BackgroundColor="#F44336"
                            TextColor="White"
                            CornerRadius="8" />
                </StackLayout>
            </Frame>

            <!-- 说明文本 / Instructions -->
            <Frame BackgroundColor="#F5F5F5" HasShadow="False" CornerRadius="8" Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="{Binding LocalizedTexts.TestInstructions}" FontSize="16" FontAttributes="Bold" TextColor="#424242" />
                    <Label TextColor="#666666" FontSize="14">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span Text="{Binding LocalizedTexts.InstantNotificationDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.DelayedNotificationDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.ShiftReminderDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.TodoReminderDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.PaymentReminderDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.BreakReminderDesc}" />
                                <Span Text="&#x0a;" />
                                <Span Text="{Binding LocalizedTexts.RescheduleDesc}" />
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </StackLayout>
            </Frame>
            
        </StackLayout>
    </ScrollView>
</ContentPage>
