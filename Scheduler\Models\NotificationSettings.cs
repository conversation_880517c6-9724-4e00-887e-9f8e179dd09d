// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 通知设置模型 / Notification Settings Model
    /// 用于管理用户的通知偏好和配置
    /// Used to manage user notification preferences and configurations
    /// </summary>
    [Table("NotificationSettings")]
    public class NotificationSettings
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID / User ID
        /// 可选，用于多用户支持
        /// Optional, for multi-user support
        /// </summary>
        public int? UserId { get; set; }

        #region 通知总开关 / General Notification Switches

        /// <summary>
        /// 是否启用通知 / Is Notification Enabled
        /// 通知功能的总开关
        /// Master switch for notification functionality
        /// </summary>
        public bool IsNotificationEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用声音 / Is Sound Enabled
        /// 通知声音的开关
        /// Switch for notification sound
        /// </summary>
        public bool IsSoundEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用振动 / Is Vibration Enabled
        /// 通知振动的开关
        /// Switch for notification vibration
        /// </summary>
        public bool IsVibrationEnabled { get; set; } = true;

        #endregion

        #region 班次提醒设置 / Shift Reminder Settings

        /// <summary>
        /// 是否启用班次提醒 / Is Shift Reminder Enabled
        /// 班次开始前的提醒功能开关
        /// Switch for reminder before shift starts
        /// </summary>
        public bool IsShiftReminderEnabled { get; set; } = true;

        /// <summary>
        /// 班次提醒提前时间 / Shift Reminder Minutes
        /// 班次提醒提前时间（分钟）
        /// Advance time for shift reminder (in minutes)
        /// </summary>
        public int ShiftReminderMinutes { get; set; } = 15;

        /// <summary>
        /// 是否启用每日工作安排通知
        /// </summary>
        public bool IsDailyScheduleEnabled { get; set; } = true;

        /// <summary>
        /// 每日工作安排通知时间（小时，24小时制）
        /// </summary>
        public int DailyScheduleHour { get; set; } = 8;

        /// <summary>
        /// 每日工作安排通知分钟
        /// </summary>
        public int DailyScheduleMinute { get; set; } = 0;

        #endregion

        #region 休息提醒设置

        /// <summary>
        /// 是否启用休息提醒
        /// </summary>
        public bool IsBreakReminderEnabled { get; set; } = true;

        /// <summary>
        /// 休息提醒间隔（小时）
        /// </summary>
        public int BreakReminderIntervalHours { get; set; } = 2;

        /// <summary>
        /// 是否仅在工作时间提醒休息
        /// </summary>
        public bool BreakReminderOnlyDuringWork { get; set; } = true;

        #endregion

        #region 薪资提醒设置

        /// <summary>
        /// 是否启用薪资提醒
        /// </summary>
        public bool IsPaymentReminderEnabled { get; set; } = true;

        /// <summary>
        /// 薪资到期提醒提前天数
        /// </summary>
        public int PaymentReminderDaysBefore { get; set; } = 1;

        /// <summary>
        /// 是否启用薪资确认提醒
        /// </summary>
        public bool IsPaymentConfirmationEnabled { get; set; } = true;

        /// <summary>
        /// 薪资确认提醒延迟天数（工作结束后几天提醒确认）
        /// </summary>
        public int PaymentConfirmationDelayDays { get; set; } = 1;

        #endregion

        #region 其他设置

        /// <summary>
        /// 是否启用班次冲突通知
        /// </summary>
        public bool IsShiftConflictEnabled { get; set; } = true;

        /// <summary>
        /// 通知显示时长（秒）
        /// </summary>
        public int NotificationDisplayDuration { get; set; } = 5;

        /// <summary>
        /// 是否在免打扰时间内静音
        /// </summary>
        public bool IsQuietHoursEnabled { get; set; } = false;

        /// <summary>
        /// 免打扰开始时间（小时）
        /// </summary>
        public int QuietHoursStartHour { get; set; } = 22;

        /// <summary>
        /// 免打扰开始时间（分钟）
        /// </summary>
        public int QuietHoursStartMinute { get; set; } = 0;

        /// <summary>
        /// 免打扰结束时间（小时）
        /// </summary>
        public int QuietHoursEndHour { get; set; } = 7;

        /// <summary>
        /// 免打扰结束时间（分钟）
        /// </summary>
        public int QuietHoursEndMinute { get; set; } = 0;

        #endregion

        #region 时间戳

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        #endregion

        #region 辅助属性

        /// <summary>
        /// 每日工作安排通知时间
        /// </summary>
        [Ignore]
        public TimeSpan DailyScheduleTime => new TimeSpan(DailyScheduleHour, DailyScheduleMinute, 0);

        /// <summary>
        /// 免打扰开始时间
        /// </summary>
        [Ignore]
        public TimeSpan QuietHoursStartTime => new TimeSpan(QuietHoursStartHour, QuietHoursStartMinute, 0);

        /// <summary>
        /// 免打扰结束时间
        /// </summary>
        [Ignore]
        public TimeSpan QuietHoursEndTime => new TimeSpan(QuietHoursEndHour, QuietHoursEndMinute, 0);

        /// <summary>
        /// 检查当前时间是否在免打扰时间内
        /// </summary>
        [Ignore]
        public bool IsInQuietHours
        {
            get
            {
                if (!IsQuietHoursEnabled) return false;

                var now = DateTime.Now.TimeOfDay;
                var start = QuietHoursStartTime;
                var end = QuietHoursEndTime;

                // 处理跨天的情况（如22:00-07:00）
                if (start > end)
                {
                    return now >= start || now <= end;
                }
                else
                {
                    return now >= start && now <= end;
                }
            }
        }

        #endregion

        /// <summary>
        /// 获取默认设置
        /// </summary>
        public static NotificationSettings GetDefault()
        {
            return new NotificationSettings
            {
                IsNotificationEnabled = true,
                IsSoundEnabled = true,
                IsVibrationEnabled = true,
                IsShiftReminderEnabled = true,
                ShiftReminderMinutes = 15,
                IsDailyScheduleEnabled = true,
                DailyScheduleHour = 8,
                DailyScheduleMinute = 0,
                IsBreakReminderEnabled = true,
                BreakReminderIntervalHours = 2,
                BreakReminderOnlyDuringWork = true,
                IsPaymentReminderEnabled = true,
                PaymentReminderDaysBefore = 1,
                IsPaymentConfirmationEnabled = true,
                PaymentConfirmationDelayDays = 1,
                IsShiftConflictEnabled = true,
                NotificationDisplayDuration = 5,
                IsQuietHoursEnabled = false,
                QuietHoursStartHour = 22,
                QuietHoursStartMinute = 0,
                QuietHoursEndHour = 7,
                QuietHoursEndMinute = 0
            };
        }
    }
}
