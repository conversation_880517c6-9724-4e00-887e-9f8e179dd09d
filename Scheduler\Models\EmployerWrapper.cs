// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel; // MVVM组件模型 / MVVM component model

namespace Scheduler.Models
{
    /// <summary>
    /// 雇主包装类 / Employer Wrapper Class
    /// 用于UI中的多选功能和数据绑定
    /// Used for multi-selection functionality and data binding in UI
    /// </summary>
    public partial class EmployerWrapper : ObservableObject
    {
        /// <summary>
        /// 雇主实体 / Employer Entity
        /// 被包装的雇主对象
        /// The wrapped employer object
        /// </summary>
        public Employer Employer { get; }

        /// <summary>
        /// 是否被选中 / Is Selected
        /// 标识该雇主是否在UI中被选中
        /// Indicates whether this employer is selected in the UI
        /// </summary>
        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化雇主包装对象
        /// Initialize employer wrapper object
        /// </summary>
        /// <param name="employer">雇主实体 / Employer entity</param>
        public EmployerWrapper(Employer employer)
        {
            Employer = employer;
        }

        // 代理属性，方便绑定 / Proxy properties for convenient binding
        /// <summary>雇主ID / Employer ID</summary>
        public int Id => Employer.Id;
        /// <summary>雇主名称 / Employer Name</summary>
        public string Name => Employer.Name;
        /// <summary>联系信息 / Contact Information</summary>
        public string? ContactInfo => Employer.ContactInfo;
        /// <summary>时薪 / Hourly Rate</summary>
        public decimal HourlyRate => Employer.HourlyRate;
        /// <summary>支付周期天数 / Payment Cycle Days</summary>
        public int PaymentCycleDays => Employer.PaymentCycleDays;
        /// <summary>颜色 / Color</summary>
        public string Color => Employer.Color;
        /// <summary>工作地点 / Work Location</summary>
        public string? WorkLocation => Employer.WorkLocation;
        /// <summary>备注 / Notes</summary>
        public string? Notes => Employer.Notes;
        /// <summary>是否活跃 / Is Active</summary>
        public bool IsActive => Employer.IsActive;
        /// <summary>创建时间 / Created At</summary>
        public DateTime CreatedAt => Employer.CreatedAt;
        /// <summary>更新时间 / Updated At</summary>
        public DateTime UpdatedAt => Employer.UpdatedAt;
    }
}
