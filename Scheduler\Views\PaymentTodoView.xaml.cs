// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 付款待办视图 / Payment Todo View
/// 显示和管理待确认的付款记录和工资支付事项
/// Display and manage pending payment records and salary payment items
/// </summary>
public partial class PaymentTodoView : ContentPage
{
    // 付款待办视图模型实例 / Payment todo view model instance
    private readonly PaymentTodoViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化付款待办视图并配置数据绑定 / Initialize payment todo view and configure data binding
    /// </summary>
    /// <param name="viewModel">付款待办视图模型 / Payment todo view model</param>
    public PaymentTodoView(PaymentTodoViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载待办付款数据和统计信息 / Load pending payment data and statistics
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // 页面出现时加载数据 / Load data when page appears
        await _viewModel.LoadDataCommand.ExecuteAsync(null);
    }
}
