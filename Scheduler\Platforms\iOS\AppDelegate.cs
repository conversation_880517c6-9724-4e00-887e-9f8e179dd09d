﻿using Foundation;
using UIKit;

namespace Scheduler
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate
    {
        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        public override bool FinishedLaunching(UIApplication application, NSDictionary launchOptions)
        {
            // 设置状态栏透明化
            SetStatusBarTransparent();

            return base.FinishedLaunching(application, launchOptions);
        }

        /// <summary>
        /// 设置状态栏透明化
        /// </summary>
        private void SetStatusBarTransparent()
        {
            // iOS 13+ 支持
            if (UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
            {
                // 设置状态栏样式为深色内容（适合浅色背景）
                UIApplication.SharedApplication.SetStatusBarStyle(UIStatusBarStyle.DarkContent, false);
            }
            else
            {
                // iOS 13以下版本
                UIApplication.SharedApplication.SetStatusBarStyle(UIStatusBarStyle.Default, false);
            }
        }
    }
}
