#if ANDROID
using Android.Content;
using Android.OS;
using AndroidX.Core.App;
using System.Diagnostics;
using System.Runtime;
using AndroidApp = Android.App;
using SystemDiagnostics = System.Diagnostics;
using SystemEnvironment = System;
using AndroidProcess = Android.OS.Process;

namespace Scheduler.Platforms.Android
{
    /// <summary>
    /// Android平台性能优化器 - 专门用于防止ANR和优化性能
    /// Android Platform Performance Optimizer - specifically for preventing ANR and optimizing performance
    /// </summary>
    public static class PerformanceOptimizer
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 初始化Android性能优化
        /// Initialize Android performance optimizations
        /// </summary>
        public static void Initialize(AndroidX.AppCompat.App.AppCompatActivity activity)
        {
            lock (_lockObject)
            {
                if (_isInitialized)
                    return;

                try
                {
                    SystemDiagnostics.Debug.WriteLine("初始化Android性能优化...");

                    // 1. 优化垃圾回收
                    OptimizeGarbageCollection();

                    // 2. 优化线程池
                    OptimizeThreadPool();

                    // 3. 设置应用优先级
                    SetApplicationPriority(activity);

                    // 4. 优化内存管理
                    OptimizeMemoryManagement();

                    _isInitialized = true;
                    SystemDiagnostics.Debug.WriteLine("Android性能优化初始化完成");
                }
                catch (Exception ex)
                {
                    SystemDiagnostics.Debug.WriteLine($"Android性能优化初始化失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 优化垃圾回收设置
        /// Optimize garbage collection settings
        /// </summary>
        private static void OptimizeGarbageCollection()
        {
            try
            {
                // 设置GC为并发模式，减少暂停时间
                GCSettings.LatencyMode = GCLatencyMode.SustainedLowLatency;
                SystemDiagnostics.Debug.WriteLine("GC优化: 设置为低延迟模式");
            }
            catch (Exception ex)
            {
                SystemDiagnostics.Debug.WriteLine($"GC优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 优化线程池配置
        /// Optimize thread pool configuration
        /// </summary>
        private static void OptimizeThreadPool()
        {
            try
            {
                // 获取CPU核心数
                var coreCount = SystemEnvironment.Environment.ProcessorCount;
                
                // 设置线程池最小线程数
                var minWorkerThreads = Math.Max(coreCount, 4);
                var minCompletionPortThreads = Math.Max(coreCount, 4);
                
                ThreadPool.SetMinThreads(minWorkerThreads, minCompletionPortThreads);
                
                // 设置线程池最大线程数
                var maxWorkerThreads = coreCount * 4;
                var maxCompletionPortThreads = coreCount * 4;
                
                ThreadPool.SetMaxThreads(maxWorkerThreads, maxCompletionPortThreads);
                
                SystemDiagnostics.Debug.WriteLine($"线程池优化: 最小线程 {minWorkerThreads}/{minCompletionPortThreads}, 最大线程 {maxWorkerThreads}/{maxCompletionPortThreads}");
            }
            catch (Exception ex)
            {
                SystemDiagnostics.Debug.WriteLine($"线程池优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置应用程序优先级
        /// Set application priority
        /// </summary>
        private static void SetApplicationPriority(AndroidX.AppCompat.App.AppCompatActivity activity)
        {
            try
            {
                // Android平台的线程优先级设置在这里可以跳过
                // 因为.NET MAUI已经有合适的默认设置
                SystemDiagnostics.Debug.WriteLine("应用优先级设置完成");
            }
            catch (Exception ex)
            {
                SystemDiagnostics.Debug.WriteLine($"设置应用优先级失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 优化内存管理
        /// Optimize memory management
        /// </summary>
        private static void OptimizeMemoryManagement()
        {
            try
            {
                // 设置大对象堆压缩模式
                GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;

                SystemDiagnostics.Debug.WriteLine("内存管理优化完成");
            }
            catch (Exception ex)
            {
                SystemDiagnostics.Debug.WriteLine($"内存管理优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 监控内存使用情况
        /// Monitor memory usage
        /// </summary>
        public static void MonitorMemoryUsage()
        {
            try
            {
                var memoryInfo = new AndroidApp.ActivityManager.MemoryInfo();
                var activityManager = Platform.CurrentActivity?.GetSystemService(Context.ActivityService) as AndroidApp.ActivityManager;
                activityManager?.GetMemoryInfo(memoryInfo);

                var totalMemory = GC.GetTotalMemory(false);
                var availableMemory = memoryInfo?.AvailMem ?? 0;

                SystemDiagnostics.Debug.WriteLine($"内存监控 - 已用: {totalMemory / 1024 / 1024}MB, 可用: {availableMemory / 1024 / 1024}MB");

                // 如果内存使用过高，触发垃圾回收
                if (totalMemory > 100 * 1024 * 1024) // 超过100MB
                {
                    SystemDiagnostics.Debug.WriteLine("内存使用过高，触发垃圾回收");
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
            }
            catch (Exception ex)
            {
                SystemDiagnostics.Debug.WriteLine($"内存监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行带超时的异步操作
        /// Execute async operation with timeout
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteWithTimeoutAsync<T>(
            Func<Task<T>> operation,
            int timeoutSeconds = 30,
            string operationName = "Unknown")
        {
            var stopwatch = SystemDiagnostics.Stopwatch.StartNew();

            try
            {
                SystemDiagnostics.Debug.WriteLine($"开始执行操作: {operationName}");

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));
                var result = await operation().WaitAsync(cts.Token).ConfigureAwait(false);

                stopwatch.Stop();
                SystemDiagnostics.Debug.WriteLine($"操作完成: {operationName}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                return result;
            }
            catch (System.OperationCanceledException)
            {
                stopwatch.Stop();
                SystemDiagnostics.Debug.WriteLine($"操作超时: {operationName}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
                throw new TimeoutException($"操作 '{operationName}' 超时 ({timeoutSeconds}秒)");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                SystemDiagnostics.Debug.WriteLine($"操作异常: {operationName}, 耗时: {stopwatch.ElapsedMilliseconds}ms, 异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行带超时的异步操作（无返回值）
        /// Execute async operation with timeout (void)
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        public static async Task ExecuteWithTimeoutAsync(
            Func<Task> operation, 
            int timeoutSeconds = 30, 
            string operationName = "Unknown")
        {
            await ExecuteWithTimeoutAsync(async () =>
            {
                await operation().ConfigureAwait(false);
                return true;
            }, timeoutSeconds, operationName).ConfigureAwait(false);
        }

        /// <summary>
        /// 检查是否在主线程上
        /// Check if running on main thread
        /// </summary>
        /// <returns>是否在主线程</returns>
        public static bool IsOnMainThread()
        {
            return Looper.MainLooper?.Thread == Java.Lang.Thread.CurrentThread();
        }

        /// <summary>
        /// 确保在后台线程执行
        /// Ensure execution on background thread
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static async Task RunOnBackgroundThreadAsync(Func<Task> action)
        {
            if (IsOnMainThread())
            {
                await Task.Run(action).ConfigureAwait(false);
            }
            else
            {
                await action().ConfigureAwait(false);
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// Get performance statistics
        /// </summary>
        /// <returns>性能统计信息</returns>
        public static string GetPerformanceStats()
        {
            try
            {
                var totalMemory = GC.GetTotalMemory(false);
                var gen0Collections = GC.CollectionCount(0);
                var gen1Collections = GC.CollectionCount(1);
                var gen2Collections = GC.CollectionCount(2);
                
                ThreadPool.GetAvailableThreads(out int workerThreads, out int completionPortThreads);
                ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);
                
                return $"性能统计:\n" +
                       $"- 内存使用: {totalMemory / 1024 / 1024}MB\n" +
                       $"- GC次数: Gen0={gen0Collections}, Gen1={gen1Collections}, Gen2={gen2Collections}\n" +
                       $"- 线程池: 可用={workerThreads}/{completionPortThreads}, 最大={maxWorkerThreads}/{maxCompletionPortThreads}\n" +
                       $"- 主线程: {(IsOnMainThread() ? "是" : "否")}";
            }
            catch (Exception ex)
            {
                return $"获取性能统计失败: {ex.Message}";
            }
        }
    }
}
#endif
