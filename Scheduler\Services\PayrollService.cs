// 引入数据模型命名空间 / Import data models namespace
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 工资计算服务 / Payroll Service
    /// 负责工资计算、支付记录生成和薪资统计
    /// Responsible for salary calculation, payment record generation and payroll statistics
    /// </summary>
    public class PayrollService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化工资计算服务 / Initialize payroll service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public PayrollService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 计算指定雇主和时间段的工资 / Calculate payment for specified employer and period
        /// 根据工作记录计算薪资并生成支付记录
        /// Calculate salary based on work records and generate payment record
        /// </summary>
        /// <param name="employerId">雇主ID / Employer ID</param>
        /// <param name="periodStart">计算周期开始日期 / Period start date</param>
        /// <param name="periodEnd">计算周期结束日期 / Period end date</param>
        /// <returns>支付记录 / Payment record</returns>
        public async Task<PaymentRecord> CalculatePaymentAsync(int employerId, DateTime periodStart, DateTime periodEnd)
        {
            var employer = await _databaseService.GetEmployerAsync(employerId);
            if (employer == null)
                throw new ArgumentException($"雇主ID {employerId} 不存在 / Employer ID {employerId} does not exist");

            // 获取该时间段内的所有工作记录 / Get all work records for the period
            var workRecords = await GetWorkRecordsForPeriodAsync(employerId, periodStart, periodEnd);

            var payment = new PaymentRecord
            {
                EmployerId = employerId,
                PeriodStart = periodStart,
                PeriodEnd = periodEnd,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 计算工作时长 / Calculate work hours
            var (regularHours, overtimeHours) = CalculateHours(workRecords);
            payment.RegularHours = regularHours;
            payment.OvertimeHours = overtimeHours;
            payment.TotalHours = regularHours + overtimeHours;

            // 计算工资（简化版本，不区分常规和加班） / Calculate salary (simplified version, no distinction between regular and overtime)
            payment.BaseAmount = (decimal)(regularHours + overtimeHours) * employer.HourlyRate;
            payment.OvertimeAmount = 0; // 不再单独计算加班费 / No separate overtime calculation
            payment.TotalAmount = payment.BaseAmount + payment.BonusAmount - payment.DeductionAmount;

            // 设置预期支付日期 / Set expected payment date
            payment.ExpectedPaymentDate = periodEnd.AddDays(employer.PaymentCycleDays);

            return payment;
        }

        /// <summary>
        /// 计算加班费（已简化，不再使用倍率）
        /// </summary>
        public async Task<decimal> CalculateOvertimePayAsync(WorkRecord record, Employer employer)
        {
            if (record.OvertimeMinutes <= 0)
                return 0;

            var overtimeHours = record.OvertimeMinutes / 60.0;
            return (decimal)overtimeHours * employer.HourlyRate; // 不再使用倍率
        }

        /// <summary>
        /// 生成月度工资单
        /// </summary>
        public async Task<List<PaymentRecord>> GenerateMonthlyPayrollAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var employers = await _databaseService.GetEmployersAsync();
            var paymentRecords = new List<PaymentRecord>();

            foreach (var employer in employers)
            {
                try
                {
                    var payment = await CalculatePaymentAsync(employer.Id, startDate, endDate);
                    
                    // 只有工作时长大于0的才生成支付记录
                    if (payment.TotalHours > 0)
                    {
                        await _databaseService.SavePaymentRecordAsync(payment);
                        paymentRecords.Add(payment);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"为雇主 {employer.Name} 生成工资单失败: {ex.Message}");
                }
            }

            return paymentRecords;
        }

        /// <summary>
        /// 验证工资计算结果
        /// </summary>
        public async Task<PayrollValidationResult> ValidatePaymentAsync(PaymentRecord payment)
        {
            var result = new PayrollValidationResult { IsValid = true };

            // 验证基本数据
            if (payment.TotalHours < 0)
            {
                result.IsValid = false;
                result.Errors.Add("总工作时长不能为负数");
            }

            if (payment.TotalAmount < 0)
            {
                result.IsValid = false;
                result.Errors.Add("总金额不能为负数");
            }

            // 验证时间段合理性
            if (payment.PeriodStart >= payment.PeriodEnd)
            {
                result.IsValid = false;
                result.Errors.Add("开始日期必须早于结束日期");
            }

            // 验证雇主存在性
            var employer = await _databaseService.GetEmployerAsync(payment.EmployerId);
            if (employer == null)
            {
                result.IsValid = false;
                result.Errors.Add($"雇主ID {payment.EmployerId} 不存在");
            }

            // 验证工作时长合理性（每天不超过24小时）
            var dayCount = (payment.PeriodEnd - payment.PeriodStart).Days + 1;
            var maxPossibleHours = dayCount * 24;
            if (payment.TotalHours > maxPossibleHours)
            {
                result.IsValid = false;
                result.Errors.Add($"工作时长 {payment.TotalHours:F1} 小时超过了最大可能时长 {maxPossibleHours} 小时");
            }

            return result;
        }

        /// <summary>
        /// 获取指定时间段的工作记录
        /// </summary>
        private async Task<List<WorkRecord>> GetWorkRecordsForPeriodAsync(int employerId, DateTime start, DateTime end)
        {
            var shifts = await _databaseService.GetShiftsByEmployerAsync(employerId);
            var periodShifts = shifts.Where(s => s.StartTime >= start && s.StartTime <= end).ToList();

            var workRecords = new List<WorkRecord>();
            foreach (var shift in periodShifts)
            {
                var workRecord = await _databaseService.GetWorkRecordByShiftAsync(shift.Id);
                if (workRecord != null && workRecord.IsCompleted)
                {
                    workRecords.Add(workRecord);
                }
            }

            return workRecords;
        }

        /// <summary>
        /// 计算常规工时和加班工时
        /// </summary>
        private (double regularHours, double overtimeHours) CalculateHours(List<WorkRecord> workRecords)
        {
            double totalRegularHours = 0;
            double totalOvertimeHours = 0;

            foreach (var record in workRecords)
            {
                var workHours = record.FinalHours;
                var overtimeHours = record.OvertimeMinutes / 60.0;

                totalRegularHours += Math.Max(0, workHours - overtimeHours);
                totalOvertimeHours += overtimeHours;
            }

            return (totalRegularHours, totalOvertimeHours);
        }

        /// <summary>
        /// 自动从shift数据生成PaymentRecord
        /// </summary>
        public async Task GeneratePaymentRecordsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayrollService: 开始自动生成支付记录");

                // 获取所有雇主
                var employers = await _databaseService.GetEmployersAsync();
                System.Diagnostics.Debug.WriteLine($"PayrollService: 找到 {employers.Count} 个雇主");

                // 获取所有现有的支付记录
                var existingPayments = await _databaseService.GetPaymentRecordsAsync();
                var existingPeriods = existingPayments
                    .Select(p => new { p.EmployerId, p.PeriodStart, p.PeriodEnd })
                    .ToHashSet();

                var generatedCount = 0;

                foreach (var employer in employers)
                {
                    // 获取该雇主的所有shift
                    var shifts = await _databaseService.GetShiftsByEmployerAsync(employer.Id);
                    System.Diagnostics.Debug.WriteLine($"PayrollService: 雇主 {employer.Name} 有 {shifts.Count} 个shift");

                    if (shifts.Count == 0) continue;

                    // 按周分组shift（根据雇主的支付周期）
                    var shiftGroups = GroupShiftsByPaymentPeriod(shifts, employer.PaymentCycleDays);

                    foreach (var group in shiftGroups)
                    {
                        var periodStart = group.Key.Start;
                        var periodEnd = group.Key.End;
                        var periodShifts = group.Value;

                        // 检查是否已经存在该时间段的支付记录
                        var periodKey = new { EmployerId = employer.Id, PeriodStart = periodStart, PeriodEnd = periodEnd };
                        if (existingPeriods.Contains(periodKey))
                        {
                            System.Diagnostics.Debug.WriteLine($"PayrollService: 跳过已存在的支付记录 - {employer.Name} ({periodStart:MM/dd} - {periodEnd:MM/dd})");
                            continue;
                        }

                        // 计算该时间段的工作时长
                        var totalHours = periodShifts.Sum(s => s.DurationHours);
                        if (totalHours <= 0) continue;

                        // 创建支付记录
                        var paymentRecord = new PaymentRecord
                        {
                            EmployerId = employer.Id,
                            PeriodStart = periodStart,
                            PeriodEnd = periodEnd,
                            TotalHours = totalHours,
                            RegularHours = Math.Min(totalHours, 38), // 38小时内为常规工时
                            OvertimeHours = Math.Max(0, totalHours - 38), // 超过38小时为加班
                            BaseAmount = (decimal)Math.Min(totalHours, 38) * employer.HourlyRate,
                            OvertimeAmount = (decimal)Math.Max(0, totalHours - 38) * employer.HourlyRate * 1.5m, // 加班费1.5倍
                            TotalAmount = (decimal)Math.Min(totalHours, 38) * employer.HourlyRate + (decimal)Math.Max(0, totalHours - 38) * employer.HourlyRate * 1.5m,
                            Status = PaymentStatus.Pending,
                            ExpectedPaymentDate = periodEnd.AddDays(employer.PaymentCycleDays),
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };

                        // 保存支付记录
                        await _databaseService.SavePaymentRecordAsync(paymentRecord);
                        generatedCount++;

                        System.Diagnostics.Debug.WriteLine($"PayrollService: 生成支付记录 - {employer.Name}, ${paymentRecord.TotalAmount:F2}, {totalHours:F1}小时 ({periodStart:MM/dd} - {periodEnd:MM/dd})");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"PayrollService: 自动生成支付记录完成，共生成 {generatedCount} 条记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayrollService: 自动生成支付记录失败 - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 按支付周期分组shift
        /// </summary>
        private Dictionary<(DateTime Start, DateTime End), List<Shift>> GroupShiftsByPaymentPeriod(List<Shift> shifts, int paymentCycleDays)
        {
            var groups = new Dictionary<(DateTime Start, DateTime End), List<Shift>>();

            foreach (var shift in shifts.OrderBy(s => s.StartTime))
            {
                var shiftDate = shift.StartTime.Date;

                // 找到包含该shift的支付周期
                var periodStart = GetPeriodStart(shiftDate, paymentCycleDays);
                var periodEnd = periodStart.AddDays(paymentCycleDays - 1);

                var periodKey = (periodStart, periodEnd);

                if (!groups.ContainsKey(periodKey))
                {
                    groups[periodKey] = new List<Shift>();
                }

                groups[periodKey].Add(shift);
            }

            return groups;
        }

        /// <summary>
        /// 获取支付周期的开始日期
        /// </summary>
        private DateTime GetPeriodStart(DateTime date, int paymentCycleDays)
        {
            // 简化实现：以每周一为周期开始
            if (paymentCycleDays == 7)
            {
                var daysFromMonday = ((int)date.DayOfWeek + 6) % 7; // 计算距离周一的天数
                return date.AddDays(-daysFromMonday);
            }

            // 对于其他周期，以月初为基准
            return new DateTime(date.Year, date.Month, 1);
        }
    }


}
