// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 应用设置模型 / Application Settings Model
    /// 用于存储应用程序的配置设置和用户偏好
    /// Used to store application configuration settings and user preferences
    /// </summary>
    [Table("AppSettings")]
    public class AppSettings
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 设置键名 / Setting Key Name
        /// 用于标识特定设置项的唯一键
        /// Unique key used to identify specific setting item
        /// </summary>
        [DataAnnotations.Required, SQLite.MaxLength(100)]
        [Unique]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 设置值 / Setting Value
        /// 存储设置的实际值（以字符串形式）
        /// Stores the actual value of the setting (as string)
        /// </summary>
        [SQLite.MaxLength(1000)]
        public string? Value { get; set; }

        /// <summary>
        /// 设置类型 / Setting Type
        /// 指定值的数据类型（String, Boolean, Integer, Decimal, DateTime）
        /// Specifies the data type of the value (String, Boolean, Integer, Decimal, DateTime)
        /// </summary>
        [SQLite.MaxLength(20)]
        public string ValueType { get; set; } = "String";

        /// <summary>
        /// 设置描述 / Setting Description
        /// 对设置项的详细说明
        /// Detailed description of the setting item
        /// </summary>
        [SQLite.MaxLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否为用户设置 / Is User Setting
        /// true表示用户设置，false表示系统设置
        /// true for user setting, false for system setting
        /// </summary>
        public bool IsUserSetting { get; set; } = true;

        /// <summary>
        /// 用户ID / User ID
        /// 如果是用户设置，关联的用户标识
        /// Associated user identifier if it's a user setting
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
