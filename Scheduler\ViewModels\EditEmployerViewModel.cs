// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 编辑雇主视图模型 / Edit Employer ViewModel
    /// 处理雇主信息的编辑和更新功能
    /// Handles employer information editing and updating functionality
    /// </summary>
    [QueryProperty(nameof(EmployerId), "employerId")]  // 查询属性绑定 / Query property binding
    public partial class EditEmployerViewModel : BaseViewModel
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 本地化文本属性 / Localized texts property
        /// 为XAML绑定提供本地化文本 / Provides localized texts for XAML binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);

        /// <summary>
        /// 雇主ID
        /// </summary>
        [ObservableProperty]
        private int employerId;

        /// <summary>
        /// 雇主名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 联系信息
        /// </summary>
        [ObservableProperty]
        private string contactInfo = string.Empty;

        /// <summary>
        /// 时薪
        /// </summary>
        [ObservableProperty]
        private decimal hourlyRate;

        /// <summary>
        /// 付款周期天数
        /// </summary>
        [ObservableProperty]
        private int paymentCycleDays = 30;

        /// <summary>
        /// 标识颜色
        /// </summary>
        [ObservableProperty]
        private string color = "#007ACC";

        /// <summary>
        /// 工作地点
        /// </summary>
        [ObservableProperty]
        private string workLocation = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [ObservableProperty]
        private string notes = string.Empty;

        /// <summary>
        /// 是否激活
        /// </summary>
        [ObservableProperty]
        private bool isActive = true;

        // 颜色选择状态
        [ObservableProperty]
        private bool isBlueSelected = true;

        [ObservableProperty]
        private bool isGreenSelected;

        [ObservableProperty]
        private bool isRedSelected;

        [ObservableProperty]
        private bool isOrangeSelected;

        [ObservableProperty]
        private bool isPurpleSelected;

        /// <summary>
        /// 颜色选项集合
        /// </summary>
        public ObservableCollection<ColorOption> ColorOptions { get; } = new();

        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle => "编辑雇主";

        /// <summary>
        /// 雇主姓名输入框背景色
        /// </summary>
        [ObservableProperty]
        private Color nameEntryBackgroundColor = Colors.LightGray;

        /// <summary>
        /// 时薪输入框背景色
        /// </summary>
        [ObservableProperty]
        private Color hourlyRateEntryBackgroundColor = Colors.LightGray;

        /// <summary>
        /// 支付周期输入框背景色
        /// </summary>
        [ObservableProperty]
        private Color paymentCycleDaysEntryBackgroundColor = Colors.LightGray;

        /// <summary>
        /// 构造函数
        /// </summary>
        public EditEmployerViewModel(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            _localizationService = LocalizationService.Instance;

            // 初始化颜色选项
            InitializeColorOptions();
        }

        /// <summary>
        /// 页面出现时初始化
        /// </summary>
        [RelayCommand]
        public async Task AppearingAsync()
        {
            if (EmployerId > 0)
            {
                await LoadEmployerAsync();
            }
        }

        /// <summary>
        /// 雇主ID变化时触发
        /// </summary>
        partial void OnEmployerIdChanged(int value)
        {
            if (value > 0)
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await LoadEmployerAsync();
                });
            }
        }

        /// <summary>
        /// 加载雇主信息
        /// </summary>
        private async Task LoadEmployerAsync()
        {
            if (EmployerId <= 0) return;

            try
            {
                IsBusy = true;

                var employer = await _databaseService.GetEmployerAsync(EmployerId);
                if (employer != null)
                {
                    Name = employer.Name;
                    ContactInfo = employer.ContactInfo ?? string.Empty;
                    HourlyRate = employer.HourlyRate;
                    PaymentCycleDays = employer.PaymentCycleDays;
                    Color = employer.Color;
                    WorkLocation = employer.WorkLocation ?? string.Empty;
                    Notes = employer.Notes ?? string.Empty;
                    IsActive = employer.IsActive;

                    // 更新颜色选择状态
                    UpdateSelectedColor(employer.Color);
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync("加载失败", ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 选择颜色
        /// </summary>
        [RelayCommand]
        private void SelectColor(ColorOption selectedColorOption)
        {
            // 更新所有颜色选项的选中状态
            foreach (var option in ColorOptions)
            {
                option.IsSelected = option == selectedColorOption;
            }

            // 更新当前选中的颜色
            Color = selectedColorOption.HexValue;

            // 通知UI更新
            OnPropertyChanged(nameof(ColorOptions));
        }

        /// <summary>
        /// 更新颜色选择状态
        /// </summary>
        private void UpdateSelectedColor(string selectedColor)
        {
            foreach (var option in ColorOptions)
            {
                option.IsSelected = option.HexValue == selectedColor;
            }

            // 保持旧的布尔属性兼容性
            IsBlueSelected = selectedColor == "#007ACC";
            IsGreenSelected = selectedColor == "#28A745";
            IsRedSelected = selectedColor == "#DC3545";
            IsOrangeSelected = selectedColor == "#FD7E14";
            IsPurpleSelected = selectedColor == "#6F42C1";
        }

        /// <summary>
        /// 保存雇主信息
        /// </summary>
        [RelayCommand]
        private async Task SaveAsync()
        {
            if (IsBusy) return;

            try
            {
                // 验证输入
                if (!ValidateInput())
                    return;

                IsBusy = true;

                var employer = new Employer
                {
                    Id = EmployerId,
                    Name = Name.Trim(),
                    ContactInfo = string.IsNullOrWhiteSpace(ContactInfo) ? null : ContactInfo.Trim(),
                    HourlyRate = HourlyRate,
                    PaymentCycleDays = PaymentCycleDays,
                    Color = Color,
                    WorkLocation = string.IsNullOrWhiteSpace(WorkLocation) ? null : WorkLocation.Trim(),
                    Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim(),
                    IsActive = IsActive
                };

                await _databaseService.SaveEmployerAsync(employer);

                // 发送雇主更新消息
                WeakReferenceMessenger.Default.Send(new EmployerUpdatedMessage());

                var saveSuccessfulText = _localizationService.GetLocalizedString("SaveSuccessful");
                var employerInfoUpdatedText = _localizationService.GetLocalizedString("EmployerInfoUpdatedSuccessfully");
                await ShowSuccessAsync(employerInfoUpdatedText, saveSuccessfulText);

                // 返回上一页
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                var saveFailedText = _localizationService.GetLocalizedString("SaveFailed");
                await ShowErrorAsync(ex.Message, saveFailedText);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 取消编辑
        /// </summary>
        [RelayCommand]
        private async Task CancelAsync()
        {
            await Shell.Current.GoToAsync("..");
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            // 重置所有输入框背景色
            ResetFieldBackgroundColors();

            var validationErrors = new List<string>();
            var firstErrorField = "";

            // 验证雇主姓名（必填）
            if (string.IsNullOrWhiteSpace(Name))
            {
                validationErrors.Add("• 雇主姓名不能为空");
                NameEntryBackgroundColor = Colors.LightPink; // 浅红色背景
                if (string.IsNullOrEmpty(firstErrorField))
                    firstErrorField = "Name";
            }
            else if (Name.Trim().Length > 100)
            {
                validationErrors.Add("• 雇主姓名不能超过100个字符");
                NameEntryBackgroundColor = Colors.LightPink;
                if (string.IsNullOrEmpty(firstErrorField))
                    firstErrorField = "Name";
            }

            // 验证时薪（必填）
            if (HourlyRate <= 0)
            {
                validationErrors.Add("• 时薪必须大于0");
                HourlyRateEntryBackgroundColor = Colors.LightPink;
                if (string.IsNullOrEmpty(firstErrorField))
                    firstErrorField = "HourlyRate";
            }
            else if (HourlyRate > 1000)
            {
                validationErrors.Add("• 时薪超过1000，请确认是否正确");
                HourlyRateEntryBackgroundColor = Colors.LightYellow; // 浅黄色警告
            }

            // 验证支付周期（必填）
            if (PaymentCycleDays <= 0)
            {
                validationErrors.Add("• 支付周期必须大于0天");
                PaymentCycleDaysEntryBackgroundColor = Colors.LightPink;
                if (string.IsNullOrEmpty(firstErrorField))
                    firstErrorField = "PaymentCycleDays";
            }
            else if (PaymentCycleDays > 365)
            {
                validationErrors.Add("• 支付周期超过365天，请确认是否正确");
                PaymentCycleDaysEntryBackgroundColor = Colors.LightYellow;
            }

            // 如果有验证错误，显示详细错误信息
            if (validationErrors.Count > 0)
            {
                var errorMessage = "请修正以下问题：\n\n" + string.Join("\n", validationErrors);
                ShowErrorAsync("表单验证失败", errorMessage).ConfigureAwait(false);

                // 尝试将焦点设置到第一个有错误的字段
                SetFocusToField(firstErrorField);

                return false;
            }

            return true;
        }

        /// <summary>
        /// 重置所有字段的背景色
        /// </summary>
        private void ResetFieldBackgroundColors()
        {
            NameEntryBackgroundColor = Colors.LightGray;
            HourlyRateEntryBackgroundColor = Colors.LightGray;
            PaymentCycleDaysEntryBackgroundColor = Colors.LightGray;
        }

        /// <summary>
        /// 设置焦点到指定字段
        /// </summary>
        private void SetFocusToField(string fieldName)
        {
            try
            {
                // 在主线程上执行焦点设置
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // 这里可以通过消息传递或其他方式通知View设置焦点
                    // 由于MVVM模式的限制，我们通过属性变化来提示UI
                    System.Diagnostics.Debug.WriteLine($"需要将焦点设置到字段: {fieldName}");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置焦点失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化颜色选项
        /// </summary>
        private void InitializeColorOptions()
        {
            ColorOptions.Clear();

            // 精选10种视觉上有明显区别的颜色
            var colors = new[]
            {
                new ColorOption { HexValue = "#007ACC", Name = "专业蓝", EnglishName = "Professional Blue", Category = "商务" },
                new ColorOption { HexValue = "#28A745", Name = "成功绿", EnglishName = "Success Green", Category = "积极" },
                new ColorOption { HexValue = "#DC3545", Name = "警示红", EnglishName = "Alert Red", Category = "重要" },
                new ColorOption { HexValue = "#FFC107", Name = "阳光黄", EnglishName = "Sunshine Yellow", Category = "活力" },
                new ColorOption { HexValue = "#6F42C1", Name = "优雅紫", EnglishName = "Elegant Purple", Category = "创意" },
                new ColorOption { HexValue = "#FD7E14", Name = "活力橙", EnglishName = "Vibrant Orange", Category = "热情" },
                new ColorOption { HexValue = "#20C997", Name = "清新青", EnglishName = "Fresh Teal", Category = "清新" },
                new ColorOption { HexValue = "#E83E8C", Name = "玫瑰粉", EnglishName = "Rose Pink", Category = "温馨" },
                new ColorOption { HexValue = "#6C757D", Name = "中性灰", EnglishName = "Neutral Gray", Category = "稳重" },
                new ColorOption { HexValue = "#8B4513", Name = "咖啡棕", EnglishName = "Coffee Brown", Category = "经典" }
            };

            foreach (var color in colors)
            {
                ColorOptions.Add(color);
            }

            // 设置默认选中的颜色
            UpdateSelectedColor(Color);
        }
    }
}
