<?xml version="1.0" encoding="utf-8" ?>
<!--
    通知历史视图 / Notification History View
    显示应用程序发送的通知历史记录
    Display history of notifications sent by the application
-->
<ContentPage x:Class="Scheduler.Views.NotificationHistoryView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             x:DataType="viewmodels:NotificationHistoryViewModel"
             Title="{Binding LocalizedTexts.NotificationHistory}">

    <!-- 页面内容 / Page content -->
    <Grid RowDefinitions="Auto,Auto,*">

        <!-- 班次通知管理区域 / Shift notification management section -->
        <Frame Grid.Row="0" BackgroundColor="#E8F5E8" HasShadow="False" Padding="15" Margin="10,10,10,5"
               IsVisible="{Binding IsShiftNotificationSectionVisible}">
            <StackLayout Spacing="15">

                <!-- 标题和测试按钮 / Title and test button -->
                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                    <Label Grid.Column="0"
                           Text="{Binding LocalizedTexts.ShiftNotificationManagement}"
                           FontSize="16"
                           FontAttributes="Bold"
                           VerticalOptions="Center"
                           TextColor="#2E7D32" />
                    <Button Grid.Column="1"
                            Text="{Binding LocalizedTexts.TestNotification}"
                            Command="{Binding SendTestNotificationCommand}"
                            BackgroundColor="#4CAF50"
                            TextColor="White"
                            FontSize="12"
                            Padding="10,5"
                            CornerRadius="15" />
                </Grid>

                <!-- 已启用通知的班次列表 / List of shifts with enabled notifications -->
                <CollectionView ItemsSource="{Binding ShiftsWithNotifications}"
                                EmptyView="{Binding LocalizedTexts.NoShiftsWithNotifications}"
                                MaximumHeightRequest="200">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="models:Shift">
                            <Frame BackgroundColor="White"
                                   HasShadow="False"
                                   CornerRadius="8"
                                   Padding="12"
                                   Margin="0,2">
                                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                                    <!-- 班次信息 / Shift information -->
                                    <StackLayout Grid.Column="0" Spacing="2">
                                        <Label Text="{Binding EmployerName}"
                                               FontSize="14"
                                               FontAttributes="Bold"
                                               TextColor="#1976D2" />
                                        <Label FontSize="12" TextColor="#666">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <Span Text="📅 " />
                                                    <Span Text="{Binding StartTime, StringFormat='{0:MM/dd HH:mm}'}" />
                                                    <Span Text=" - " />
                                                    <Span Text="{Binding EndTime, StringFormat='{0:HH:mm}'}" />
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label FontSize="11" TextColor="#999"
                                               Text="{Binding ReminderMinutes, StringFormat='🔔 Remind {0} minutes in advance'}" />
                                    </StackLayout>

                                    <!-- 取消通知按钮 / Cancel notification button -->
                                    <Button Grid.Column="1"
                                            Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=LocalizedTexts.CancelNotification}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=CancelShiftNotificationCommand}"
                                            CommandParameter="{Binding .}"
                                            BackgroundColor="#FF5722"
                                            TextColor="White"
                                            FontSize="11"
                                            Padding="8,4"
                                            CornerRadius="12"
                                            VerticalOptions="Center" />
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- 刷新按钮 / Refresh button -->
                <Button Text="{Binding LocalizedTexts.RefreshData}"
                        Command="{Binding RefreshAllDataCommand}"
                        BackgroundColor="#2196F3"
                        TextColor="White"
                        FontSize="12"
                        Padding="10,5"
                        CornerRadius="15"
                        HorizontalOptions="Center" />

            </StackLayout>
        </Frame>

        <!-- 筛选和搜索区域 / Filter and search section -->
        <Frame Grid.Row="1" BackgroundColor="#F5F5F5" HasShadow="False" Padding="15" Margin="10,10,10,5">
            <StackLayout Spacing="15">
                
                <!-- 搜索框 / Search box -->
                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                    <Entry Grid.Column="0"
                           Text="{Binding SearchText}"
                           Placeholder="{Binding LocalizedTexts.SearchPlaceholder}"
                           BackgroundColor="White" />
                    <Button Grid.Column="1"
                            Text="{Binding LocalizedTexts.Search}"
                            Command="{Binding SearchNotificationsCommand}"
                            BackgroundColor="#2196F3"
                            TextColor="White"
                            WidthRequest="80" />
                </Grid>
                
                <!-- 筛选选项 / Filter options -->
                <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                    <!-- 通知类型筛选 / Notification type filter -->
                    <StackLayout Grid.Column="0">
                        <Label Text="{Binding LocalizedTexts.NotificationType}" FontSize="12" TextColor="#666" />
                        <Picker ItemsSource="{Binding NotificationTypeOptions}"
                                SelectedItem="{Binding SelectedType}"
                                BackgroundColor="White">
                            <Picker.ItemDisplayBinding>
                                <Binding Path="." Converter="{StaticResource NotificationTypeToStringConverter}" />
                            </Picker.ItemDisplayBinding>
                        </Picker>
                    </StackLayout>
                    
                    <!-- 通知状态筛选 / Notification status filter -->
                    <StackLayout Grid.Column="1">
                        <Label Text="{Binding LocalizedTexts.NotificationStatus}" FontSize="12" TextColor="#666" />
                        <Picker ItemsSource="{Binding NotificationStatusOptions}"
                                SelectedItem="{Binding SelectedStatus}"
                                BackgroundColor="White">
                            <Picker.ItemDisplayBinding>
                                <Binding Path="." Converter="{StaticResource NotificationStatusToStringConverter}" />
                            </Picker.ItemDisplayBinding>
                        </Picker>
                    </StackLayout>
                </Grid>
                
                <!-- 日期范围筛选 / Date range filter -->
                <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                    <StackLayout Grid.Column="0">
                        <Label Text="{Binding LocalizedTexts.StartDate}" FontSize="12" TextColor="#666" />
                        <DatePicker Date="{Binding StartDate}" BackgroundColor="White" />
                    </StackLayout>
                    <StackLayout Grid.Column="1">
                        <Label Text="{Binding LocalizedTexts.EndDate}" FontSize="12" TextColor="#666" />
                        <DatePicker Date="{Binding EndDate}" BackgroundColor="White" />
                    </StackLayout>
                </Grid>
                
                <!-- 操作按钮 / Action buttons -->
                <Grid ColumnDefinitions="*,*,*" ColumnSpacing="10">
                    <Button Grid.Column="0"
                            Text="{Binding LocalizedTexts.ClearFilters}"
                            Command="{Binding ClearFiltersCommand}"
                            BackgroundColor="#FF9800"
                            TextColor="White"
                            FontSize="12" />
                    <Button Grid.Column="1"
                            Text="{Binding LocalizedTexts.Refresh}"
                            Command="{Binding LoadNotificationsCommand}"
                            BackgroundColor="#4CAF50"
                            TextColor="White"
                            FontSize="12" />
                    <Button Grid.Column="2"
                            Text="{Binding LocalizedTexts.ClearAll}"
                            Command="{Binding ClearAllNotificationsCommand}"
                            BackgroundColor="#F44336"
                            TextColor="White"
                            FontSize="12" />
                </Grid>
                
            </StackLayout>
        </Frame>
        
        <!-- 通知列表区域 / Notification list section -->
        <Grid Grid.Row="2" RowDefinitions="Auto,*,Auto">

            <!-- 加载指示器 / Loading indicator -->
            <ActivityIndicator Grid.Row="0"
                               IsVisible="{Binding IsLoading}"
                               IsRunning="{Binding IsLoading}"
                               Margin="20" />

            <!-- 通知列表 / Notification list -->
            <CollectionView Grid.Row="1"
                            ItemsSource="{Binding Notifications}"
                            IsVisible="{Binding IsLoading, Converter={StaticResource InvertedBoolConverter}}"
                            Margin="10,0">
                
                <CollectionView.EmptyView>
                    <StackLayout Padding="50" HorizontalOptions="Center" VerticalOptions="Center">
                        <Label Text="📭" FontSize="48" HorizontalOptions="Center" />
                        <Label Text="{Binding LocalizedTexts.NoNotificationRecords}"
                               FontSize="16"
                               TextColor="#999"
                               HorizontalOptions="Center" />
                        <Label Text="{Binding LocalizedTexts.TryAdjustingFilters}"
                               FontSize="12"
                               TextColor="#CCC"
                               HorizontalOptions="Center"
                               Margin="0,10,0,0" />
                    </StackLayout>
                </CollectionView.EmptyView>
                
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:NotificationRecord">
                        <Frame BackgroundColor="White" 
                               HasShadow="True" 
                               CornerRadius="8" 
                               Margin="0,5" 
                               Padding="15">
                            
                            <Grid RowDefinitions="Auto,Auto,Auto,Auto" ColumnDefinitions="*,Auto">
                                
                                <!-- 通知标题和状态 / Notification title and status -->
                                <StackLayout Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Spacing="10">
                                    <Label Text="{Binding Title}" 
                                           FontSize="16" 
                                           FontAttributes="Bold" 
                                           TextColor="#333"
                                           HorizontalOptions="FillAndExpand" />
                                    <Frame BackgroundColor="{Binding Status, Converter={StaticResource NotificationStatusToColorConverter}}"
                                           Padding="8,4"
                                           CornerRadius="12"
                                           HasShadow="False">
                                        <Label Text="{Binding Status, Converter={StaticResource NotificationStatusToStringConverter}}" 
                                               FontSize="10" 
                                               TextColor="White" />
                                    </Frame>
                                </StackLayout>
                                
                                <!-- 通知类型标签 / Notification type badge -->
                                <Frame Grid.Row="0" Grid.Column="1"
                                       BackgroundColor="{Binding Type, Converter={StaticResource NotificationTypeToColorConverter}}"
                                       Padding="6,3"
                                       CornerRadius="10"
                                       HasShadow="False"
                                       VerticalOptions="Start">
                                    <Label Text="{Binding Type, Converter={StaticResource NotificationTypeToStringConverter}}" 
                                           FontSize="9" 
                                           TextColor="White" />
                                </Frame>
                                
                                <!-- 通知内容 / Notification message -->
                                <Label Grid.Row="1" Grid.ColumnSpan="2"
                                       Text="{Binding Message}" 
                                       FontSize="14" 
                                       TextColor="#666"
                                       Margin="0,5,0,0" />
                                
                                <!-- 时间信息 / Time information -->
                                <StackLayout Grid.Row="2" Grid.ColumnSpan="2" 
                                             Orientation="Horizontal" 
                                             Spacing="15"
                                             Margin="0,10,0,0">
                                    <Label FontSize="12" TextColor="#999"
                                           Text="{Binding ScheduledAt, StringFormat='Scheduled time: {0:MM-dd HH:mm}'}" />
                                    <Label FontSize="12" TextColor="#999"
                                           IsVisible="{Binding ReadAt, Converter={StaticResource IsNotNullConverter}}"
                                           Text="{Binding ReadAt, StringFormat='Read time: {0:MM-dd HH:mm}'}" />
                                </StackLayout>
                                
                                <!-- 操作按钮 / Action buttons -->
                                <StackLayout Grid.Row="3" Grid.ColumnSpan="2" 
                                             Orientation="Horizontal" 
                                             Spacing="10"
                                             Margin="0,10,0,0">
                                    
                                    <Button Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=LocalizedTexts.MarkAsRead}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=MarkAsReadCommand}"
                                            CommandParameter="{Binding .}"
                                            BackgroundColor="#4CAF50"
                                            TextColor="White"
                                            FontSize="12"
                                            Padding="10,5"
                                            CornerRadius="15"
                                            IsVisible="{Binding Status, Converter={StaticResource NotificationStatusToUnreadVisibilityConverter}}" />
                                    
                                    <Button Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=LocalizedTexts.Resend}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=ResendNotificationCommand}"
                                            CommandParameter="{Binding .}"
                                            BackgroundColor="#2196F3"
                                            TextColor="White"
                                            FontSize="12"
                                            Padding="10,5"
                                            CornerRadius="15" />
                                    
                                    <Button Text="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=LocalizedTexts.Delete}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:NotificationHistoryViewModel}}, Path=DeleteNotificationCommand}"
                                            CommandParameter="{Binding .}"
                                            BackgroundColor="#F44336"
                                            TextColor="White"
                                            FontSize="12"
                                            Padding="10,5"
                                            CornerRadius="15" />
                                    
                                </StackLayout>
                                
                            </Grid>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            
            <!-- 状态消息 / Status message -->
            <Frame Grid.Row="2" 
                   IsVisible="{Binding StatusMessage, Converter={StaticResource StringToBoolConverter}}"
                   BackgroundColor="#E3F2FD" 
                   HasShadow="False" 
                   CornerRadius="8" 
                   Padding="15"
                   Margin="10">
                <StackLayout Orientation="Horizontal">
                    <Label Text="{Binding StatusMessage}" 
                           TextColor="#1976D2" 
                           FontSize="14" 
                           VerticalOptions="Center"
                           HorizontalOptions="FillAndExpand" />
                    <Button Text="×" 
                            Command="{Binding ClearStatusMessageCommand}"
                            BackgroundColor="Transparent"
                            TextColor="#1976D2"
                            FontSize="18"
                            Padding="5"
                            WidthRequest="30"
                            HeightRequest="30" />
                </StackLayout>
            </Frame>
            
        </Grid>
    </Grid>
</ContentPage>
