<?xml version="1.0" encoding="UTF-8" ?>
<!--
    应用程序Shell导航框架 / Application Shell Navigation Framework

    这个文件定义了整个应用程序的导航结构和用户界面框架
    This file defines the navigation structure and user interface framework for the entire application

    主要功能 / Main Features:
    1. 定义应用程序的导航层次结构 / Define application navigation hierarchy
    2. 配置标签页导航和路由 / Configure tab navigation and routing
    3. 管理登录状态和页面访问控制 / Manage login state and page access control
    4. 提供统一的应用程序外观和行为 / Provide unified application appearance and behavior

    导航流程 / Navigation Flow:
    登录页面 → 主应用标签页导航 / Login Page → Main Application Tab Navigation
-->
<Shell
    x:Class="Scheduler.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Scheduler.Views"
    xmlns:v="clr-namespace:Scheduler"
    Title="Scheduler">

    <!-- ==================== 登录页面配置 / Login Page Configuration ==================== -->
    <!--
        应用程序的初始页面，用户必须先通过登录验证才能访问主要功能
        Initial page of the application, users must pass login verification before accessing main features

        配置说明 / Configuration Details:
        - Route="Login": 定义登录页面的路由地址 / Define login page route address
        - Shell.FlyoutBehavior="Disabled": 禁用侧边栏菜单 / Disable flyout menu
        - Shell.TabBarIsVisible="False": 隐藏底部标签栏 / Hide bottom tab bar
        - ContentTemplate: 使用数据模板延迟加载页面 / Use data template for lazy loading
    -->
    <ShellContent
        Title="Login"
        Route="Login"
        ContentTemplate="{DataTemplate local:LoginView}"
        Shell.FlyoutBehavior="Disabled"
        Shell.TabBarIsVisible="False"/>

    <!-- ==================== 主应用标签页导航 / Main Application Tab Navigation ==================== -->
    <!--
        登录成功后显示的主要导航界面，包含应用程序的核心功能模块
        Main navigation interface displayed after successful login, containing core functional modules

        设计原则 / Design Principles:
        1. 简洁明了的标签页布局 / Clean and clear tab layout
        2. 直观的图标和标题 / Intuitive icons and titles
        3. 快速访问常用功能 / Quick access to frequently used features
        4. 一致的视觉风格 / Consistent visual style
    -->
    <TabBar Route="Main">

        <!-- 主页标签 / Home Tab -->
        <!--
            应用程序的仪表板页面，显示工作概览和快捷操作
            Application dashboard page showing work overview and quick actions
        -->
        <ShellContent
            Title="Home"
            Route="Home"
            ContentTemplate="{DataTemplate local:HomeView}">
            <ShellContent.Icon>
                <!-- 使用字体图标提供一致的视觉体验 / Use font icons for consistent visual experience -->
                <FontImageSource FontFamily="OpenSansRegular" Glyph="H" Size="20" Color="Gray" />
            </ShellContent.Icon>
        </ShellContent>

        <!-- 日历标签 / Calendar Tab -->
        <!--
            日历视图页面，用于查看和管理工作班次安排
            Calendar view page for viewing and managing work shift schedules
        -->
        <ShellContent
            Title="Calendar"
            Route="Calendar"
            ContentTemplate="{DataTemplate local:CalendarView}">
            <ShellContent.Icon>
                <FontImageSource FontFamily="OpenSansRegular" Glyph="C" Size="20" Color="Gray" />
            </ShellContent.Icon>
        </ShellContent>

        <!-- 工资标签 / Pay Tab -->
        <!--
            工资查看页面，显示收入统计和工资计算结果
            Pay view page showing income statistics and salary calculation results
        -->
        <ShellContent
            Title="Pay"
            Route="Pay"
            ContentTemplate="{DataTemplate local:PayView}">
            <ShellContent.Icon>
                <FontImageSource FontFamily="OpenSansRegular" Glyph="P" Size="20" Color="Gray" />
            </ShellContent.Icon>
        </ShellContent>

    </TabBar>

</Shell>
