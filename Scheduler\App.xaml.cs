﻿// 引入应用服务命名空间 / Import application services namespace
using Scheduler.Services;

namespace Scheduler
{
    /// <summary>
    /// 应用程序主类 / Main Application Class
    /// 继承自MAUI的Application类，负责应用程序的生命周期管理和窗口创建
    /// Inherits from MAUI's Application class, responsible for application lifecycle management and window creation
    /// </summary>
    /// <remarks>
    /// 这个类是应用程序的入口点，在MauiProgram.cs配置完依赖注入后被调用
    /// This class is the application entry point, called after dependency injection is configured in MauiProgram.cs
    /// 主要职责包括：
    /// Main responsibilities include:
    /// 1. 初始化应用程序组件 / Initialize application components
    /// 2. 创建主窗口和Shell导航框架 / Create main window and Shell navigation framework
    /// 3. 验证依赖注入配置 / Validate dependency injection configuration
    /// 4. 设置应用程序的全局行为 / Set global application behavior
    /// </remarks>
    public partial class App : Application
    {
        /// <summary>
        /// 应用程序构造函数 / Application Constructor
        /// 初始化应用程序的基本组件和资源
        /// Initialize basic application components and resources
        /// </summary>
        /// <remarks>
        /// 在这个阶段，依赖注入容器已经配置完成，但服务还未完全可用
        /// At this stage, the dependency injection container is configured, but services are not fully available yet
        /// InitializeComponent()会加载App.xaml中定义的资源字典和样式
        /// InitializeComponent() loads resource dictionaries and styles defined in App.xaml
        /// </remarks>
        public App()
        {
            // 初始化XAML组件，加载应用程序级别的资源和样式
            // Initialize XAML components, load application-level resources and styles
            InitializeComponent();
        }

        /// <summary>
        /// 创建应用程序主窗口 / Create Main Application Window
        /// 这是MAUI应用程序生命周期中的关键方法，负责创建和配置主窗口
        /// This is a key method in the MAUI application lifecycle, responsible for creating and configuring the main window
        /// </summary>
        /// <param name="activationState">应用程序激活状态信息 / Application activation state information</param>
        /// <returns>配置完成的应用程序窗口实例 / Configured application window instance</returns>
        /// <remarks>
        /// 此方法在以下情况下被调用：
        /// This method is called in the following scenarios:
        /// 1. 应用程序首次启动 / Application first launch
        /// 2. 应用程序从后台恢复 / Application resume from background
        /// 3. 多窗口场景下创建新窗口 / Creating new windows in multi-window scenarios
        /// </remarks>
        protected override Window CreateWindow(IActivationState? activationState)
        {
            // ==================== 依赖注入验证 / Dependency Injection Validation ====================
            // 在创建窗口前验证依赖注入配置是否正确工作
            // Validate that dependency injection configuration is working correctly before creating the window
            try
            {
                // 获取当前平台应用程序的服务提供者 / Get the service provider from current platform application
                // 这是访问MauiProgram.cs中配置的依赖注入容器的正确方式
                // This is the correct way to access the dependency injection container configured in MauiProgram.cs
                var serviceProvider = IPlatformApplication.Current?.Services;

                if (serviceProvider != null)
                {
                    // 尝试获取本地化服务实例来测试依赖注入是否正常工作
                    // Try to get localization service instance to test if dependency injection is working properly
                    var localizationService = serviceProvider.GetService<LocalizationService>();

                    if (localizationService != null)
                    {
                        // 依赖注入测试成功 / Dependency injection test successful
                        System.Diagnostics.Debug.WriteLine("✓ LocalizationService依赖注入测试成功");
                        System.Diagnostics.Debug.WriteLine("✓ LocalizationService dependency injection test successful");
                    }
                    else
                    {
                        // 服务获取失败，可能是注册问题 / Service retrieval failed, possibly a registration issue
                        System.Diagnostics.Debug.WriteLine("✗ LocalizationService依赖注入测试失败");
                        System.Diagnostics.Debug.WriteLine("✗ LocalizationService dependency injection test failed");
                    }
                }
                else
                {
                    // 服务提供者获取失败，严重的配置问题 / Service provider retrieval failed, serious configuration issue
                    System.Diagnostics.Debug.WriteLine("✗ ServiceProvider获取失败");
                    System.Diagnostics.Debug.WriteLine("✗ ServiceProvider retrieval failed");
                }
            }
            catch (Exception ex)
            {
                // 捕获并记录依赖注入测试过程中的任何异常 / Catch and log any exceptions during dependency injection testing
                System.Diagnostics.Debug.WriteLine($"❌ 依赖注入测试异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Dependency injection test exception: {ex.Message}");
            }

            // ==================== 主窗口创建 / Main Window Creation ====================
            // 创建应用程序的主窗口，使用AppShell作为根容器
            // Create the main application window using AppShell as the root container
            // AppShell提供了导航框架、标签页管理和路由功能
            // AppShell provides navigation framework, tab management, and routing functionality
            var window = new Window(new AppShell());

            // ==================== 数据库初始化说明 / Database Initialization Note ====================
            // 数据库将在首次使用时自动初始化，无需在此处手动初始化
            // Database will be automatically initialized on first use, no manual initialization needed here
            // 这种延迟初始化策略可以：
            // This lazy initialization strategy can:
            // 1. 加快应用程序启动速度 / Speed up application startup
            // 2. 避免阻塞UI线程 / Avoid blocking the UI thread
            // 3. 只在需要时创建数据库连接 / Create database connections only when needed

            // 返回配置完成的窗口实例 / Return the configured window instance
            return window;
        }
    }
}