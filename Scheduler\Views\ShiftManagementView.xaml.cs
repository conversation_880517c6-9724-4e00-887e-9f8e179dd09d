// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 班次管理视图 / Shift Management View
/// 用于创建、编辑和管理工作班次的综合管理页面
/// Comprehensive management page for creating, editing and managing work shifts
/// </summary>
public partial class ShiftManagementView : ContentPage
{
    // 班次管理视图模型实例 / Shift management view model instance
    private readonly ShiftManagementViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化班次管理视图并配置数据绑定 / Initialize shift management view and configure data binding
    /// </summary>
    /// <param name="viewModel">班次管理视图模型 / Shift management view model</param>
    public ShiftManagementView(ShiftManagementViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 初始化班次数据和雇主列表 / Initialize shift data and employer list
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        try
        {
            // 初始化视图模型数据 / Initialize view model data
            await _viewModel.InitializeAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ShiftManagementView OnAppearing 错误 / Error: {ex.Message}");
            // 显示错误提示给用户 / Show error message to user
            await DisplayAlert("错误 / Error", "加载数据失败，请重试 / Failed to load data, please try again", "确定 / OK");
        }
    }
}
