<?xml version="1.0" encoding="utf-8" ?>
<!--
    工作日志视图 / Work Log View
    显示工作时间记录、班次统计和工作历史
    Display work time records, shift statistics and work history
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:Class="Scheduler.Views.LogView"
             x:DataType="viewmodels:LogViewModel"
             Title="{Binding Title}">

    <!-- 下拉刷新容器 / Pull-to-refresh container -->
    <!-- 修复：优化RefreshView和ScrollView结构，避免Android平台触摸冲突 -->
    <!-- Fix: Optimize RefreshView and ScrollView structure to avoid touch conflicts on Android -->
    <RefreshView IsRefreshing="{Binding IsRefreshing}"
                 Command="{Binding RefreshCommand}">
        <ScrollView>
            <VerticalStackLayout Padding="16" Spacing="16">

                <!-- 顶部状态区域 / Top status area -->
                <Frame BackgroundColor="{AppThemeBinding Light=#F8F9FA, Dark=#2D3748}"
                       HasShadow="True" CornerRadius="12">
                    <VerticalStackLayout Spacing="16">
                        <!-- 时间显示部分 / Time display section -->
                        <VerticalStackLayout Spacing="8">
                            <Label Text="{Binding CurrentTime}"
                                   FontSize="28" FontAttributes="Bold"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#2D3748, Dark=#F8F9FA}"/>
                            <Label Text="{Binding CurrentDate}"
                                   FontSize="16"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        </VerticalStackLayout>

                        <!-- 分隔线 / Separator line -->
                        <BoxView HeightRequest="1"
                                 BackgroundColor="{AppThemeBinding Light=#E5E7EB, Dark=#4B5563}"
                                 HorizontalOptions="FillAndExpand"/>

                        <!-- 今日工作统计 / Today's work statistics -->
                        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="16">
                            <!-- 工作时长统计 / Work hours statistics -->
                            <VerticalStackLayout Grid.Column="0" Spacing="4">
                                <Label Text="{Binding TodayWorkedHours, StringFormat='{0:F1}h'}"
                                       FontSize="20" FontAttributes="Bold"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#10B981, Dark=#34D399}"/>
                                <Label Text="Worked"
                                       FontSize="12"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            </VerticalStackLayout>

                            <!-- 班次数量统计 / Shift count statistics -->
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label Text="{Binding TodayShiftsCount}"
                                       FontSize="20" FontAttributes="Bold"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#3B82F6, Dark=#60A5FA}"/>
                                <Label Text="Scheduled"
                                       FontSize="12"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            </VerticalStackLayout>

                            <VerticalStackLayout Grid.Column="2" Spacing="4">
                                <Label Text="Ready for work"
                                       FontSize="12" FontAttributes="Bold"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#8B5CF6, Dark=#A78BFA}"
                                       LineBreakMode="WordWrap"/>
                            </VerticalStackLayout>
                        </Grid>
                    </VerticalStackLayout>
                </Frame>

                <!-- 快速操作区域 -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12"
                       IsVisible="{Binding IsQuickActionsVisible}">
                    <VerticalStackLayout Spacing="12">
                        <Label Text="{Binding LocalizedTexts.QuickActions}"
                               FontSize="16" FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="12">
                            <Button Grid.Column="0"
                                    Text="{Binding LocalizedTexts.ClockIn}"
                                    BackgroundColor="#10B981"
                                    TextColor="White"
                                    FontSize="14"
                                    IsEnabled="{Binding CanMarkIn}"
                                    IsVisible="{Binding IsLogConfirmationEnabled}"
                                    Command="{Binding MarkInCommand}"/>

                            <Button Grid.Column="1"
                                    Text="{Binding LocalizedTexts.ClockOut}"
                                    BackgroundColor="#3B82F6"
                                    TextColor="White"
                                    FontSize="14"
                                    IsEnabled="{Binding CanMarkOut}"
                                    IsVisible="{Binding IsLogConfirmationEnabled}"
                                    Command="{Binding MarkOutCommand}"/>

                            <Button Grid.Column="2"
                                    Text="{Binding LocalizedTexts.AddNote}"
                                    BackgroundColor="#8B5CF6"
                                    TextColor="White"
                                    FontSize="14"
                                    Command="{Binding AddNoteCommand}"/>
                        </Grid>
                    </VerticalStackLayout>
                </Frame>

                <!-- 筛选控制区域 -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12">
                    <VerticalStackLayout Spacing="12">
                        <Label Text="Filter And Search"
                               FontSize="16" FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                        <!-- 时间范围选择 -->
                        <Grid ColumnDefinitions="*,*,*,*" ColumnSpacing="8">
                            <Button Grid.Column="0"
                                    Text="Today"
                                    BackgroundColor="#3B82F6"
                                    TextColor="White"
                                    FontSize="12"
                                    Command="{Binding FilterByTimeRangeCommand}"
                                    CommandParameter="Today"/>

                            <Button Grid.Column="1"
                                    Text="Week"
                                    BackgroundColor="#6B7280"
                                    TextColor="White"
                                    FontSize="12"
                                    Command="{Binding FilterByTimeRangeCommand}"
                                    CommandParameter="Week"/>

                            <Button Grid.Column="2"
                                    Text="Mounth"
                                    BackgroundColor="#6B7280"
                                    TextColor="White"
                                    FontSize="12"
                                    Command="{Binding FilterByTimeRangeCommand}"
                                    CommandParameter="Month"/>

                            <Button Grid.Column="3"
                                    Text="All"
                                    BackgroundColor="#6B7280"
                                    TextColor="White"
                                    FontSize="12"
                                    Command="{Binding FilterByTimeRangeCommand}"
                                    CommandParameter="All"/>
                        </Grid>

                        <!-- 搜索框 -->
                        <Frame BackgroundColor="{AppThemeBinding Light=#F8F9FA, Dark=#4B5563}"
                               HasShadow="False"
                               CornerRadius="8"
                               Padding="0">
                            <Entry Text="{Binding SearchText}"
                                   Placeholder="Search work location、notes..."
                                   FontSize="14"
                                   BackgroundColor="Transparent"
                                   TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"
                                   PlaceholderColor="{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}"
                                   Margin="12,8"
                                   ReturnCommand="{Binding SearchCommand}"/>
                        </Frame>

                        <!-- 清除筛选按钮 -->
                        <Button Text="Clean Filter"
                                BackgroundColor="Transparent"
                                TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                FontSize="12"
                                Command="{Binding ClearFiltersCommand}"/>
                    </VerticalStackLayout>
                </Frame>

                <!-- 工作记录列表 -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12">
                    <VerticalStackLayout Spacing="12">
                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="12">
                            <Label Grid.Column="0"
                                   Text="Wrok Log"
                                   FontSize="16" FontAttributes="Bold"
                                   VerticalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                            <Label Grid.Column="1"
                                   FontSize="12"
                                   VerticalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}">
                                <Label.FormattedText>
                                    <FormattedString>
                                        <Span Text="Records: "/>
                                        <Span Text="{Binding FilteredWorkRecords.Count}"/>
                                    </FormattedString>
                                </Label.FormattedText>
                            </Label>
                        </Grid>

                        <!-- 工作记录列表 -->
                        <CollectionView ItemsSource="{Binding FilteredWorkRecords}"
                                        IsVisible="{Binding HasWorkRecords}">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Frame Margin="0,4"
                                           Padding="12"
                                           CornerRadius="8"
                                           BackgroundColor="{AppThemeBinding Light=#F8F9FA, Dark=#4B5563}"
                                           HasShadow="False">
                                        <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">

                                            <!-- 状态指示器 -->
                                            <BoxView Grid.Column="0"
                                                     WidthRequest="4"
                                                     HeightRequest="40"
                                                     BackgroundColor="{Binding IsCompleted, Converter={StaticResource BoolToColorConverter}}"
                                                     VerticalOptions="Center"/>

                                            <!-- 记录信息 -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                                <!-- 时间信息 -->
                                                <Label FontAttributes="Bold" FontSize="16"
                                                       TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}">
                                                    <Label.Text>
                                                        <MultiBinding StringFormat="{}{0:HH:mm} - {1:HH:mm}">
                                                            <Binding Path="ClockInTime"/>
                                                            <Binding Path="ClockOutTime"/>
                                                        </MultiBinding>
                                                    </Label.Text>
                                                </Label>

                                                <!-- 工作地点 -->
                                                <Label Text="{Binding ActualLocation}"
                                                       FontSize="14"
                                                       TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                                       IsVisible="{Binding ActualLocation, Converter={StaticResource StringToBoolConverter}}"/>

                                                <!-- 工作时长 -->
                                                <Label FontSize="12"
                                                       TextColor="{AppThemeBinding Light=#10B981, Dark=#34D399}">
                                                    <Label.FormattedText>
                                                        <FormattedString>
                                                            <Span Text="Work Hours: "/>
                                                            <Span Text="{Binding FinalHours}"/>
                                                        </FormattedString>
                                                    </Label.FormattedText>
                                                </Label>

                                                <!-- 工作笔记预览 -->
                                                <Label Text="{Binding Notes}"
                                                       FontSize="12"
                                                       TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                                       LineBreakMode="TailTruncation"
                                                       MaxLines="2"
                                                       IsVisible="{Binding Notes, Converter={StaticResource StringToBoolConverter}}"/>
                                            </VerticalStackLayout>

                                            <!-- 操作按钮 -->
                                            <VerticalStackLayout Grid.Column="2" Spacing="4">
                                                <Button Text="{Binding LocalizedTexts.Details}"
                                                        BackgroundColor="#3B82F6"
                                                        TextColor="White"
                                                        FontSize="10"
                                                        WidthRequest="50"
                                                        HeightRequest="30"
                                                        Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.ViewRecordDetailCommand}"
                                                        CommandParameter="{Binding .}"/>

                                                <Button Text="{Binding LocalizedTexts.Edit}"
                                                        BackgroundColor="#10B981"
                                                        TextColor="White"
                                                        FontSize="10"
                                                        WidthRequest="50"
                                                        HeightRequest="30"
                                                        Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.EditRecordCommand}"
                                                        CommandParameter="{Binding .}"/>
                                            </VerticalStackLayout>
                                        </Grid>

                                        <!-- 添加手势识别器用于长按删除 -->
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.ViewRecordDetailCommand}"
                                                                  CommandParameter="{Binding .}"/>
                                        </Frame.GestureRecognizers>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <!-- 空状态显示 -->
                        <VerticalStackLayout Spacing="16"
                                             Padding="32"
                                             IsVisible="{Binding HasWorkRecords, Converter={StaticResource InverseBoolConverter}}">
                            <Label Text="📝"
                                   FontSize="48"
                                   HorizontalOptions="Center"/>
                            <Label Text="Not any work log now"
                                   FontSize="16"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            <Label Text="Start your first day work memerey！"
                                   FontSize="14"
                                   HorizontalOptions="Center"
                                   TextColor="{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}"/>
                        </VerticalStackLayout>
                    </VerticalStackLayout>
                </Frame>

                <!-- 底部操作区域 -->
                <Frame BackgroundColor="{AppThemeBinding Light=#FFFFFF, Dark=#374151}"
                       HasShadow="True" CornerRadius="12">
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="12">
                        <Button Grid.Column="0"
                                Text="Export Log"
                                BackgroundColor="#8B5CF6"
                                TextColor="White"
                                FontSize="14"/>

                        <Button Grid.Column="1"
                                Text="Statistical Report"
                                BackgroundColor="#F59E0B"
                                TextColor="White"
                                FontSize="14"/>
                    </Grid>
                </Frame>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>