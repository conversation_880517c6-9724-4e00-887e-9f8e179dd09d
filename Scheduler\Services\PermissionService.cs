
namespace Scheduler.Services
{
    /// <summary>
    /// 权限状态枚举 / Permission Status Enumeration
    /// 定义各种权限的状态类型
    /// Defines status types for various permissions
    /// </summary>
    public enum PermissionStatus
    {
        /// <summary>未知状态 / Unknown status</summary>
        Unknown,
        /// <summary>拒绝 / Denied</summary>
        Denied,
        /// <summary>已授权 / Granted</summary>
        Granted,
        /// <summary>受限制 / Restricted</summary>
        Restricted,
        /// <summary>有限制的授权 / Limited authorization</summary>
        Limited
    }

    /// <summary>
    /// 权限管理服务接口 / Permission Management Service Interface
    /// 定义权限检查和请求的标准契约
    /// Defines standard contract for permission checking and requesting
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>检查通知权限状态 / Check notification permission status</summary>
        Task<PermissionStatus> CheckNotificationPermissionAsync();
        /// <summary>请求通知权限 / Request notification permission</summary>
        Task<PermissionStatus> RequestNotificationPermissionAsync();
        /// <summary>是否应该显示权限说明 / Whether should show permission rationale</summary>
        Task<bool> ShouldShowRationaleAsync();
        /// <summary>打开应用设置页面 / Open app settings page</summary>
        Task OpenAppSettingsAsync();
        /// <summary>检查位置权限状态 / Check location permission status</summary>
        Task<PermissionStatus> CheckLocationPermissionAsync();
        /// <summary>请求位置权限 / Request location permission</summary>
        Task<PermissionStatus> RequestLocationPermissionAsync();
    }

    /// <summary>
    /// 权限管理服务 / Permission Management Service
    /// 负责管理应用程序的各种权限请求和检查
    /// Responsible for managing various permission requests and checks for the application
    /// </summary>
    public class PermissionService : IPermissionService
    {
        /// <summary>
        /// 检查通知权限状态 / Check notification permission status
        /// 跨平台检查通知权限的当前状态
        /// Cross-platform check of current notification permission status
        /// </summary>
        /// <returns>权限状态 / Permission status</returns>
        public async Task<PermissionStatus> CheckNotificationPermissionAsync()
        {
            try
            {
#if ANDROID
                var status = await Permissions.CheckStatusAsync<Permissions.PostNotifications>();
                return ConvertPermissionStatus(status);
#elif IOS
                var status = await Permissions.CheckStatusAsync<Permissions.PostNotifications>();
                return ConvertPermissionStatus(status);
#else
                // Windows 和其他平台通常默认允许通知 / Windows and other platforms usually allow notifications by default
                return PermissionStatus.Granted;
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查通知权限失败: {ex.Message}");
                return PermissionStatus.Unknown;
            }
        }

        /// <summary>
        /// 请求通知权限
        /// </summary>
        public async Task<PermissionStatus> RequestNotificationPermissionAsync()
        {
            try
            {
#if ANDROID
                var status = await Permissions.RequestAsync<Permissions.PostNotifications>();
                return ConvertPermissionStatus(status);
#elif IOS
                var status = await Permissions.RequestAsync<Permissions.PostNotifications>();
                return ConvertPermissionStatus(status);
#else
                // Windows 和其他平台通常默认允许通知
                return PermissionStatus.Granted;
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"请求通知权限失败: {ex.Message}");
                return PermissionStatus.Denied;
            }
        }

        /// <summary>
        /// 检查是否应该显示权限说明
        /// </summary>
        public async Task<bool> ShouldShowRationaleAsync()
        {
            try
            {
#if ANDROID
                // Android平台的权限说明检查
                return false; // 简化实现，实际项目中可以使用平台特定的API
#else
                return false;
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查权限说明状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 打开应用设置页面
        /// </summary>
        public async Task OpenAppSettingsAsync()
        {
            try
            {
                AppInfo.ShowSettingsUI();
                await Task.CompletedTask; // 使方法异步
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开应用设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查位置权限状态
        /// </summary>
        public async Task<PermissionStatus> CheckLocationPermissionAsync()
        {
            try
            {
                var status = await Permissions.CheckStatusAsync<Permissions.LocationWhenInUse>();
                return ConvertPermissionStatus(status);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查位置权限失败: {ex.Message}");
                return PermissionStatus.Unknown;
            }
        }

        /// <summary>
        /// 请求位置权限
        /// </summary>
        public async Task<PermissionStatus> RequestLocationPermissionAsync()
        {
            try
            {
                var status = await Permissions.RequestAsync<Permissions.LocationWhenInUse>();
                return ConvertPermissionStatus(status);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"请求位置权限失败: {ex.Message}");
                return PermissionStatus.Denied;
            }
        }

        /// <summary>
        /// 转换权限状态
        /// </summary>
        private static PermissionStatus ConvertPermissionStatus(Microsoft.Maui.ApplicationModel.PermissionStatus status)
        {
            return status switch
            {
                Microsoft.Maui.ApplicationModel.PermissionStatus.Granted => PermissionStatus.Granted,
                Microsoft.Maui.ApplicationModel.PermissionStatus.Denied => PermissionStatus.Denied,
                Microsoft.Maui.ApplicationModel.PermissionStatus.Disabled => PermissionStatus.Restricted,
                Microsoft.Maui.ApplicationModel.PermissionStatus.Limited => PermissionStatus.Limited,
                Microsoft.Maui.ApplicationModel.PermissionStatus.Restricted => PermissionStatus.Restricted,
                _ => PermissionStatus.Unknown
            };
        }

        /// <summary>
        /// 显示权限请求对话框
        /// </summary>
        public async Task<bool> ShowPermissionRequestDialogAsync(string title, string message, string acceptText = "允许", string denyText = "拒绝")
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    return await Application.Current.MainPage.DisplayAlert(title, message, acceptText, denyText);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示权限对话框失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示权限被拒绝的说明对话框
        /// </summary>
        public async Task ShowPermissionDeniedDialogAsync(string title, string message, string settingsText = "去设置", string cancelText = "取消")
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    var result = await Application.Current.MainPage.DisplayAlert(title, message, settingsText, cancelText);
                    if (result)
                    {
                        await OpenAppSettingsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示权限被拒绝对话框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并请求通知权限（带用户友好的提示）
        /// </summary>
        public async Task<bool> EnsureNotificationPermissionAsync()
        {
            try
            {
                var currentStatus = await CheckNotificationPermissionAsync();
                
                if (currentStatus == PermissionStatus.Granted)
                {
                    return true;
                }

                // 如果权限被拒绝且应该显示说明
                if (currentStatus == PermissionStatus.Denied && await ShouldShowRationaleAsync())
                {
                    var shouldRequest = await ShowPermissionRequestDialogAsync(
                        "通知权限",
                        "为了及时提醒您的工作安排，应用需要发送通知的权限。请允许通知权限以获得最佳体验。",
                        "允许",
                        "暂不");

                    if (!shouldRequest)
                    {
                        return false;
                    }
                }

                // 请求权限
                var requestResult = await RequestNotificationPermissionAsync();
                
                if (requestResult == PermissionStatus.Granted)
                {
                    return true;
                }

                // 如果权限被永久拒绝，引导用户到设置页面
                if (requestResult == PermissionStatus.Denied)
                {
                    await ShowPermissionDeniedDialogAsync(
                        "通知权限被拒绝",
                        "通知权限对于及时提醒工作安排非常重要。您可以在设置中手动开启通知权限。",
                        "去设置",
                        "稍后");
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保通知权限失败: {ex.Message}");
                return false;
            }
        }
    }
}
