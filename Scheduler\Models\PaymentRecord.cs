// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 支付状态枚举 / Payment Status Enumeration
    /// 定义支付记录的不同状态
    /// Defines different states of payment records
    /// </summary>
    public enum PaymentStatus
    {
        Pending = 0,    // 待支付 / Pending payment
        Paid = 1,       // 已支付 / Paid
        Overdue = 2     // 逾期 / Overdue
    }

    /// <summary>
    /// 支付记录模型 / Payment Record Model
    /// 用于记录和管理工资支付信息
    /// Used to record and manage salary payment information
    /// </summary>
    [Table("PaymentRecords")]
    public class PaymentRecord
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 雇主ID / Employer ID
        /// 关联的雇主标识
        /// Associated employer identifier
        /// </summary>
        [DataAnnotations.Required]
        [Indexed]
        public int EmployerId { get; set; }

        /// <summary>
        /// 支付周期开始日期 / Payment Period Start Date
        /// 工资计算周期的开始日期
        /// Start date of the salary calculation period
        /// </summary>
        [DataAnnotations.Required]
        public DateTime PeriodStart { get; set; }

        /// <summary>
        /// 支付周期结束日期 / Payment Period End Date
        /// 工资计算周期的结束日期
        /// End date of the salary calculation period
        /// </summary>
        [DataAnnotations.Required]
        public DateTime PeriodEnd { get; set; }

        /// <summary>
        /// 总工作小时数 / Total Work Hours
        /// 该周期内的总工作时间
        /// Total working time in this period
        /// </summary>
        public double TotalHours { get; set; }

        /// <summary>
        /// 常规工作小时数 / Regular Work Hours
        /// 常规工作时间（非加班）
        /// Regular working hours (non-overtime)
        /// </summary>
        public double RegularHours { get; set; }

        /// <summary>
        /// Overtime hours
        /// </summary>
        public double OvertimeHours { get; set; }

        /// <summary>
        /// Base salary amount
        /// </summary>
        public decimal BaseAmount { get; set; }

        /// <summary>
        /// Overtime pay amount
        /// </summary>
        public decimal OvertimeAmount { get; set; }

        /// <summary>
        /// Bonus amount
        /// </summary>
        public decimal BonusAmount { get; set; } = 0;

        /// <summary>
        /// Deduction amount
        /// </summary>
        public decimal DeductionAmount { get; set; } = 0;

        /// <summary>
        /// Total amount
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Payment status
        /// </summary>
        [Indexed]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        /// <summary>
        /// Expected payment date
        /// </summary>
        public DateTime? ExpectedPaymentDate { get; set; }

        /// <summary>
        /// Actual payment date
        /// </summary>
        public DateTime? ActualPaymentDate { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        [SQLite.MaxLength(50)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        [SQLite.MaxLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Whether payment is overdue
        /// </summary>
        [Ignore]
        public bool IsOverdue
        {
            get
            {
                if (!ExpectedPaymentDate.HasValue || Status == PaymentStatus.Paid)
                    return false;
                return DateTime.Now > ExpectedPaymentDate.Value;
            }
        }

        /// <summary>
        /// Number of overdue days
        /// </summary>
        [Ignore]
        public int OverdueDays
        {
            get
            {
                if (!IsOverdue) return 0;
                return (DateTime.Now - ExpectedPaymentDate!.Value).Days;
            }
        }
    }
}
