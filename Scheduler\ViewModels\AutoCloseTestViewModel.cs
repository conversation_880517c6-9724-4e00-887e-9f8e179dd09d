// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using Scheduler.Services;                     // 应用服务 / Application services

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 自动关闭提示测试视图模型 / Auto Close Alert Test ViewModel
    /// 用于测试自动关闭提示功能
    /// Used for testing auto-close alert functionality
    /// </summary>
    public partial class AutoCloseTestViewModel : BaseViewModel
    {
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化自动关闭测试视图模型 / Initialize auto close test view model
        /// </summary>
        /// <param name="autoCloseAlertService">自动关闭提示服务（可选） / Auto-close alert service (optional)</param>
        /// <param name="localizationService">本地化服务（可选） / Localization service (optional)</param>
        public AutoCloseTestViewModel(IAutoCloseAlertService? autoCloseAlertService = null, LocalizationService? localizationService = null) : base(autoCloseAlertService)
        {
            _localizationService = localizationService ?? DependencyService.Get<LocalizationService>();
        }

        /// <summary>
        /// 本地化文本属性 / Localized texts property
        /// 提供界面显示的本地化文本 / Provides localized texts for UI display
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);

        /// <summary>
        /// 测试成功提示命令
        /// </summary>
        [RelayCommand]
        private async Task TestSuccess()
        {
            await ShowSuccessAsync("这是一个成功提示，将在5秒后自动关闭！", "操作成功");
        }

        /// <summary>
        /// 测试错误提示命令
        /// </summary>
        [RelayCommand]
        private async Task TestError()
        {
            var errorTestMessage = _localizationService.GetLocalizedString("ErrorTestMessage");
            var operationFailedTitle = _localizationService.GetLocalizedString("OperationFailedTitle");
            await ShowErrorAsync(errorTestMessage, operationFailedTitle);
        }

        /// <summary>
        /// 测试信息提示命令
        /// </summary>
        [RelayCommand]
        private async Task TestInfo()
        {
            var infoTestMessage = _localizationService.GetLocalizedString("InfoTestMessage");
            var infoTitle = _localizationService.GetLocalizedString("InfoTitle");
            var okText = _localizationService.GetLocalizedString("OK");

            if (_autoCloseAlertService != null)
            {
                await _autoCloseAlertService.ShowInfoAsync(infoTestMessage, infoTitle);
            }
            else
            {
                await Application.Current!.MainPage!.DisplayAlert(infoTitle, infoTestMessage, okText);
            }
        }

        /// <summary>
        /// 测试警告提示命令
        /// </summary>
        [RelayCommand]
        private async Task TestWarning()
        {
            var warningTestMessage = _localizationService.GetLocalizedString("WarningTestMessage");
            var warningTitle = _localizationService.GetLocalizedString("WarningTitle");
            var okText = _localizationService.GetLocalizedString("OK");

            if (_autoCloseAlertService != null)
            {
                await _autoCloseAlertService.ShowWarningAsync(warningTestMessage, warningTitle);
            }
            else
            {
                await Application.Current!.MainPage!.DisplayAlert(warningTitle, warningTestMessage, okText);
            }
        }
    }
}
