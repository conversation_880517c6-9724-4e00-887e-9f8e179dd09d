// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.ViewModels;
using Scheduler.Extensions;
using Scheduler.Services;

namespace Scheduler.Views;

/// <summary>
/// 登录页面 / Login Page
/// 用户身份验证界面，应用程序的入口点
/// User authentication interface, entry point of the application
/// </summary>
public partial class LoginView : ContentPage
{
    // 键盘服务实例 / Keyboard service instance
    private readonly IKeyboardService _keyboardService;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化登录页面并配置服务 / Initialize login page and configure services
    /// </summary>
    /// <param name="viewModel">登录视图模型 / Login view model</param>
    /// <param name="keyboardService">键盘服务 / Keyboard service</param>
    public LoginView(LoginViewModel viewModel, IKeyboardService keyboardService)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        BindingContext = viewModel;
        _keyboardService = keyboardService;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 执行登录页面的初始化逻辑 / Execute login page initialization logic
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is LoginViewModel viewModel)
        {
            // 清空密码字段（安全考虑） / Clear password field (security consideration)
            viewModel.Password = "";

            // 延迟聚焦用户名框以触发键盘 / Delay focus on username field to trigger keyboard
            await Task.Delay(300);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    // 主动聚焦用户名输入框 / Focus on username input field
                    UsernameEntry?.Focus();

                    // 强制显示软键盘（Android优化） / Force show soft keyboard (Android optimization)
                    _keyboardService?.ForceShowKeyboard();
                    System.Diagnostics.Debug.WriteLine("LoginView: 已聚焦用户名输入框并强制显示键盘 / Focused username field and forced keyboard display");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"LoginView: 聚焦用户名输入框失败 / Failed to focus username field - {ex.Message}");
                }
            });
        }
    }

    /// <summary>
    /// 页面消失时的处理 / Handle page disappearing
    /// 清理敏感信息和资源 / Clean up sensitive information and resources
    /// </summary>
    protected override void OnDisappearing()
    {
        base.OnDisappearing();

        if (BindingContext is LoginViewModel viewModel)
        {
            // 页面消失时清空敏感信息 / Clear sensitive information when page disappears
            viewModel.Password = "";
        }
    }

    /// <summary>
    /// 用户名输入框获得焦点时的处理 / Handle username entry focused
    /// 确保键盘正确显示 / Ensure keyboard displays correctly
    /// </summary>
    private void OnUsernameEntryFocused(object sender, FocusEventArgs e)
    {
        if (e.IsFocused)
        {
            try
            {
                // 强制显示软键盘 / Force show soft keyboard
                _keyboardService?.ForceShowKeyboard();
                System.Diagnostics.Debug.WriteLine("LoginView: 用户名输入框获得焦点，强制显示键盘 / Username field focused, forced keyboard display");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoginView: 用户名输入框焦点处理失败 / Username field focus handling failed - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 密码输入框获得焦点时的处理 / Handle password entry focused
    /// 确保键盘正确显示 / Ensure keyboard displays correctly
    /// </summary>
    private void OnPasswordEntryFocused(object sender, FocusEventArgs e)
    {
        if (e.IsFocused)
        {
            try
            {
                // 强制显示软键盘 / Force show soft keyboard
                _keyboardService?.ForceShowKeyboard();
                System.Diagnostics.Debug.WriteLine("LoginView: 密码输入框获得焦点，强制显示键盘 / Password field focused, forced keyboard display");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoginView: 密码输入框焦点处理失败 / Password field focus handling failed - {ex.Message}");
            }
        }
    }
}
