// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 支付待办事项视图模型 / Payment Todo ViewModel
    /// 专门管理支付相关的待办任务和确认流程
    /// Specifically manages payment-related todo tasks and confirmation processes
    /// </summary>
    public partial class PaymentTodoViewModel : BaseViewModel, IRecipient<DataResetMessage>, IRecipient<PaymentRecordUpdatedMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 待办事项服务实例 / Todo service instance
        private readonly TodoService _todoService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化支付待办事项视图模型 / Initialize payment todo view model
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="todoService">待办事项服务 / Todo service</param>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        public PaymentTodoViewModel(DatabaseService databaseService, TodoService todoService, LocalizationService localizationService)
        {
            _databaseService = databaseService;
            _todoService = todoService;
            _localizationService = localizationService;
            Title = "支付待办事项";

            // 初始化集合 / Initialize collections
            PendingPaymentTodos = new ObservableCollection<TodoItem>();
            CompletedPaymentTodos = new ObservableCollection<TodoItem>();
            OverduePaymentTodos = new ObservableCollection<TodoItem>();

            // 注册消息监听 / Register message listeners
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
            WeakReferenceMessenger.Default.Register<PaymentRecordUpdatedMessage>(this);
        }

        #region Observable Properties

        /// <summary>
        /// 未完成的支付待办事项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TodoItem> pendingPaymentTodos;

        /// <summary>
        /// 已完成的支付待办事项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TodoItem> completedPaymentTodos;

        /// <summary>
        /// 逾期的支付待办事项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TodoItem> overduePaymentTodos;

        /// <summary>
        /// 未完成数量
        /// </summary>
        [ObservableProperty]
        private int pendingCount;

        /// <summary>
        /// 已完成数量
        /// </summary>
        [ObservableProperty]
        private int completedCount;

        /// <summary>
        /// 逾期数量
        /// </summary>
        [ObservableProperty]
        private int overdueCount;

        /// <summary>
        /// 总待支付金额
        /// </summary>
        [ObservableProperty]
        private decimal totalPendingAmount;

        /// <summary>
        /// 总已确认金额
        /// </summary>
        [ObservableProperty]
        private decimal totalConfirmedAmount;

        /// <summary>
        /// 是否显示已完成的项目
        /// </summary>
        [ObservableProperty]
        private bool showCompletedItems = true;

        #endregion

        #region Commands

        /// <summary>
        /// 页面加载命令
        /// </summary>
        [RelayCommand]
        private async Task LoadDataAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine("PaymentTodoViewModel: 开始加载支付待办事项数据");

                // 自动创建支付待办事项
                await AutoCreatePaymentTodosAsync();

                // 加载各类支付待办事项
                await LoadPendingPaymentTodosAsync();
                await LoadCompletedPaymentTodosAsync();
                await LoadOverduePaymentTodosAsync();

                // 计算统计信息
                await CalculateStatisticsAsync();

                System.Diagnostics.Debug.WriteLine("PaymentTodoViewModel: 支付待办事项数据加载完成");
            });
        }

        /// <summary>
        /// 确认支付待办事项
        /// </summary>
        [RelayCommand]
        private async Task ConfirmPaymentTodoAsync(TodoItem todoItem)
        {
            if (todoItem == null || todoItem.RelatedEntityId == null) return;

            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 确认支付待办事项 - {todoItem.Title}");

                // 获取关联的支付记录
                var paymentRecord = await _databaseService.GetPaymentRecordAsync(todoItem.RelatedEntityId.Value);
                if (paymentRecord != null)
                {
                    // 确认支付记录
                    await _databaseService.ConfirmPaymentAsync(paymentRecord);

                    // 标记待办事项为完成
                    await _todoService.ToggleTodoItemCompletionAsync(todoItem);

                    // 重新加载数据
                    await LoadDataAsync();

                    // 发送支付记录更新消息
                    WeakReferenceMessenger.Default.Send(new PaymentRecordUpdatedMessage
                    {
                        PaymentRecordId = paymentRecord.Id,
                        Action = "Confirmed",
                        EmployerId = paymentRecord.EmployerId,
                        Amount = paymentRecord.TotalAmount,
                        UpdateTime = DateTime.Now
                    });

                    await ShowSuccessAsync($"支付已确认：${paymentRecord.TotalAmount:F2}");
                    System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 支付已确认 - {todoItem.Title}");
                }
                else
                {
                    await ShowErrorAsync("找不到关联的支付记录");
                }
            });
        }

        /// <summary>
        /// 取消支付确认
        /// </summary>
        [RelayCommand]
        private async Task CancelPaymentConfirmationAsync(TodoItem todoItem)
        {
            if (todoItem == null || todoItem.RelatedEntityId == null) return;

            var confirmed = await Application.Current!.MainPage!.DisplayAlert(
                "取消确认",
                $"确定要取消支付确认吗？",
                "确定",
                "取消");

            if (!confirmed) return;

            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 取消支付确认 - {todoItem.Title}");

                // 获取关联的支付记录
                var paymentRecord = await _databaseService.GetPaymentRecordAsync(todoItem.RelatedEntityId.Value);
                if (paymentRecord != null)
                {
                    // 取消支付确认
                    paymentRecord.Status = PaymentStatus.Pending;
                    paymentRecord.ActualPaymentDate = null;
                    await _databaseService.UpdatePaymentRecordAsync(paymentRecord);

                    // 标记待办事项为未完成
                    await _todoService.ToggleTodoItemCompletionAsync(todoItem);

                    // 重新加载数据
                    await LoadDataAsync();

                    // 发送支付记录更新消息
                    WeakReferenceMessenger.Default.Send(new PaymentRecordUpdatedMessage
                    {
                        PaymentRecordId = paymentRecord.Id,
                        Action = "Cancelled",
                        EmployerId = paymentRecord.EmployerId,
                        Amount = paymentRecord.TotalAmount,
                        UpdateTime = DateTime.Now
                    });

                    await ShowSuccessAsync("支付确认已取消");
                    System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 支付确认已取消 - {todoItem.Title}");
                }
                else
                {
                    await ShowErrorAsync("找不到关联的支付记录");
                }
            });
        }

        /// <summary>
        /// 查看支付详情
        /// </summary>
        [RelayCommand]
        private async Task ViewPaymentDetailsAsync(TodoItem todoItem)
        {
            if (todoItem == null || todoItem.RelatedEntityId == null) return;

            await SafeExecuteAsync(async () =>
            {
                var paymentRecord = await _databaseService.GetPaymentRecordAsync(todoItem.RelatedEntityId.Value);
                if (paymentRecord != null)
                {
                    var employer = await _databaseService.GetEmployerAsync(paymentRecord.EmployerId);
                    var employerName = employer?.Name ?? "未知雇主";

                    var details = $"雇主：{employerName}\n" +
                                 $"工作期间：{paymentRecord.PeriodStart:yyyy/MM/dd} - {paymentRecord.PeriodEnd:yyyy/MM/dd}\n" +
                                 $"工作时长：{paymentRecord.TotalHours:F1} 小时\n" +
                                 $"基础工资：${paymentRecord.BaseAmount:F2}\n" +
                                 $"加班费：${paymentRecord.OvertimeAmount:F2}\n" +
                                 $"总金额：${paymentRecord.TotalAmount:F2}\n" +
                                 $"状态：{(paymentRecord.Status == PaymentStatus.Paid ? "已支付" : "待支付")}\n" +
                                 $"预期支付日期：{paymentRecord.ExpectedPaymentDate?.ToString("yyyy/MM/dd") ?? "未设定"}";

                    if (paymentRecord.ActualPaymentDate.HasValue)
                    {
                        details += $"\n实际支付日期：{paymentRecord.ActualPaymentDate.Value:yyyy/MM/dd}";
                    }

                    await Application.Current!.MainPage!.DisplayAlert("支付详情", details, "确定");
                }
                else
                {
                    await ShowErrorAsync("找不到支付记录详情");
                }
            });
        }

        /// <summary>
        /// 切换显示已完成项目
        /// </summary>
        [RelayCommand]
        private async Task ToggleShowCompletedAsync()
        {
            ShowCompletedItems = !ShowCompletedItems;
            if (ShowCompletedItems)
            {
                await LoadCompletedPaymentTodosAsync();
            }
            else
            {
                CompletedPaymentTodos.Clear();
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 自动创建支付待办事项
        /// </summary>
        private async Task AutoCreatePaymentTodosAsync()
        {
            try
            {
                var createdCount = await _todoService.AutoCreatePaymentTodoItemsAsync();
                if (createdCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 自动创建了 {createdCount} 个支付待办事项");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 自动创建支付待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载未完成的支付待办事项
        /// </summary>
        private async Task LoadPendingPaymentTodosAsync()
        {
            try
            {
                var paymentTodos = await _todoService.GetPaymentTodoItemsAsync();
                var pendingTodos = paymentTodos.Where(t => !t.IsCompleted && !t.IsOverdue).ToList();

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    PendingPaymentTodos.Clear();
                    foreach (var todo in pendingTodos.OrderBy(t => t.DueDate))
                    {
                        PendingPaymentTodos.Add(todo);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载了 {pendingTodos.Count} 个未完成支付待办事项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载未完成支付待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载已完成的支付待办事项
        /// </summary>
        private async Task LoadCompletedPaymentTodosAsync()
        {
            try
            {
                if (!ShowCompletedItems)
                {
                    MainThread.BeginInvokeOnMainThread(() => CompletedPaymentTodos.Clear());
                    return;
                }

                var paymentTodos = await _todoService.GetPaymentTodoItemsAsync();
                var completedTodos = paymentTodos.Where(t => t.IsCompleted).Take(10).ToList();

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    CompletedPaymentTodos.Clear();
                    foreach (var todo in completedTodos.OrderByDescending(t => t.CompletedDate))
                    {
                        CompletedPaymentTodos.Add(todo);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载了 {completedTodos.Count} 个已完成支付待办事项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载已完成支付待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载逾期的支付待办事项
        /// </summary>
        private async Task LoadOverduePaymentTodosAsync()
        {
            try
            {
                var paymentTodos = await _todoService.GetPaymentTodoItemsAsync();
                var overdueTodos = paymentTodos.Where(t => !t.IsCompleted && t.IsOverdue).ToList();

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    OverduePaymentTodos.Clear();
                    foreach (var todo in overdueTodos.OrderBy(t => t.DueDate))
                    {
                        OverduePaymentTodos.Add(todo);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载了 {overdueTodos.Count} 个逾期支付待办事项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 加载逾期支付待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 计算统计信息
        /// </summary>
        private async Task CalculateStatisticsAsync()
        {
            try
            {
                PendingCount = PendingPaymentTodos.Count;
                CompletedCount = CompletedPaymentTodos.Count;
                OverdueCount = OverduePaymentTodos.Count;

                // 计算待支付总金额
                TotalPendingAmount = 0;
                foreach (var todo in PendingPaymentTodos.Concat(OverduePaymentTodos))
                {
                    if (todo.RelatedEntityId.HasValue)
                    {
                        var payment = await _databaseService.GetPaymentRecordAsync(todo.RelatedEntityId.Value);
                        if (payment != null)
                        {
                            TotalPendingAmount += payment.TotalAmount;
                        }
                    }
                }

                // 计算已确认总金额
                TotalConfirmedAmount = await _databaseService.GetTotalConfirmedIncomeAsync();

                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 统计信息 - 未完成:{PendingCount}, 已完成:{CompletedCount}, 逾期:{OverdueCount}, 待支付:${TotalPendingAmount:F2}, 已确认:${TotalConfirmedAmount:F2}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 计算统计信息失败 - {ex.Message}");
            }
        }

        #endregion

        #region Message Handlers

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 收到数据重置消息 - {message.ResetTime}");

                // 清空所有数据
                PendingPaymentTodos.Clear();
                CompletedPaymentTodos.Clear();
                OverduePaymentTodos.Clear();

                // 重置统计数据
                PendingCount = 0;
                CompletedCount = 0;
                OverdueCount = 0;
                TotalPendingAmount = 0;
                TotalConfirmedAmount = 0;

                // 重新加载数据（应该为空）
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine("PaymentTodoViewModel: 数据重置完成");
            });
        }

        /// <summary>
        /// 接收支付记录更新消息
        /// </summary>
        public void Receive(PaymentRecordUpdatedMessage message)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PaymentTodoViewModel: 收到支付记录更新消息 - {message.PaymentRecordId}, 操作: {message.Action}");

                // 重新加载数据以反映支付状态变化
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine("PaymentTodoViewModel: 支付记录更新处理完成");
            });
        }

        #endregion

        #region Localization
        /// <summary>
        /// 本地化文本
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);
        #endregion
    }
}
