// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Models;         // 数据模型 / Data models
using Plugin.LocalNotification; // 本地通知插件 / Local notification plugin

namespace Scheduler.Services
{
    /// <summary>
    /// 通知测试服务接口 / Notification Test Service Interface
    /// 定义通知功能测试的标准契约
    /// Defines standard contract for notification functionality testing
    /// </summary>
    public interface INotificationTestService
    {
        /// <summary>测试基本通知功能 / Test basic notification functionality</summary>
        Task TestBasicNotificationAsync();
        /// <summary>测试班次提醒 / Test shift reminder</summary>
        Task TestShiftReminderAsync();
        /// <summary>测试每日日程通知 / Test daily schedule notification</summary>
        Task TestDailyScheduleNotificationAsync();
        /// <summary>测试支付提醒 / Test payment reminder</summary>
        Task TestPaymentReminderAsync();
        /// <summary>测试通知权限 / Test notification permissions</summary>
        Task TestNotificationPermissionsAsync();
        /// <summary>测试通知调度 / Test notification scheduling</summary>
        Task TestNotificationSchedulingAsync();
        /// <summary>运行所有测试 / Run all tests</summary>
        Task RunAllTestsAsync();
    }

    /// <summary>
    /// 通知测试服务 / Notification Test Service
    /// 负责测试各种通知功能的正确性和可靠性
    /// Responsible for testing correctness and reliability of various notification functions
    /// </summary>
    public class NotificationTestService : INotificationTestService
    {
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;
        // 通知设置服务实例 / Notification settings service instance
        private readonly INotificationSettingsService _notificationSettingsService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 权限服务实例 / Permission service instance
        private readonly PermissionService _permissionService;
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化通知测试服务 / Initialize notification test service
        /// </summary>
        /// <param name="notificationService">通知服务 / Notification service</param>
        /// <param name="notificationSettingsService">通知设置服务 / Notification settings service</param>
        /// <param name="notificationScheduler">通知调度器 / Notification scheduler</param>
        /// <param name="permissionService">权限服务 / Permission service</param>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public NotificationTestService(
            INotificationService notificationService,
            INotificationSettingsService notificationSettingsService,
            NotificationScheduler notificationScheduler,
            PermissionService permissionService,
            DatabaseService databaseService)
        {
            _notificationService = notificationService;
            _notificationSettingsService = notificationSettingsService;
            _notificationScheduler = notificationScheduler;
            _permissionService = permissionService;
            _databaseService = databaseService;
        }

        /// <summary>
        /// 测试基本通知功能 / Test basic notification functionality
        /// 验证通知系统的基本发送和显示功能
        /// Verify basic sending and display functionality of notification system
        /// </summary>
        public async Task TestBasicNotificationAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试基本通知功能...");

                // 检查权限
                var hasPermission = await _permissionService.EnsureNotificationPermissionAsync();
                if (!hasPermission)
                {
                    System.Diagnostics.Debug.WriteLine("❌ 通知权限测试失败：权限被拒绝");
                    return;
                }

                // 发送测试通知
                var notification = new NotificationRequest
                {
                    NotificationId = 999999,
                    Title = "测试通知",
                    Description = "这是一个测试通知，用于验证通知功能是否正常工作。",
                    Schedule = new NotificationRequestSchedule
                    {
                        NotifyTime = DateTime.Now.AddSeconds(5)
                    }
                };
                await LocalNotificationCenter.Current.Show(notification);

                System.Diagnostics.Debug.WriteLine("✅ 基本通知功能测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 基本通知功能测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试班次提醒
        /// </summary>
        public async Task TestShiftReminderAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试班次提醒...");

                // 创建测试班次
                var testShift = new Shift
                {
                    Id = 999999, // 使用特殊ID避免与真实数据冲突
                    EmployerId = 1,
                    StartTime = DateTime.Now.AddMinutes(2),
                    EndTime = DateTime.Now.AddHours(2),
                    HourlyRate = 25.0m,
                    Status = ShiftStatus.Scheduled,
                    Description = "测试班次",
                    Location = "测试地点",
                    Notes = "这是一个用于测试通知的班次"
                };

                // 安排班次提醒
                await _notificationScheduler.ScheduleShiftNotificationsAsync(testShift);

                System.Diagnostics.Debug.WriteLine("✅ 班次提醒测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 班次提醒测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试每日工作安排通知
        /// </summary>
        public async Task TestDailyScheduleNotificationAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试每日工作安排通知...");

                // 安排明天的每日通知
                var tomorrow = DateTime.Today.AddDays(1);
                await _notificationService.ScheduleDailyWorkScheduleNotificationAsync(tomorrow);

                System.Diagnostics.Debug.WriteLine("✅ 每日工作安排通知测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 每日工作安排通知测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试薪资提醒
        /// </summary>
        public async Task TestPaymentReminderAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试薪资提醒...");

                // 安排薪资提醒
                await _notificationService.SchedulePaymentRemindersAsync();

                System.Diagnostics.Debug.WriteLine("✅ 薪资提醒测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 薪资提醒测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试通知权限
        /// </summary>
        public async Task TestNotificationPermissionsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试通知权限...");

                // 检查通知权限
                var notificationStatus = await _permissionService.CheckNotificationPermissionAsync();
                System.Diagnostics.Debug.WriteLine($"通知权限状态: {notificationStatus}");

                // 检查位置权限
                var locationStatus = await _permissionService.CheckLocationPermissionAsync();
                System.Diagnostics.Debug.WriteLine($"位置权限状态: {locationStatus}");

                // 测试权限请求
                if (notificationStatus != PermissionStatus.Granted)
                {
                    var granted = await _permissionService.EnsureNotificationPermissionAsync();
                    System.Diagnostics.Debug.WriteLine($"权限请求结果: {granted}");
                }

                System.Diagnostics.Debug.WriteLine("✅ 通知权限测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 通知权限测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试通知调度
        /// </summary>
        public async Task TestNotificationSchedulingAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试通知调度...");

                // 测试通知设置
                var settings = await _notificationSettingsService.GetSettingsAsync();
                System.Diagnostics.Debug.WriteLine($"通知设置 - 班次提醒: {settings.IsShiftReminderEnabled}, 每日安排: {settings.IsDailyScheduleEnabled}");

                // 测试调度器初始化
                await _notificationScheduler.InitializeAsync();

                // 测试清理过期通知
                await _notificationScheduler.CleanupExpiredNotificationsAsync();

                System.Diagnostics.Debug.WriteLine("✅ 通知调度测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 通知调度测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 开始运行所有通知功能测试...");
                System.Diagnostics.Debug.WriteLine(new string('=', 50));

                await TestNotificationPermissionsAsync();
                await Task.Delay(1000);

                await TestBasicNotificationAsync();
                await Task.Delay(1000);

                await TestNotificationSchedulingAsync();
                await Task.Delay(1000);

                await TestShiftReminderAsync();
                await Task.Delay(1000);

                await TestDailyScheduleNotificationAsync();
                await Task.Delay(1000);

                await TestPaymentReminderAsync();

                System.Diagnostics.Debug.WriteLine(new string('=', 50));
                System.Diagnostics.Debug.WriteLine("🎉 所有通知功能测试完成！");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 测试运行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        public async Task<string> GenerateTestReportAsync()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("通知功能测试报告");
            report.AppendLine(new string('=', 30));
            report.AppendLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            try
            {
                // 权限状态
                var notificationStatus = await _permissionService.CheckNotificationPermissionAsync();
                var locationStatus = await _permissionService.CheckLocationPermissionAsync();
                
                report.AppendLine("权限状态:");
                report.AppendLine($"  通知权限: {notificationStatus}");
                report.AppendLine($"  位置权限: {locationStatus}");
                report.AppendLine();

                // 通知设置
                var settings = await _notificationSettingsService.GetSettingsAsync();
                report.AppendLine("通知设置:");
                report.AppendLine($"  班次提醒: {(settings.IsShiftReminderEnabled ? "启用" : "禁用")}");
                report.AppendLine($"  每日安排: {(settings.IsDailyScheduleEnabled ? "启用" : "禁用")}");
                report.AppendLine($"  薪资提醒: {(settings.IsPaymentReminderEnabled ? "启用" : "禁用")}");
                report.AppendLine($"  声音提醒: {(settings.IsSoundEnabled ? "启用" : "禁用")}");
                report.AppendLine($"  振动提醒: {(settings.IsVibrationEnabled ? "启用" : "禁用")}");
                report.AppendLine();

                // 数据统计
                var shifts = await _databaseService.GetShiftsAsync(DateTime.Today, DateTime.Today.AddDays(7));
                report.AppendLine("数据统计:");
                report.AppendLine($"  未来7天班次数量: {shifts.Count}");
                report.AppendLine($"  已安排班次: {shifts.Count(s => s.Status == ShiftStatus.Scheduled)}");
                report.AppendLine();

                report.AppendLine("测试结果: ✅ 通过");
            }
            catch (Exception ex)
            {
                report.AppendLine($"测试结果: ❌ 失败 - {ex.Message}");
            }

            return report.ToString();
        }
    }
}
