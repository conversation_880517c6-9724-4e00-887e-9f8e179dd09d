<?xml version="1.0" encoding="utf-8" ?>
<!--
    班次列表视图 / Shift List View
    显示所有工作班次的列表，支持搜索、筛选和管理功能
    Display list of all work shifts with search, filter and management functions
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Scheduler.Views.ShiftListView"
             Title="All Shifts"
             Shell.FlyoutBehavior="Disabled"
             Shell.TabBarIsVisible="False">

    <!-- 返回按钮配置 / Back button configuration -->
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="True" IsVisible="True" />
    </Shell.BackButtonBehavior>

    <Grid RowDefinitions="Auto,*">

        <!-- 筛选和搜索栏 / Filter and search bar -->
        <Frame Grid.Row="0"
               BackgroundColor="{AppThemeBinding Light=#F8F9FA, Dark=#2D3748}"
               HasShadow="False"
               CornerRadius="0"
               Padding="16">
            <Grid ColumnDefinitions="*,Auto,Auto" ColumnSpacing="10">
                <!-- 搜索框 / Search box -->
                <SearchBar Grid.Column="0"
                          Placeholder="搜索班次..."
                          Text="{Binding SearchText}"
                          BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"/>

                <!-- 筛选按钮 / Filter button -->
                <Button Grid.Column="1"
                        Text="{Binding LocalizedTexts.Filter}"
                        BackgroundColor="#8B5CF6"
                        TextColor="White"
                        FontSize="14"
                        Command="{Binding ShowFilterCommand}"/>

                <!-- 添加按钮 / Add button -->
                <Button Grid.Column="2"
                        Text="{Binding LocalizedTexts.Add}"
                        BackgroundColor="#10B981"
                        TextColor="White"
                        FontSize="14"
                        Command="{Binding AddShiftCommand}"/>
            </Grid>
        </Frame>

        <!-- 班次列表 / Shift list -->
        <RefreshView Grid.Row="1"
                     IsRefreshing="{Binding IsRefreshing}"
                     Command="{Binding RefreshCommand}">
            
            <CollectionView ItemsSource="{Binding Shifts}"
                           SelectionMode="Single"
                           SelectedItem="{Binding SelectedShift}">
                
                <CollectionView.EmptyView>
                    <StackLayout Padding="50" HorizontalOptions="Center" VerticalOptions="Center">
                        <Label Text="📅" FontSize="48" HorizontalOptions="Center"/>
                        <Label Text="{Binding LocalizedTexts.NoShiftRecords}"
                               FontSize="18"
                               FontAttributes="Bold"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        <Label Text="{Binding LocalizedTexts.ClickAddToCreateFirstShift}"
                               FontSize="14"
                               HorizontalOptions="Center"
                               TextColor="{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}"/>
                        <Button Text="{Binding LocalizedTexts.AddNow}"
                                BackgroundColor="#3B82F6"
                                TextColor="White"
                                Command="{Binding AddShiftCommand}"
                                Margin="0,20,0,0"/>
                    </StackLayout>
                </CollectionView.EmptyView>

                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <!-- 修复：使用Grid替代Button，避免Content属性冲突 -->
                        <!-- Fix: Use Grid instead of Button to avoid Content property conflicts -->
                        <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto,Auto" ColumnSpacing="12">
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.EditShiftCommand}"
                                                      CommandParameter="{Binding .}"/>
                            </Grid.GestureRecognizers>
                                    <!-- 状态指示器 -->
                                    <BoxView Grid.Column="0"
                                             WidthRequest="4"
                                             HeightRequest="50"
                                             BackgroundColor="{Binding StatusColor}"
                                             VerticalOptions="Center"/>

                                    <!-- 班次信息 -->
                                    <VerticalStackLayout Grid.Column="1" Spacing="4">
                                        <Label Text="{Binding EmployerName}"
                                               FontAttributes="Bold"
                                               FontSize="16"
                                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                                        <Label FontSize="14" TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}">
                                            <Label.Text>
                                                <MultiBinding StringFormat="{}{0:MM/dd} {1:HH:mm} - {2:HH:mm}">
                                                    <Binding Path="StartTime"/>
                                                    <Binding Path="StartTime"/>
                                                    <Binding Path="EndTime"/>
                                                </MultiBinding>
                                            </Label.Text>
                                        </Label>

                                        <Label FontSize="12" TextColor="{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}">
                                            <Label.Text>
                                                <MultiBinding StringFormat="{}工时: {0:F1}h | 时薪: ¥{1:F2}">
                                                    <Binding Path="WorkHours"/>
                                                    <Binding Path="HourlyRate"/>
                                                </MultiBinding>
                                            </Label.Text>
                                        </Label>
                                    </VerticalStackLayout>

                                    <!-- 收入显示 -->
                                    <VerticalStackLayout Grid.Column="2" HorizontalOptions="End">
                                        <Label Text="{Binding EstimatedEarnings, StringFormat='¥{0:F2}'}"
                                               FontAttributes="Bold"
                                               FontSize="16"
                                               TextColor="#10B981"
                                               HorizontalOptions="End"/>
                                        <Label Text="{Binding StatusText}"
                                               FontSize="12"
                                               TextColor="{Binding StatusColor}"
                                               HorizontalOptions="End"/>
                                    </VerticalStackLayout>

                                    <!-- 操作按钮 -->
                                    <Button Grid.Column="3"
                                            Text="⋯"
                                            FontSize="18"
                                            BackgroundColor="Transparent"
                                            TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.ShowShiftOptionsCommand}"
                                            CommandParameter="{Binding .}"/>

                                    <!-- 分隔线 -->
                                    <BoxView Grid.ColumnSpan="4"
                                             HeightRequest="1"
                                             BackgroundColor="{AppThemeBinding Light=#E5E7EB, Dark=#4B5563}"
                                             VerticalOptions="End"
                                             HorizontalOptions="FillAndExpand"/>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>
    </Grid>

</ContentPage>
