// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 简化的班次管理视图模型 / Simple Shift Management ViewModel
    /// 提供简化的班次创建和管理功能
    /// Provides simplified shift creation and management functionality
    /// </summary>
    public partial class SimpleShiftViewModel : BaseViewModel
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知调度器实例 / Notification scheduler instance
        private readonly NotificationScheduler _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 雇主集合 / Employers collection
        [ObservableProperty]
        private ObservableCollection<Employer> employers = new();

        // 选中的雇主 / Selected employer
        [ObservableProperty]
        private Employer? selectedEmployer;

        // 时薪字符串 / Hourly rate string
        [ObservableProperty]
        private string hourlyRate = "0";

        // 开始日期 / Start date
        [ObservableProperty]
        private DateTime startDate = DateTime.Today;

        // 开始时间 / Start time
        [ObservableProperty]
        private TimeSpan startTime = new(9, 0, 0);

        // 结束日期 / End date
        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        // 结束时间 / End time
        [ObservableProperty]
        private TimeSpan endTime = new(17, 0, 0);

        // 工作小时数 / Work hours
        [ObservableProperty]
        private double workHours;

        // 预估收入 / Estimated earnings
        [ObservableProperty]
        private decimal estimatedEarnings;

        // 状态消息 / Status message
        [ObservableProperty]
        private string statusMessage = string.Empty;

        #region 通知设置属性 (Notification Settings Properties)

        [ObservableProperty]
        private bool isReminderEnabled = true;

        [ObservableProperty]
        private int selectedReminderMinutes = 15;

        [ObservableProperty]
        private ObservableCollection<ReminderTimeOption> reminderTimeOptions = new();

        [ObservableProperty]
        private ReminderTimeOption? selectedReminderTimeOption;

        #endregion

        #region 编辑模式属性 (Edit Mode Properties)

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private int? editingShiftId;

        [ObservableProperty]
        private string pageTitle = "Add New Shift";

        [ObservableProperty]
        private string saveButtonText = "Save Shift";

        #endregion

        public bool HasStatusMessage => !string.IsNullOrEmpty(StatusMessage);
        public bool IsEmployerSelected => SelectedEmployer != null;
        public bool CanSaveShift => SelectedEmployer != null && WorkHours > 0 && decimal.TryParse(HourlyRate, out var rate) && rate > 0;

        public SimpleShiftViewModel(DatabaseService databaseService, NotificationScheduler notificationScheduler, LocalizationService localizationService)
        {
            _databaseService = databaseService;
            _notificationScheduler = notificationScheduler;
            _localizationService = localizationService;
            Title = "Shift Management";

            // 初始化通知设置
            InitializeReminderTimeOptions();

            System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: 构造函数执行完成");

            // 计算初始值
            CalculateWorkHours();
        }

        /// <summary>
        /// 初始化提醒时间选项
        /// </summary>
        private void InitializeReminderTimeOptions()
        {
            ReminderTimeOptions.Clear();
            var options = ReminderTimeOption.GetDefaultOptions();

            foreach (var option in options)
            {
                ReminderTimeOptions.Add(option);
            }

            // 设置默认选择为15分钟前
            SelectedReminderTimeOption = ReminderTimeOptions.FirstOrDefault(x => x.Minutes == 15);
            if (SelectedReminderTimeOption != null)
            {
                SelectedReminderMinutes = SelectedReminderTimeOption.Minutes;
            }
        }

        /// <summary>
        /// 初始化ViewModel，支持编辑模式
        /// </summary>
        public async Task InitializeAsync(int? shiftId = null)
        {
            System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: 开始初始化");

            await LoadEmployersAsync();

            if (shiftId.HasValue && shiftId.Value > 0)
            {
                // 编辑模式
                await LoadShiftForEditingAsync(shiftId.Value);
            }
            else
            {
                // 新建模式
                SetupNewShiftMode();
            }

            System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: 初始化完成");
        }

        /// <summary>
        /// 设置新建班次模式
        /// </summary>
        private void SetupNewShiftMode()
        {
            IsEditMode = false;
            EditingShiftId = null;
            PageTitle = "Add New Shift";
            SaveButtonText = "Save Shift";

            // 重置为默认值
            IsReminderEnabled = true;
            SelectedReminderTimeOption = ReminderTimeOptions.FirstOrDefault(x => x.Minutes == 15);
            if (SelectedReminderTimeOption != null)
            {
                SelectedReminderMinutes = SelectedReminderTimeOption.Minutes;
            }
        }

        /// <summary>
        /// 加载班次数据用于编辑
        /// </summary>
        private async Task LoadShiftForEditingAsync(int shiftId)
        {
            try
            {
                var shift = await _databaseService.GetShiftAsync(shiftId);
                if (shift != null)
                {
                    IsEditMode = true;
                    EditingShiftId = shiftId;
                    PageTitle = "Edit Shift";
                    SaveButtonText = "Update Shift";

                    // 加载班次数据
                    SelectedEmployer = Employers.FirstOrDefault(e => e.Id == shift.EmployerId);
                    StartDate = shift.StartTime.Date;
                    StartTime = shift.StartTime.TimeOfDay;
                    EndDate = shift.EndTime.Date;
                    EndTime = shift.EndTime.TimeOfDay;
                    HourlyRate = shift.HourlyRate.ToString("F2");

                    // 加载通知设置
                    IsReminderEnabled = shift.IsReminderEnabled;
                    SelectedReminderMinutes = shift.ReminderMinutes;
                    SelectedReminderTimeOption = ReminderTimeOptions.FirstOrDefault(x => x.Minutes == shift.ReminderMinutes);

                    // 重新计算相关值
                    CalculateWorkHours();
                    CalculateEstimatedEarnings();
                }
            }
            catch (Exception ex)
            {
                var failedToLoadText = _localizationService.GetLocalizedString("FailedToLoadShiftData");
                await ShowErrorAsync($"{failedToLoadText}: {ex.Message}");
            }
        }

        private async Task LoadEmployersAsync()
        {
            try
            {
                var employerList = await _databaseService.GetEmployersAsync();
                Employers.Clear();
                foreach (var employer in employerList)
                {
                    Employers.Add(employer);
                }
            }
            catch (Exception ex)
            {
                var failedToLoadText = _localizationService.GetLocalizedString("FailedToLoadEmployers");
                await ShowErrorAsync($"{failedToLoadText}: {ex.Message}");
            }
        }

        partial void OnSelectedEmployerChanged(Employer? value)
        {
            if (value != null)
            {
                HourlyRate = value.HourlyRate.ToString("F2");
                CalculateEstimatedEarnings();
            }
            OnPropertyChanged(nameof(IsEmployerSelected));
            OnPropertyChanged(nameof(CanSaveShift));
        }

        partial void OnSelectedReminderTimeOptionChanged(ReminderTimeOption? value)
        {
            if (value != null)
            {
                SelectedReminderMinutes = value.Minutes;
            }
        }

        partial void OnIsReminderEnabledChanged(bool value)
        {
            System.Diagnostics.Debug.WriteLine($"班次提醒通知已{(value ? "启用" : "禁用")}");
        }

        partial void OnStartDateChanged(DateTime value)
        {
            if (EndDate < value)
            {
                EndDate = value;
            }
            CalculateWorkHours();
        }

        partial void OnStartTimeChanged(TimeSpan value)
        {
            CalculateWorkHours();
        }

        partial void OnEndDateChanged(DateTime value)
        {
            if (value < StartDate)
            {
                StartDate = value;
            }
            CalculateWorkHours();
        }

        partial void OnEndTimeChanged(TimeSpan value)
        {
            CalculateWorkHours();
        }

        partial void OnHourlyRateChanged(string value)
        {
            CalculateEstimatedEarnings();
            OnPropertyChanged(nameof(CanSaveShift));
        }

        private void CalculateWorkHours()
        {
            var startDateTime = StartDate.Add(StartTime);
            var endDateTime = EndDate.Add(EndTime);

            if (endDateTime > startDateTime)
            {
                WorkHours = (endDateTime - startDateTime).TotalHours;
            }
            else
            {
                WorkHours = 0;
            }

            CalculateEstimatedEarnings();
            OnPropertyChanged(nameof(CanSaveShift));
        }

        private void CalculateEstimatedEarnings()
        {
            if (decimal.TryParse(HourlyRate, out var rate))
            {
                EstimatedEarnings = (decimal)WorkHours * rate;
            }
            else
            {
                EstimatedEarnings = 0;
            }
        }

        [RelayCommand]
        private async Task AddEmployerAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("EmployerView");
            }
            catch (Exception ex)
            {
                var failedToNavigateText = _localizationService.GetLocalizedString("FailedToNavigateToEmployerPage");
                await ShowErrorAsync($"{failedToNavigateText}: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task ViewShiftsAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("ShiftListView");
            }
            catch (Exception ex)
            {
                var failedToNavigateText = _localizationService.GetLocalizedString("FailedToNavigateToShiftsPage");
                await ShowErrorAsync($"{failedToNavigateText}: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task SaveShiftAsync()
        {
            if (SelectedEmployer == null)
            {
                var pleaseSelectEmployerText = _localizationService.GetLocalizedString("PleaseSelectEmployer");
                await ShowErrorAsync(pleaseSelectEmployerText);
                return;
            }

            if (!decimal.TryParse(HourlyRate, out var rate) || rate <= 0)
            {
                var pleaseEnterValidHourlyRateText = _localizationService.GetLocalizedString("PleaseEnterValidHourlyRate");
                await ShowErrorAsync(pleaseEnterValidHourlyRateText);
                return;
            }

            if (WorkHours <= 0)
            {
                var pleaseSetValidWorkHoursText = _localizationService.GetLocalizedString("PleaseSetValidWorkHours");
                await ShowErrorAsync(pleaseSetValidWorkHoursText);
                return;
            }

            try
            {
                IsBusy = true;
                var savingShiftText = _localizationService.GetLocalizedString("SavingShift");
                StatusMessage = savingShiftText;
                OnPropertyChanged(nameof(HasStatusMessage));

                // 创建班次对象
                var startDateTime = StartDate.Add(StartTime);
                var endDateTime = EndDate.Add(EndTime);

                Shift shiftToSave;

                if (IsEditMode && EditingShiftId.HasValue)
                {
                    // 编辑模式：加载现有班次并更新
                    shiftToSave = await _databaseService.GetShiftAsync(EditingShiftId.Value);
                    if (shiftToSave == null)
                    {
                        await ShowErrorAsync("无法找到要编辑的班次");
                        return;
                    }

                    // 更新班次信息
                    shiftToSave.EmployerId = SelectedEmployer.Id;
                    shiftToSave.StartTime = startDateTime;
                    shiftToSave.EndTime = endDateTime;
                    shiftToSave.HourlyRate = rate;
                    shiftToSave.EstimatedHours = WorkHours;
                    shiftToSave.UpdatedAt = DateTime.Now;

                    // 更新通知设置
                    shiftToSave.IsReminderEnabled = IsReminderEnabled;
                    shiftToSave.ReminderMinutes = SelectedReminderMinutes;
                }
                else
                {
                    // 新建模式：创建新班次
                    shiftToSave = new Shift
                    {
                        EmployerId = SelectedEmployer.Id,
                        StartTime = startDateTime,
                        EndTime = endDateTime,
                        HourlyRate = rate,
                        Status = ShiftStatus.Scheduled,
                        Description = $"{SelectedEmployer.Name} - Work Shift",
                        Location = "Work Location",
                        EstimatedHours = WorkHours,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        // 设置通知相关属性
                        IsReminderEnabled = IsReminderEnabled,
                        ReminderMinutes = SelectedReminderMinutes
                    };
                }

                if (IsEditMode)
                {
                    // 编辑模式：直接保存更新
                    await _databaseService.SaveShiftAsync(shiftToSave);

                    // 取消旧通知并重新安排新通知
                    if (_notificationScheduler != null)
                    {
                        await _notificationScheduler.CancelShiftNotificationsAsync(shiftToSave.Id);

                        if (shiftToSave.IsReminderEnabled)
                        {
                            await _notificationScheduler.ScheduleShiftNotificationsAsync(shiftToSave);
                        }
                    }

                    StatusMessage = "班次更新成功！";
                    await ShowSuccessAsync("班次更新成功！");
                }
                else
                {
                    // 新建模式：检查冲突并保存
                    var (success, shiftId, errorMessage) = await _databaseService.SaveShiftWithConflictCheckAsync(shiftToSave);

                    if (!success)
                    {
                        await ShowErrorAsync($"Cannot create shift: {errorMessage}");
                        return;
                    }

                    shiftToSave.Id = shiftId;

                    // 只有在启用提醒时才安排通知
                    if (_notificationScheduler != null && shiftToSave.IsReminderEnabled)
                    {
                        await _notificationScheduler.ScheduleShiftNotificationsAsync(shiftToSave);
                    }

                    StatusMessage = "班次创建成功！";
                    await ShowSuccessAsync("班次创建成功！");
                }

                // 发送消息通知其他模块刷新数据
                WeakReferenceMessenger.Default.Send(new ShiftCreatedMessage(shiftToSave));

                OnPropertyChanged(nameof(HasStatusMessage));

                // 清空表单
                SelectedEmployer = null;
                HourlyRate = "0";
                StartDate = DateTime.Today;
                StartTime = new(9, 0, 0);
                EndDate = DateTime.Today;
                EndTime = new(17, 0, 0);
                CalculateWorkHours();
                CalculateEstimatedEarnings();

                await Task.Delay(1000);
                StatusMessage = string.Empty;
                OnPropertyChanged(nameof(HasStatusMessage));

                // 自动返回主页
                await GoBackAsync();
            }
            catch (Exception ex)
            {
                var saveFailedText = _localizationService.GetLocalizedString("SaveFailed");
                await ShowErrorAsync($"{saveFailedText}: {ex.Message}");
                StatusMessage = string.Empty;
                OnPropertyChanged(nameof(HasStatusMessage));
                System.Diagnostics.Debug.WriteLine($"保存班次失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private async Task CancelAsync()
        {
            System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: CancelAsync called");
            await GoBackAsync();
        }

        /// <summary>
        /// 返回上一页
        /// </summary>
        private async Task GoBackAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"SimpleShiftViewModel: GoBackAsync called, NavigationStack count: {Shell.Current.Navigation.NavigationStack.Count}");

                // 尝试多种导航方式确保能够返回
                if (Shell.Current.Navigation.NavigationStack.Count > 1)
                {
                    // 如果导航栈中有多个页面，使用PopAsync
                    System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: Using PopAsync");
                    await Shell.Current.Navigation.PopAsync();
                }
                else
                {
                    // 否则直接导航到Home页面
                    System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: Using GoToAsync to Main/Home");
                    await Shell.Current.GoToAsync("//Main/Home");
                }

                System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: Navigation successful");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SimpleShiftViewModel: Navigation failed: {ex.Message}");

                // 如果上述方法都失败，尝试直接导航到主页
                try
                {
                    System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: Trying fallback navigation");
                    await Shell.Current.GoToAsync("//Main/Home");
                    System.Diagnostics.Debug.WriteLine("SimpleShiftViewModel: Fallback navigation successful");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"SimpleShiftViewModel: Fallback navigation failed: {fallbackEx.Message}");
                    await ShowErrorAsync($"Navigation failed: {ex.Message}. Fallback also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// 本地化文本 - Localized texts for XAML binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);
    }
}
