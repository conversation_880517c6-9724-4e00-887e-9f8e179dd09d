using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DishMeAPP.Models
{
    /// <summary>
    /// 菜谱模型类 - 表示一个完整的菜谱信息
    /// Recipe Model Class - Represents complete recipe information
    /// 包含Entity Framework Core数据注解，支持SQLite数据库存储
    /// </summary>
    public class Recipe : INotifyPropertyChanged
    {
        /// <summary>
        /// 菜谱唯一标识符 - 主键，自动递增
        /// Unique identifier for the recipe - Primary key, auto-increment
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 菜谱名称 - 必填字段，最大长度200字符
        /// Recipe name - Required field, maximum 200 characters
        /// </summary>
        [Required(ErrorMessage = "菜谱名称不能为空")]
        [MaxLength(200, ErrorMessage = "菜谱名称不能超过200个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 制作步骤 - 长文本字段
        /// Cooking steps - Long text field
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string Steps { get; set; } = string.Empty;

        /// <summary>
        /// 食材清单 - 长文本字段
        /// Ingredients list - Long text field
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string Ingredients { get; set; } = string.Empty;

        /// <summary>
        /// 菜谱分类 - 最大长度100字符
        /// Recipe category - Maximum 100 characters
        /// </summary>
        [MaxLength(100)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 图片路径 - 最大长度500字符
        /// Image path - Maximum 500 characters
        /// </summary>
        [MaxLength(500)]
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间 - 自动设置
        /// Creation time - Automatically set
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间 - 自动更新
        /// Update time - Automatically updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 制作时间（分钟）- 可选字段
        /// Cooking time in minutes - Optional field
        /// </summary>
        public int? CookingTimeMinutes { get; set; }

        /// <summary>
        /// 难度等级（1-5）- 默认为1
        /// Difficulty level (1-5) - Default is 1
        /// </summary>
        [Range(1, 5, ErrorMessage = "难度等级必须在1-5之间")]
        public int DifficultyLevel { get; set; } = 1;

        /// <summary>
        /// 份数 - 默认为1
        /// Number of servings - Default is 1
        /// </summary>
        [Range(1, 20, ErrorMessage = "份数必须在1-20之间")]
        public int Servings { get; set; } = 1;

        /// <summary>
        /// 是否收藏 - 默认为false
        /// Is favorite - Default is false
        /// </summary>
        public bool IsFavorite { get; set; } = false;

        /// <summary>
        /// 标签 - 用逗号分隔的标签列表，最大长度500字符
        /// Tags - Comma-separated tag list, maximum 500 characters
        /// </summary>
        [MaxLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// 营养信息 - JSON格式存储，长文本字段
        /// Nutrition information - Stored in JSON format, long text field
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? NutritionInfo { get; set; }

        // 以下是原有的方法，保持不变以确保UI层兼容性
        // The following methods remain unchanged to ensure UI layer compatibility

        /// <summary>
        /// 转换为Dish对象（用于订单系统）
        /// Convert to Dish object (for order system)
        /// </summary>
        /// <returns>对应的Dish对象</returns>
        public Dish ToDish()
        {
            return new Dish
            {
                Id = Id,
                Name = Name,
                Category = GetDishCategory(),
                Image = GetDisplayImagePath(),
                Description = $"{Ingredients}\n\n{Steps}".Trim()
            };
        }

        /// <summary>
        /// 获取菜谱分类的显示名称
        /// Get display name for recipe category
        /// </summary>
        /// <returns>分类显示名称</returns>
        private string GetDishCategory()
        {
            return string.IsNullOrWhiteSpace(Category) ? "其他" : Category;
        }

        /// <summary>
        /// 获取显示用的图片路径
        /// Get image path for display
        /// </summary>
        /// <returns>图片路径</returns>
        private string GetDisplayImagePath()
        {
            // 如果没有图片，返回默认占位符
            // If no image, return default placeholder
            if (string.IsNullOrWhiteSpace(ImagePath))
            {
                return "recipe_placeholder.png"; // 需要添加默认图片
            }

            return ImagePath;
        }

        // INotifyPropertyChanged 实现
        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// Trigger property change notification
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 设置属性值并触发通知
        /// Set property value and trigger notification
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">字段引用</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否发生了变更</returns>
        protected bool SetProperty<T>(ref T field, T value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 重写ToString方法，用于调试
        /// Override ToString method for debugging
        /// </summary>
        /// <returns>对象的字符串表示</returns>
        public override string ToString()
        {
            return $"Recipe: {Name} (ID: {Id}, Category: {Category}, Created: {CreatedAt:yyyy-MM-dd})";
        }

        /// <summary>
        /// 重写Equals方法
        /// Override Equals method
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Recipe other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// 重写GetHashCode方法
        /// Override GetHashCode method
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        /// <summary>
        /// 验证菜谱数据的完整性
        /// Validate recipe data integrity
        /// </summary>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, List<string> Errors) Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("菜谱名称不能为空");

            if (Name.Length > 200)
                errors.Add("菜谱名称不能超过200个字符");

            if (DifficultyLevel < 1 || DifficultyLevel > 5)
                errors.Add("难度等级必须在1-5之间");

            if (Servings < 1 || Servings > 20)
                errors.Add("份数必须在1-20之间");

            if (CookingTimeMinutes.HasValue && CookingTimeMinutes.Value < 0)
                errors.Add("制作时间不能为负数");

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// 创建菜谱的副本
        /// Create a copy of the recipe
        /// </summary>
        /// <returns>菜谱副本</returns>
        public Recipe Clone()
        {
            return new Recipe
            {
                // 注意：不复制Id，让数据库自动生成新的ID
                // Note: Don't copy Id, let database auto-generate new ID
                Name = $"{Name} (副本)",
                Steps = Steps,
                Ingredients = Ingredients,
                Category = Category,
                ImagePath = ImagePath,
                CookingTimeMinutes = CookingTimeMinutes,
                DifficultyLevel = DifficultyLevel,
                Servings = Servings,
                IsFavorite = false, // 副本默认不收藏
                Tags = Tags,
                NutritionInfo = NutritionInfo,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
