// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 数据库测试视图 / Database Test View
/// 用于测试数据库连接和执行数据库诊断操作
/// Used for testing database connections and performing database diagnostic operations
/// </summary>
public partial class DatabaseTestView : ContentPage
{
    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化数据库测试视图并配置数据绑定 / Initialize database test view and configure data binding
    /// </summary>
    /// <param name="viewModel">数据库测试视图模型 / Database test view model</param>
    public DatabaseTestView(DatabaseTestViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        BindingContext = viewModel;
    }
}
