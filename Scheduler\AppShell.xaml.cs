﻿// 引入视图和服务命名空间 / Import Views and Services namespaces
using Scheduler.Views;
using Scheduler.Services;

namespace Scheduler
{
    /// <summary>
    /// 应用程序Shell后台代码 / Application Shell Code-behind
    /// 负责路由注册和应用程序初始化 / Responsible for route registration and application initialization
    /// </summary>
    public partial class AppShell : Shell
    {
        // 应用初始化服务实例 / Application initialization service instance
        private AppInitializationService? _appInitializationService;
        // 初始化状态标志 / Initialization status flag
        private bool _isInitialized = false;

        /// <summary>
        /// 构造函数 - 初始化Shell并注册路由 / Constructor - Initialize Shell and register routes
        /// </summary>
        public AppShell()
        {
            // 初始化XAML组件 / Initialize XAML components
            InitializeComponent();

            // ==================== 路由注册 / Route Registration ====================

            // 雇主管理页面路由 / Employer management page routes
            Routing.RegisterRoute("EmployerView", typeof(EmployerView));
            Routing.RegisterRoute("AllEmployer", typeof(AllEmployer));
            Routing.RegisterRoute("EditEmployer", typeof(EditEmployerView));

            // 班次管理页面路由 / Shift management page routes
            Routing.RegisterRoute("SimpleShift", typeof(SimpleShiftView));
            Routing.RegisterRoute("ShiftList", typeof(ShiftListView));
            Routing.RegisterRoute("ShiftManagement", typeof(ShiftManagementView));

            // 待办事项管理页面路由 / Todo management page routes
            Routing.RegisterRoute("TodoList", typeof(TodoListView));
            Routing.RegisterRoute("PaymentTodo", typeof(PaymentTodoView));

            // 支付记录管理页面路由 / Payment records management page routes
            Routing.RegisterRoute("AllPaymentRecords", typeof(AllPaymentRecordsView));

            // 通知管理页面路由 / Notification management page routes
            Routing.RegisterRoute("NotificationSettings", typeof(NotificationSettingsView));
            Routing.RegisterRoute("NotificationHistory", typeof(NotificationHistoryView));
            Routing.RegisterRoute("NotificationTest", typeof(NotificationTestView));

            // 开发调试页面路由 / Development debug page routes
            Routing.RegisterRoute("DatabaseTest", typeof(DatabaseTestView));

            System.Diagnostics.Debug.WriteLine("AppShell: 路由注册完成 / Route registration completed");
        }

        /// <summary>
        /// Shell出现时的处理 / Handle Shell appearing
        /// 执行应用程序初始化逻辑 / Execute application initialization logic
        /// </summary>
        protected override async void OnAppearing()
        {
            base.OnAppearing();

            // 防止重复初始化 / Prevent duplicate initialization
            if (_isInitialized)
                return;

            try
            {
                // 从依赖注入容器获取初始化服务 / Get initialization service from DI container
                _appInitializationService = Handler?.MauiContext?.Services?.GetService<AppInitializationService>();

                // 执行应用程序初始化 / Execute application initialization
                if (_appInitializationService != null && !_appInitializationService.IsInitialized)
                {
                    await _appInitializationService.InitializeAsync();
                }

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                // 记录初始化失败信息 / Log initialization failure
                System.Diagnostics.Debug.WriteLine($"应用初始化失败 / Application initialization failed: {ex.Message}");
                _isInitialized = true; // 避免重复尝试 / Avoid repeated attempts
            }
        }
    }
}
