using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Scheduler.Models;
using Scheduler.Services;
using System.Collections.ObjectModel;

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 雇主信息编辑视图模型 - 管理雇主信息的创建和编辑
    /// Employer information editing ViewModel - manages creation and editing of employer information
    /// </summary>
    /// <remarks>
    /// 该ViewModel负责处理雇主信息的所有业务逻辑，包括：
    /// - 雇主基本信息的数据绑定和验证
    /// - 新增和编辑模式的切换
    /// - 数据保存和删除操作
    /// - 多语言支持和界面本地化
    /// - 颜色选择和UI定制
    /// </remarks>
    public partial class EmployerViewModel : BaseViewModel, IRecipient<LanguageChangedMessage>
    {
        private readonly DatabaseService _databaseService;
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 雇主ID - 用于标识雇主的唯一标识符（0表示新建）
        /// </summary>
        [ObservableProperty]
        private int employerId;

        /// <summary>
        /// 雇主名称 - 雇主或公司的名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 联系信息 - 雇主的联系方式（电话、邮箱等）
        /// </summary>
        [ObservableProperty]
        private string contactInfo = string.Empty;

        /// <summary>
        /// 时薪 - 该雇主的每小时工资率
        /// </summary>
        [ObservableProperty]
        private decimal hourlyRate;

        /// <summary>
        /// 付款周期天数 - 雇主的工资发放周期（默认30天）
        /// </summary>
        [ObservableProperty]
        private int paymentCycleDays = 30;

        /// <summary>
        /// 标识颜色 - 用于在UI中区分不同雇主的颜色代码
        /// </summary>
        [ObservableProperty]
        private string color = "#007ACC";

        /// <summary>
        /// 工作地点 - 该雇主的工作场所地址
        /// </summary>
        [ObservableProperty]
        private string workLocation = string.Empty;

        /// <summary>
        /// 备注信息 - 关于雇主的额外说明和注意事项
        /// </summary>
        [ObservableProperty]
        private string notes = string.Empty;

        /// <summary>
        /// 是否活跃 - 标识雇主是否仍在合作中
        /// </summary>
        [ObservableProperty]
        private bool isActive = true;

        /// <summary>
        /// 是否编辑模式 - 标识当前是编辑现有雇主还是创建新雇主
        /// </summary>
        [ObservableProperty]
        private bool isEditMode;

        /// <summary>
        /// 页面标题 - 根据编辑模式动态显示的页面标题
        /// </summary>
        [ObservableProperty]
        private string pageTitle = string.Empty;

        [ObservableProperty]
        private string saveButtonText = string.Empty;

        // 本地化文本属性
        public string FillEmployerInfoText => _localizationService.GetLocalizedString("FillEmployerInfo");
        public string BasicInfoText => _localizationService.GetLocalizedString("BasicInfo");
        public string EmployerNameRequiredText => _localizationService.GetLocalizedString("EmployerNameRequired");
        public string EnterEmployerNameText => _localizationService.GetLocalizedString("EnterEmployerName");
        public string ContactInfoText => _localizationService.GetLocalizedString("ContactInfo");
        public string EnterContactInfoText => _localizationService.GetLocalizedString("EnterContactInfo");
        public string WorkLocationText => _localizationService.GetLocalizedString("WorkLocation");
        public string EnterWorkLocationText => _localizationService.GetLocalizedString("EnterWorkLocation");
        public string SalaryInfoText => _localizationService.GetLocalizedString("SalaryInfo");
        public string HourlyRateRequiredText => _localizationService.GetLocalizedString("HourlyRateRequired");

        // 预定义颜色选项
        public ObservableCollection<ColorOption> ColorOptions { get; } = new();

        public EmployerViewModel(DatabaseService databaseService, IAutoCloseAlertService? autoCloseAlertService = null) : base(autoCloseAlertService)
        {
            _databaseService = databaseService;
            _localizationService = LocalizationService.Instance;

            // 注册语言更改消息
            WeakReferenceMessenger.Default.Register(this);

            // 初始化颜色选项
            InitializeColorOptions();

            // 初始化本地化文本
            UpdateLocalizedTexts();
        }

        /// <summary>
        /// 初始化编辑模式
        /// </summary>
        public async Task InitializeAsync(int? employerId = null)
        {
            try
            {
                IsBusy = true;

                if (employerId.HasValue && employerId.Value > 0)
                {
                    // 编辑模式
                    IsEditMode = true;
                    PageTitle = _localizationService.GetLocalizedString("EditEmployer");
                    SaveButtonText = _localizationService.GetLocalizedString("Update");

                    var employer = await _databaseService.GetEmployerAsync(employerId.Value);
                    if (employer != null)
                    {
                        LoadEmployerData(employer);
                    }
                }
                else
                {
                    // 新增模式
                    IsEditMode = false;
                    PageTitle = _localizationService.GetLocalizedString("AddEmployer");
                    SaveButtonText = _localizationService.GetLocalizedString("Save");
                    ResetForm();
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync("初始化失败", ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 加载雇主数据到表单
        /// </summary>
        private void LoadEmployerData(Employer employer)
        {
            EmployerId = employer.Id;
            Name = employer.Name;
            ContactInfo = employer.ContactInfo ?? string.Empty;
            HourlyRate = employer.HourlyRate;
            PaymentCycleDays = employer.PaymentCycleDays;
            Color = employer.Color;
            WorkLocation = employer.WorkLocation ?? string.Empty;
            Notes = employer.Notes ?? string.Empty;
            IsActive = employer.IsActive;

            // 更新颜色选择状态
            UpdateSelectedColor(employer.Color);
        }

        /// <summary>
        /// 重置表单
        /// </summary>
        private void ResetForm()
        {
            EmployerId = 0;
            Name = string.Empty;
            ContactInfo = string.Empty;
            HourlyRate = 0;
            PaymentCycleDays = 30;
            Color = "#007ACC";
            WorkLocation = string.Empty;
            Notes = string.Empty;
            IsActive = true;

            // 更新颜色选择状态
            UpdateSelectedColor("#007ACC");
        }

        /// <summary>
        /// 保存雇主信息
        /// </summary>
        [RelayCommand]
        private async Task SaveAsync()
        {
            if (IsBusy) return;

            try
            {
                // 验证输入
                if (!ValidateInput())
                    return;

                IsBusy = true;

                var employer = new Employer
                {
                    Id = EmployerId,
                    Name = Name.Trim(),
                    ContactInfo = string.IsNullOrWhiteSpace(ContactInfo) ? null : ContactInfo.Trim(),
                    HourlyRate = HourlyRate,
                    PaymentCycleDays = PaymentCycleDays,
                    Color = Color,
                    WorkLocation = string.IsNullOrWhiteSpace(WorkLocation) ? null : WorkLocation.Trim(),
                    Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim(),
                    IsActive = IsActive
                };

                await _databaseService.SaveEmployerAsync(employer);

                // 发送雇主更新消息
                WeakReferenceMessenger.Default.Send(new EmployerUpdatedMessage());

                var successTitle = _localizationService.GetLocalizedString("SaveSuccess");
                var successMessage = IsEditMode
                    ? _localizationService.GetLocalizedString("EmployerInfoUpdated")
                    : _localizationService.GetLocalizedString("NewEmployerAdded");
                await ShowSuccessAsync(successTitle, successMessage);

                // 返回上一页
                await GoBackAsync();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync(_localizationService.GetLocalizedString("SaveFailed"), ex.Message);
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 验证输入数据
        /// </summary>
        private bool ValidateInput()
        {
            var validationFailedTitle = _localizationService.GetLocalizedString("ValidationFailed");

            if (string.IsNullOrWhiteSpace(Name))
            {
                ShowErrorAsync(validationFailedTitle, _localizationService.GetLocalizedString("PleaseEnterEmployerName")).ConfigureAwait(false);
                return false;
            }

            if (HourlyRate < 0)
            {
                ShowErrorAsync(validationFailedTitle, _localizationService.GetLocalizedString("PleaseEnterValidHourlyRate")).ConfigureAwait(false);
                return false;
            }

            if (PaymentCycleDays <= 0)
            {
                ShowErrorAsync(validationFailedTitle, _localizationService.GetLocalizedString("PleaseEnterValidPaymentCycle")).ConfigureAwait(false);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 取消编辑
        /// </summary>
        [RelayCommand]
        private async Task CancelAsync()
        {
            await GoBackAsync();
        }

        /// <summary>
        /// 选择颜色
        /// </summary>
        [RelayCommand]
        private void SelectColor(ColorOption selectedColorOption)
        {
            // 更新所有颜色选项的选中状态
            foreach (var option in ColorOptions)
            {
                option.IsSelected = option == selectedColorOption;
            }

            // 更新当前选中的颜色
            Color = selectedColorOption.HexValue;

            // 通知UI更新
            OnPropertyChanged(nameof(ColorOptions));
        }

        /// <summary>
        /// 查看所有雇主
        /// </summary>
        [RelayCommand]
        private async Task ViewAllEmployersAsync()
        {
            await Shell.Current.GoToAsync("AllEmployer");
        }

        /// <summary>
        /// 返回上一页
        /// </summary>
        private async Task GoBackAsync()
        {
            await Shell.Current.GoToAsync("..");
        }

        /// <summary>
        /// 更新本地化文本
        /// </summary>
        private void UpdateLocalizedTexts()
        {
            if (IsEditMode)
            {
                PageTitle = _localizationService.GetLocalizedString("EditEmployer");
                SaveButtonText = _localizationService.GetLocalizedString("Update");
            }
            else
            {
                PageTitle = _localizationService.GetLocalizedString("AddEmployer");
                SaveButtonText = _localizationService.GetLocalizedString("Save");
            }

            // 通知所有本地化文本属性更改
            OnPropertyChanged(nameof(FillEmployerInfoText));
            OnPropertyChanged(nameof(BasicInfoText));
            OnPropertyChanged(nameof(EmployerNameRequiredText));
            OnPropertyChanged(nameof(EnterEmployerNameText));
            OnPropertyChanged(nameof(ContactInfoText));
            OnPropertyChanged(nameof(EnterContactInfoText));
            OnPropertyChanged(nameof(WorkLocationText));
            OnPropertyChanged(nameof(EnterWorkLocationText));
            OnPropertyChanged(nameof(SalaryInfoText));
            OnPropertyChanged(nameof(HourlyRateRequiredText));
        }

        /// <summary>
        /// 初始化颜色选项
        /// </summary>
        private void InitializeColorOptions()
        {
            ColorOptions.Clear();

            // 精选10种视觉上有明显区别的颜色
            var colors = new[]
            {
                new ColorOption { HexValue = "#007ACC", Name = "专业蓝", EnglishName = "Professional Blue", Category = "商务" },
                new ColorOption { HexValue = "#28A745", Name = "成功绿", EnglishName = "Success Green", Category = "积极" },
                new ColorOption { HexValue = "#DC3545", Name = "警示红", EnglishName = "Alert Red", Category = "重要" },
                new ColorOption { HexValue = "#FFC107", Name = "阳光黄", EnglishName = "Sunshine Yellow", Category = "活力" },
                new ColorOption { HexValue = "#6F42C1", Name = "优雅紫", EnglishName = "Elegant Purple", Category = "创意" },
                new ColorOption { HexValue = "#FD7E14", Name = "活力橙", EnglishName = "Vibrant Orange", Category = "热情" },
                new ColorOption { HexValue = "#20C997", Name = "清新青", EnglishName = "Fresh Teal", Category = "清新" },
                new ColorOption { HexValue = "#E83E8C", Name = "玫瑰粉", EnglishName = "Rose Pink", Category = "温馨" },
                new ColorOption { HexValue = "#6C757D", Name = "中性灰", EnglishName = "Neutral Gray", Category = "稳重" },
                new ColorOption { HexValue = "#8B4513", Name = "咖啡棕", EnglishName = "Coffee Brown", Category = "经典" }
            };

            foreach (var color in colors)
            {
                ColorOptions.Add(color);
            }

            // 设置默认选中的颜色
            UpdateSelectedColor(Color);
        }

        /// <summary>
        /// 更新选中的颜色状态
        /// </summary>
        private void UpdateSelectedColor(string selectedColor)
        {
            foreach (var option in ColorOptions)
            {
                option.IsSelected = option.HexValue == selectedColor;
            }
        }

        /// <summary>
        /// 接收语言更改消息
        /// </summary>
        public void Receive(LanguageChangedMessage message)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                UpdateLocalizedTexts();
            });
        }
    }

    /// <summary>
    /// 雇主更新消息
    /// </summary>
    public class EmployerUpdatedMessage
    {
    }
}
