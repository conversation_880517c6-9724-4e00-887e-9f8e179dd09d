// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 雇主信息编辑页面 / Employer Information Editing Page
/// 用于创建和编辑雇主信息的表单页面
/// Form page for creating and editing employer information
/// </summary>
/// <remarks>
/// 该页面提供完整的雇主信息管理功能：
/// This page provides complete employer information management functionality:
/// - 雇主基本信息的输入和编辑 / Input and editing of basic employer information
/// - 键盘输入处理和导航 / Keyboard input handling and navigation
/// - 查询参数支持（用于编辑现有雇主） / Query parameter support (for editing existing employers)
/// </remarks>
[QueryProperty(nameof(EmployerId), "employerId")]
public partial class EmployerView : ContentPage
{
    // 雇主视图模型实例 / Employer view model instance
    private readonly EmployerViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化雇主视图并配置输入处理 / Initialize employer view and configure input handling
    /// </summary>
    /// <param name="viewModel">雇主视图模型 / Employer view model</param>
    public EmployerView(EmployerViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;

        // 配置输入框处理 / Configure entry handling
        ConfigureSimpleEntryHandling();
    }

    /// <summary>
    /// 雇主ID查询参数 / Employer ID query parameter
    /// 用于编辑模式时传递雇主ID / Used to pass employer ID in edit mode
    /// </summary>
    public string EmployerId
    {
        set
        {
            if (int.TryParse(value, out int employerId))
            {
                // 编辑模式初始化 / Edit mode initialization
                Loaded += async (s, e) => await _viewModel.InitializeAsync(employerId);
            }
            else
            {
                // 新增模式初始化 / Add mode initialization
                Loaded += async (s, e) => await _viewModel.InitializeAsync();
            }
        }
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 确保视图模型正确初始化 / Ensure view model is properly initialized
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // 如果没有通过查询参数初始化，则使用默认初始化 / Use default initialization if not initialized via query parameters
        if (_viewModel.PageTitle == "新增雇主" && _viewModel.EmployerId == 0)
        {
            await _viewModel.InitializeAsync();
        }
    }

    /// <summary>
    /// 配置输入框处理 / Configure entry handling
    /// 设置输入框之间的导航和焦点管理 / Set up navigation and focus management between entries
    /// </summary>
    private void ConfigureSimpleEntryHandling()
    {
        try
        {
            // 配置输入框导航链 / Configure entry navigation chain
            ConfigureEntryNavigation(NameEntry, ContactInfoEntry);
            ConfigureEntryNavigation(ContactInfoEntry, WorkLocationEntry);
            ConfigureEntryNavigation(WorkLocationEntry, HourlyRateEntry);
            ConfigureEntryNavigation(HourlyRateEntry, PaymentCycleDaysEntry);
            ConfigureEntryNavigation(PaymentCycleDaysEntry, null); // 最后一个输入框 / Last entry

            System.Diagnostics.Debug.WriteLine("EmployerView: 输入框处理配置完成 / Entry handling configuration completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"EmployerView: 配置输入框处理失败 / Failed to configure entry handling - {ex.Message}");
        }
    }

    /// <summary>
    /// 配置输入框导航 / Configure entry navigation
    /// 设置当前输入框完成时跳转到下一个输入框 / Set up navigation to next entry when current entry is completed
    /// </summary>
    /// <param name="currentEntry">当前输入框 / Current entry</param>
    /// <param name="nextEntry">下一个输入框 / Next entry</param>
    private void ConfigureEntryNavigation(Entry currentEntry, Entry? nextEntry)
    {
        if (currentEntry == null) return;

        try
        {
            // 输入完成事件处理 / Completion event handling
            currentEntry.Completed += (s, e) =>
            {
                try
                {
                    if (nextEntry != null)
                    {
                        // 跳转到下一个输入框 / Navigate to next entry
                        nextEntry.Focus();
                    }
                    else
                    {
                        // 最后一个输入框，跳转到备注编辑器 / Last entry, navigate to notes editor
                        NotesEditor?.Focus();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"EmployerView: 输入框导航失败 / Entry navigation failed - {ex.Message}");
                }
            };

            System.Diagnostics.Debug.WriteLine($"EmployerView: 输入框导航配置完成 / Entry navigation configured - {currentEntry.AutomationId}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"EmployerView: 配置输入框导航失败 / Failed to configure entry navigation - {ex.Message}");
        }
    }
}
