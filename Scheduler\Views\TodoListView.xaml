<?xml version="1.0" encoding="utf-8" ?>
<!--
    待办事项列表视图 / Todo List View
    显示和管理用户的待办事项，包括统计信息和操作功能
    Display and manage user's todo items, including statistics and operation functions
-->
<ContentPage x:Class="Scheduler.Views.TodoListView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             xmlns:behaviors="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:DataType="viewmodels:TodoListViewModel"
             Title="{Binding Title}">

    <!-- 下拉刷新视图 / Pull-to-refresh view -->
    <!-- 修复：移除嵌套的ScrollView，避免Android平台触摸事件冲突 -->
    <!-- Fix: Remove nested ScrollView to avoid touch event conflicts on Android platform -->
    <RefreshView Command="{Binding RefreshCommand}"
                 IsRefreshing="{Binding IsBusy}">
        <!-- 主要内容布局 / Main content layout -->
        <ScrollView>
            <VerticalStackLayout Spacing="15" Padding="20">

                <!-- 统计信息卡片 / Statistics card -->
                <Frame BackgroundColor="#2196F3"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="20">
                    <!-- 统计信息网格布局 / Statistics grid layout -->
                    <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="*,*,*,*">
                        <!-- 统计标题 / Statistics title -->
                        <Label Grid.Row="0" Grid.ColumnSpan="4"
                               Text="Todo Statistics"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="White"
                               HorizontalOptions="Center"
                               Margin="0,0,0,15"/>

                        <!-- 统计数据区域 / Statistics data area -->
                        <!-- 总数统计 / Total count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="0" HorizontalOptions="Center">
                            <Label Text="{Binding TotalCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="Total"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <!-- 未完成统计 / Pending count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="1" HorizontalOptions="Center">
                            <Label Text="{Binding PendingCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="Pending"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <!-- 已完成统计 / Completed count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="2" HorizontalOptions="Center">
                            <Label Text="{Binding CompletedCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                            <Label Text="Completed"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>

                        <!-- 逾期统计 / Overdue count statistics -->
                        <VerticalStackLayout Grid.Row="1" Grid.Column="3" HorizontalOptions="Center">
                            <Label Text="{Binding OverdueCount}"
                                   FontSize="24"
                                   FontAttributes="Bold"
                                   TextColor="#FF5722"
                                   HorizontalOptions="Center"/>
                            <Label Text="Overdue"
                                   FontSize="12"
                                   TextColor="White"
                                   HorizontalOptions="Center"/>
                        </VerticalStackLayout>
                    </Grid>
                </Frame>

                <!-- 操作按钮区域 / Action buttons area -->
                <Grid ColumnDefinitions="*,*,*">
                    <!-- 同步状态按钮 / Sync status button -->
                    <Button Grid.Column="0"
                            Text="Sync Status"
                            Command="{Binding SyncTodoStatusCommand}"
                            BackgroundColor="#4CAF50"
                            TextColor="White"
                            CornerRadius="8"
                            Margin="0,0,5,0"/>

                    <!-- 切换显示按钮 / Toggle display button -->
                    <Button Grid.Column="1"
                            Text="Toggle View"
                            Command="{Binding ToggleShowCompletedCommand}"
                            BackgroundColor="#FF9800"
                            TextColor="White"
                            CornerRadius="8"
                            Margin="5,0,5,0"/>

                    <!-- 刷新按钮 / Refresh button -->
                    <Button Grid.Column="2"
                            Text="Refresh"
                            Command="{Binding RefreshCommand}"
                            BackgroundColor="#607D8B"
                            TextColor="White"
                            CornerRadius="8"
                            Margin="5,0,0,0"/>
                </Grid>

                <!-- 类型过滤器区域 / Type filter area -->
                <Frame BackgroundColor="#F5F5F5"
                       HasShadow="False"
                       CornerRadius="8"
                       Padding="15">
                    <VerticalStackLayout Spacing="10">
                        <!-- 过滤器标题 / Filter title -->
                        <Label Text="Filter by Type"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <!-- 过滤器按钮网格 / Filter buttons grid -->
                        <Grid ColumnDefinitions="*,*,*,*">
                            <!-- 全部类型按钮 / All types button -->
                            <Button Grid.Column="0"
                                    Text="All"
                                    Command="{Binding ApplyTypeFilterCommand}"
                                    CommandParameter="{x:Null}"
                                    BackgroundColor="#E0E0E0"
                                    TextColor="#333"
                                    CornerRadius="6"
                                    FontSize="12"
                                    Margin="0,0,2,0"/>

                            <!-- 班次类型按钮 / Shift type button -->
                            <Button Grid.Column="1"
                                    Text="Shift"
                                    Command="{Binding ApplyTypeFilterCommand}"
                                    CommandParameter="{x:Static models:TodoItemType.Shift}"
                                    BackgroundColor="#E0E0E0"
                                    TextColor="#333"
                                    CornerRadius="6"
                                    FontSize="12"
                                    Margin="2,0,2,0"/>

                            <!-- 支付类型按钮 / Payment type button -->
                            <Button Grid.Column="2"
                                    Text="Payment"
                                    Command="{Binding ApplyTypeFilterCommand}"
                                    CommandParameter="{x:Static models:TodoItemType.Payment}"
                                    BackgroundColor="#E0E0E0"
                                    TextColor="#333"
                                    CornerRadius="6"
                                    FontSize="12"
                                    Margin="2,0,2,0"/>

                            <!-- 提醒类型按钮 / Reminder type button -->
                            <Button Grid.Column="3"
                                    Text="Reminder"
                                    Command="{Binding ApplyTypeFilterCommand}"
                                    CommandParameter="{x:Static models:TodoItemType.Reminder}"
                                    BackgroundColor="#E0E0E0"
                                    TextColor="#333"
                                    CornerRadius="6"
                                    FontSize="12"
                                    Margin="2,0,0,0"/>
                        </Grid>
                    </VerticalStackLayout>
                </Frame>

                <!-- 未完成的待办事项区域 / Pending todo items area -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="15">
                    <VerticalStackLayout Spacing="10">
                        <!-- 未完成任务标题 / Pending tasks title -->
                        <Label Text="Pending Tasks"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <!-- 未完成任务列表 / Pending tasks list -->
                        <CollectionView ItemsSource="{Binding PendingTodoItems}"
                                        EmptyView="No pending todo items">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:TodoItem">
                                    <!-- 单个任务项容器 / Individual task item container -->
                                    <Frame BackgroundColor="#FAFAFA"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <!-- 任务项网格布局 / Task item grid layout -->
                                        <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                                            <!-- 完成状态复选框 / Completion status checkbox -->
                                            <CheckBox Grid.Column="0"
                                                      IsChecked="{Binding IsCompleted}"
                                                      Color="{Binding PriorityColor}"
                                                      VerticalOptions="Center">
                                                <CheckBox.Behaviors>
                                                    <behaviors:EventToCommandBehavior EventName="CheckedChanged"
                                                                                      Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:TodoListViewModel}}, Path=ToggleTodoCompletionCommand}"
                                                                                      CommandParameter="{Binding .}"/>
                                                </CheckBox.Behaviors>
                                            </CheckBox>

                                            <!-- 任务信息区域 / Task information area -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="3" Margin="10,0,0,0">
                                                <!-- 任务标题 / Task title -->
                                                <Label Text="{Binding Title}"
                                                       FontSize="16"
                                                       FontAttributes="Bold"
                                                       TextColor="#333"/>
                                                <!-- 任务描述 / Task description -->
                                                <Label Text="{Binding Description}"
                                                       FontSize="12"
                                                       TextColor="#666"
                                                       MaxLines="2"/>
                                                <!-- 任务标签区域 / Task tags area -->
                                                <Grid ColumnDefinitions="Auto,Auto,*">
                                                    <!-- 任务类型标签 / Task type tag -->
                                                    <Label Grid.Column="0"
                                                           Text="{Binding TypeText}"
                                                           FontSize="10"
                                                           TextColor="White"
                                                           BackgroundColor="{Binding PriorityColor}"
                                                           Padding="6,2"
                                                           Margin="0,0,5,0"/>
                                                    <!-- 优先级标签 / Priority tag -->
                                                    <Label Grid.Column="1"
                                                           Text="{Binding PriorityText}"
                                                           FontSize="10"
                                                           TextColor="{Binding PriorityColor}"
                                                           FontAttributes="Bold"/>
                                                </Grid>
                                            </VerticalStackLayout>

                                            <!-- 状态信息区域 / Status information area -->
                                            <VerticalStackLayout Grid.Column="2" HorizontalOptions="End" VerticalOptions="Center">
                                                <!-- 状态文本 / Status text -->
                                                <Label Text="{Binding StatusText}"
                                                       FontSize="12"
                                                       TextColor="{Binding StatusColor}"
                                                       FontAttributes="Bold"
                                                       HorizontalOptions="End"/>
                                                <!-- 截止时间 / Due date -->
                                                <Label Text="{Binding DueDate, StringFormat='{0:MM/dd HH:mm}'}"
                                                       FontSize="10"
                                                       TextColor="#999"
                                                       HorizontalOptions="End"/>
                                            </VerticalStackLayout>

                                            <!-- 删除按钮 / Delete button -->
                                            <Button Grid.Column="3"
                                                    Text="✕"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:TodoListViewModel}}, Path=DeleteTodoItemCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="#F44336"
                                                    TextColor="White"
                                                    CornerRadius="15"
                                                    WidthRequest="30"
                                                    HeightRequest="30"
                                                    FontSize="12"
                                                    Margin="10,0,0,0"
                                                    VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- 最近完成的待办事项区域 / Recently completed todo items area -->
                <Frame BackgroundColor="White"
                       HasShadow="True"
                       CornerRadius="12"
                       Padding="15"
                       IsVisible="{Binding ShowCompletedItems}">
                    <VerticalStackLayout Spacing="10">
                        <!-- 已完成任务标题 / Completed tasks title -->
                        <Label Text="Recently Completed Tasks"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <!-- 已完成任务列表 / Completed tasks list -->
                        <CollectionView ItemsSource="{Binding RecentCompletedTodoItems}"
                                        EmptyView="No completed todo items">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:TodoItem">
                                    <!-- 已完成任务项容器 / Completed task item container -->
                                    <Frame BackgroundColor="#E8F5E8"
                                           CornerRadius="8"
                                           Margin="0,3"
                                           Padding="12"
                                           HasShadow="False">
                                        <!-- 已完成任务网格布局 / Completed task grid layout -->
                                        <Grid ColumnDefinitions="Auto,*,Auto">
                                            <!-- 完成标记 / Completion mark -->
                                            <Label Grid.Column="0"
                                                   Text="✓"
                                                   FontSize="20"
                                                   TextColor="#4CAF50"
                                                   FontAttributes="Bold"
                                                   VerticalOptions="Center"/>

                                            <!-- 已完成任务信息 / Completed task information -->
                                            <VerticalStackLayout Grid.Column="1" Spacing="3" Margin="10,0,0,0">
                                                <!-- 已完成任务标题 / Completed task title -->
                                                <Label Text="{Binding Title}"
                                                       FontSize="16"
                                                       TextColor="#333"
                                                       TextDecorations="Strikethrough"/>
                                                <!-- 已完成任务类型 / Completed task type -->
                                                <Label Text="{Binding TypeText}"
                                                       FontSize="12"
                                                       TextColor="#666"/>
                                            </VerticalStackLayout>

                                            <!-- 完成时间 / Completion time -->
                                            <Label Grid.Column="2"
                                                   Text="{Binding CompletedDate, StringFormat='{0:MM/dd HH:mm}'}"
                                                   FontSize="12"
                                                   TextColor="#4CAF50"
                                                   VerticalOptions="Center"/>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </Frame>

                <!-- 加载指示器 / Loading indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}"
                                   IsRunning="{Binding IsBusy}"
                                   Color="#2196F3"
                                   VerticalOptions="Center"
                                   HorizontalOptions="Center"
                                   Margin="20"/>

            </VerticalStackLayout>
        </ScrollView>
    </RefreshView>
</ContentPage>
