namespace Scheduler.Models
{
    /// <summary>
    /// 工作记录详细信息 / Work Record With Details
    /// 包含班次和雇主信息的扩展工作记录
    /// Extended work record containing shift and employer information
    /// </summary>
    public class WorkRecordWithDetails : WorkRecord
    {
        /// <summary>班次开始时间 / Shift Start Time</summary>
        public DateTime ShiftStartTime { get; set; }
        /// <summary>班次结束时间 / Shift End Time</summary>
        public DateTime ShiftEndTime { get; set; }
        /// <summary>班次地点 / Shift Location</summary>
        public string? ShiftLocation { get; set; }
        /// <summary>班次描述 / Shift Description</summary>
        public string? ShiftDescription { get; set; }
        /// <summary>雇主名称 / Employer Name</summary>
        public string EmployerName { get; set; } = string.Empty;
        /// <summary>雇主时薪 / Employer Hourly Rate</summary>
        public decimal EmployerHourlyRate { get; set; }
        /// <summary>雇主颜色 / Employer Color</summary>
        public string EmployerColor { get; set; } = string.Empty;
    }

    /// <summary>
    /// 班次统计信息 / Shift Statistics
    /// 提供班次相关的统计数据和计算指标
    /// Provides shift-related statistical data and calculated metrics
    /// </summary>
    public class ShiftStatistics
    {
        /// <summary>总班次数 / Total Shifts</summary>
        public int TotalShifts { get; set; }
        /// <summary>已完成班次数 / Completed Shifts</summary>
        public int CompletedShifts { get; set; }
        /// <summary>已取消班次数 / Cancelled Shifts</summary>
        public int CancelledShifts { get; set; }
        /// <summary>平均工作小时数 / Average Hours</summary>
        public double AverageHours { get; set; }
        /// <summary>总计划工作小时数 / Total Scheduled Hours</summary>
        public double TotalScheduledHours { get; set; }

        /// <summary>完成率 / Completion Rate - 已完成班次占总班次的百分比</summary>
        public double CompletionRate => TotalShifts > 0 ? (double)CompletedShifts / TotalShifts * 100 : 0;
        /// <summary>取消率 / Cancellation Rate - 已取消班次占总班次的百分比</summary>
        public double CancellationRate => TotalShifts > 0 ? (double)CancelledShifts / TotalShifts * 100 : 0;
    }

    /// <summary>
    /// 月度工作时长统计 / Monthly Work Hours
    /// 按月统计的工作时长和班次数据
    /// Monthly statistics of work hours and shift data
    /// </summary>
    public class MonthlyWorkHours
    {
        /// <summary>月份 / Month - 格式为MM</summary>
        public string Month { get; set; } = string.Empty;
        /// <summary>总工作小时数 / Total Hours</summary>
        public double TotalHours { get; set; }
        /// <summary>总班次数 / Total Shifts</summary>
        public int TotalShifts { get; set; }
        /// <summary>平均工作小时数 / Average Hours</summary>
        public double AverageHours { get; set; }

        /// <summary>月份名称 / Month Name - 将数字月份转换为月份名称</summary>
        public string MonthName => DateTime.TryParseExact(Month, "MM", null, System.Globalization.DateTimeStyles.None, out var date)
            ? date.ToString("MMMM") : Month;
    }

    /// <summary>
    /// 顶级雇主统计
    /// </summary>
    public class TopEmployer
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal TotalEarnings { get; set; }
        public int TotalShifts { get; set; }
        public double TotalHours { get; set; }
        
        public decimal AverageEarningsPerHour => TotalHours > 0 ? TotalEarnings / (decimal)TotalHours : 0;
    }

    /// <summary>
    /// 工作效率指标
    /// </summary>
    public class WorkEfficiencyMetrics
    {
        public int TotalWorkRecords { get; set; }
        public double TotalWorkHours { get; set; }
        public double AverageShiftHours { get; set; }
        public double TotalOvertimeHours { get; set; }
        public double OnTimePercentage { get; set; }
        public double AverageLateMinutes { get; set; }
        public int WorkingDays { get; set; }
        
        public double AverageHoursPerDay => WorkingDays > 0 ? TotalWorkHours / WorkingDays : 0;
        public double OvertimePercentage => TotalWorkHours > 0 ? TotalOvertimeHours / TotalWorkHours * 100 : 0;
    }

    /// <summary>
    /// 收入趋势数据
    /// </summary>
    public class IncomeTrend
    {
        public string Period { get; set; } = string.Empty;
        public decimal TotalIncome { get; set; }
        public int PaymentCount { get; set; }
        
        public decimal AveragePayment => PaymentCount > 0 ? TotalIncome / PaymentCount : 0;
    }

    /// <summary>
    /// 验证结果基类
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid => !Errors.Any();
        public List<string> Errors { get; } = new List<string>();
        public List<string> Warnings { get; } = new List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        public string GetErrorsAsString()
        {
            return string.Join("\n", Errors);
        }

        public string GetWarningsAsString()
        {
            return string.Join("\n", Warnings);
        }
    }

    /// <summary>
    /// 工资验证结果
    /// </summary>
    public class PayrollValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        public string GetErrorsAsString()
        {
            return string.Join("\n", Errors);
        }

        public string GetWarningsAsString()
        {
            return string.Join("\n", Warnings);
        }
    }

    /// <summary>
    /// 班次验证结果
    /// </summary>
    public class ShiftValidationResult
    {
        public bool IsValid { get; set; }
        public bool HasConflicts { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<ShiftConflict> Conflicts { get; set; } = new List<ShiftConflict>();

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        public string GetErrorsAsString()
        {
            return string.Join("\n", Errors);
        }

        public string GetWarningsAsString()
        {
            return string.Join("\n", Warnings);
        }
    }

    /// <summary>
    /// 冲突类型枚举
    /// </summary>
    public enum ConflictType
    {
        Overlap,    // 部分重叠
        Exact,      // 完全相同
        Contained,  // 被包含
        Contains    // 包含其他
    }

    /// <summary>
    /// 班次冲突信息
    /// </summary>
    public class ShiftConflict
    {
        // 新架构属性
        public Shift NewShift { get; set; } = new();
        public Shift ConflictingShift { get; set; } = new();
        public ConflictType ConflictType { get; set; }
        public int OverlapMinutes { get; set; }

        // 兼容旧架构的属性
        public List<Shift> ConflictingShifts { get; set; } = new();
        public DateTime OverlapStart { get; set; }
        public DateTime OverlapEnd { get; set; }
        public string Description { get; set; } = string.Empty;

        public string ConflictDescription => ConflictType switch
        {
            ConflictType.Exact => "完全重叠",
            ConflictType.Overlap => $"部分重叠 ({OverlapMinutes} 分钟)",
            ConflictType.Contained => "被包含在其他班次中",
            ConflictType.Contains => "包含其他班次",
            _ => "未知冲突类型"
        };

        /// <summary>
        /// 生成冲突描述（兼容旧代码）
        /// </summary>
        public void GenerateDescription()
        {
            if (ConflictingShifts.Count >= 2)
            {
                var shift1 = ConflictingShifts[0];
                var shift2 = ConflictingShifts[1];

                var overlapMinutes = (int)(OverlapEnd - OverlapStart).TotalMinutes;
                Description = $"班次冲突: {shift1.Description} 与 {shift2.Description} 重叠 {overlapMinutes} 分钟";
            }
            else if (!string.IsNullOrEmpty(ConflictDescription))
            {
                Description = ConflictDescription;
            }
        }
    }

    /// <summary>
    /// 备份数据结构
    /// </summary>
    public class BackupData
    {
        public string Version { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public List<Employer> Employers { get; set; } = new();
        public List<Shift> Shifts { get; set; } = new();
        public List<WorkRecord> WorkRecords { get; set; } = new();
        public List<PaymentRecord> PaymentRecords { get; set; } = new();
        public List<NotificationRecord> NotificationRecords { get; set; } = new();
    }

    /// <summary>
    /// 备份元数据
    /// </summary>
    public class BackupMetadata
    {
        public string Version { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Dictionary<string, int> RecordCounts { get; set; } = new();
    }

    /// <summary>
    /// 备份信息
    /// </summary>
    public class BackupInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public long FileSize { get; set; }
        public string Version { get; set; } = string.Empty;
        public Dictionary<string, int> RecordCounts { get; set; } = new();

        public string FileSizeFormatted => FormatFileSize(FileSize);

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
