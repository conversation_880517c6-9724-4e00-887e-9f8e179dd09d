// 引入数据模型命名空间 / Import data models namespace
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 通知设置服务接口 / Notification Settings Service Interface
    /// 定义通知设置管理的标准契约
    /// Defines standard contract for notification settings management
    /// </summary>
    public interface INotificationSettingsService
    {
        /// <summary>获取通知设置 / Get notification settings</summary>
        Task<NotificationSettings> GetSettingsAsync();
        /// <summary>保存通知设置 / Save notification settings</summary>
        Task<bool> SaveSettingsAsync(NotificationSettings settings);
        /// <summary>重置为默认设置 / Reset to default settings</summary>
        Task<bool> ResetToDefaultAsync();
        /// <summary>更新单个设置项 / Update single setting item</summary>
        Task<bool> UpdateSettingAsync(string key, object value);
        /// <summary>获取单个设置项 / Get single setting item</summary>
        Task<T> GetSettingAsync<T>(string key, T defaultValue = default);
    }

    /// <summary>
    /// 通知设置服务 / Notification Settings Service
    /// 负责管理通知相关的用户偏好设置
    /// Responsible for managing notification-related user preference settings
    /// </summary>
    public class NotificationSettingsService : INotificationSettingsService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 缓存的设置对象 / Cached settings object
        private NotificationSettings? _cachedSettings;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化通知设置服务 / Initialize notification settings service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public NotificationSettingsService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 获取通知设置 / Get notification settings
        /// 从缓存或数据库获取用户的通知设置
        /// Get user notification settings from cache or database
        /// </summary>
        /// <returns>通知设置对象 / Notification settings object</returns>
        public async Task<NotificationSettings> GetSettingsAsync()
        {
            try
            {
                // 如果有缓存，直接返回 / If cached, return directly
                if (_cachedSettings != null)
                {
                    return _cachedSettings;
                }

                // 从数据库获取设置 / Get settings from database
                var settings = await _databaseService.GetNotificationSettingsAsync();

                // 如果没有设置，创建默认设置 / If no settings, create default settings
                if (settings == null)
                {
                    settings = NotificationSettings.GetDefault();
                    await _databaseService.SaveNotificationSettingsAsync(settings);
                }

                // 缓存设置
                _cachedSettings = settings;
                return settings;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取通知设置失败: {ex.Message}");
                return NotificationSettings.GetDefault();
            }
        }

        /// <summary>
        /// 保存通知设置
        /// </summary>
        public async Task<bool> SaveSettingsAsync(NotificationSettings settings)
        {
            try
            {
                settings.UpdatedAt = DateTime.Now;
                var result = await _databaseService.SaveNotificationSettingsAsync(settings);
                
                if (result > 0)
                {
                    // 更新缓存
                    _cachedSettings = settings;
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存通知设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public async Task<bool> ResetToDefaultAsync()
        {
            try
            {
                var defaultSettings = NotificationSettings.GetDefault();
                
                // 如果已有设置，保留ID
                var existingSettings = await _databaseService.GetNotificationSettingsAsync();
                if (existingSettings != null)
                {
                    defaultSettings.Id = existingSettings.Id;
                    defaultSettings.CreatedAt = existingSettings.CreatedAt;
                }

                return await SaveSettingsAsync(defaultSettings);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置通知设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新单个设置项
        /// </summary>
        public async Task<bool> UpdateSettingAsync(string key, object value)
        {
            try
            {
                var settings = await GetSettingsAsync();
                var property = typeof(NotificationSettings).GetProperty(key);
                
                if (property != null && property.CanWrite)
                {
                    // 类型转换
                    var convertedValue = Convert.ChangeType(value, property.PropertyType);
                    property.SetValue(settings, convertedValue);
                    
                    return await SaveSettingsAsync(settings);
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新设置项失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取单个设置项
        /// </summary>
        public async Task<T> GetSettingAsync<T>(string key, T defaultValue = default)
        {
            try
            {
                var settings = await GetSettingsAsync();
                var property = typeof(NotificationSettings).GetProperty(key);
                
                if (property != null && property.CanRead)
                {
                    var value = property.GetValue(settings);
                    if (value != null)
                    {
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                }
                
                return defaultValue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取设置项失败: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        public void ClearCache()
        {
            _cachedSettings = null;
        }

        /// <summary>
        /// 检查是否在免打扰时间内
        /// </summary>
        public async Task<bool> IsInQuietHoursAsync()
        {
            try
            {
                var settings = await GetSettingsAsync();
                return settings.IsInQuietHours;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查免打扰时间失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取班次提醒提前时间
        /// </summary>
        public async Task<int> GetShiftReminderMinutesAsync()
        {
            return await GetSettingAsync("ShiftReminderMinutes", 15);
        }

        /// <summary>
        /// 获取休息提醒间隔
        /// </summary>
        public async Task<int> GetBreakReminderIntervalHoursAsync()
        {
            return await GetSettingAsync("BreakReminderIntervalHours", 2);
        }

        /// <summary>
        /// 获取每日工作安排通知时间
        /// </summary>
        public async Task<TimeSpan> GetDailyScheduleTimeAsync()
        {
            var settings = await GetSettingsAsync();
            return settings.DailyScheduleTime;
        }

        /// <summary>
        /// 检查特定类型的通知是否启用
        /// </summary>
        public async Task<bool> IsNotificationTypeEnabledAsync(NotificationType type)
        {
            try
            {
                var settings = await GetSettingsAsync();
                
                if (!settings.IsNotificationEnabled)
                    return false;

                return type switch
                {
                    NotificationType.WorkReminder => settings.IsShiftReminderEnabled,
                    NotificationType.BreakReminder => settings.IsBreakReminderEnabled,
                    NotificationType.PaymentDue => settings.IsPaymentReminderEnabled,
                    NotificationType.ShiftConflict => settings.IsShiftConflictEnabled,
                    _ => true
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查通知类型启用状态失败: {ex.Message}");
                return false;
            }
        }
    }
}
