<?xml version="1.0" encoding="utf-8" ?>
<!--
    登录视图 / Login View
    用户身份验证页面，应用程序的入口点
    User authentication page, entry point of the application
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             x:Class="Scheduler.Views.LoginView"
             x:DataType="viewmodels:LoginViewModel"
             Title="Login"
             Shell.NavBarIsVisible="False">

    <ScrollView>
        <VerticalStackLayout Spacing="0" VerticalOptions="FillAndExpand">

            <!-- 顶部装饰区域 / Top decoration area -->
            <Frame BackgroundColor="#007ACC"
                   HasShadow="False"
                   CornerRadius="0"
                   Padding="0"
                   HeightRequest="250"
                   Margin="0,-50,0,0">
                <Grid>
                    <!-- 背景装饰元素 / Background decoration elements -->
                    <Ellipse Fill="#0066AA"
                             WidthRequest="300"
                             HeightRequest="300"
                             HorizontalOptions="End"
                             VerticalOptions="Start"
                             Margin="0,-100,-100,0"
                             Opacity="0.3"/>
                    <Ellipse Fill="#0088CC"
                             WidthRequest="200"
                             HeightRequest="200"
                             HorizontalOptions="Start"
                             VerticalOptions="End"
                             Margin="-50,0,0,-50"
                             Opacity="0.2"/>

                    <!-- Logo和标题区域 / Logo and title area -->
                    <VerticalStackLayout VerticalOptions="Center"
                                         HorizontalOptions="Center"
                                         Spacing="10"
                                         Margin="0,30,0,0">
                        <!-- 应用图标 / Application icon -->
                        <Frame BackgroundColor="White"
                               WidthRequest="80"
                               HeightRequest="80"
                               CornerRadius="40"
                               HasShadow="True"
                               Padding="20">
                            <Label Text="📅"
                                   FontSize="30"
                                   HorizontalOptions="Center"
                                   VerticalOptions="Center"/>
                        </Frame>

                        <!-- 应用标题 / Application title -->
                        <Label Text="Scheduler"
                               FontSize="32"
                               FontAttributes="Bold"
                               TextColor="White"
                               HorizontalOptions="Center"/>
                        <Label Text="{Binding LocalizedTexts.WorkManagementSystem}"
                               FontSize="16"
                               TextColor="White"
                               Opacity="0.9"
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>
                </Grid>
            </Frame>

            <!-- 登录表单区域 / Login form area -->
            <VerticalStackLayout Spacing="20"
                                 Padding="30,40,30,30"
                                 VerticalOptions="FillAndExpand">

                <!-- 欢迎文本 / Welcome text -->
                <VerticalStackLayout Spacing="8" Margin="0,0,0,20">
                    <Label Text="Welcome Back"
                           FontSize="28"
                           FontAttributes="Bold"
                           TextColor="#1F2937"
                           HorizontalOptions="Center"/>
                    <Label Text="Sign in to continue"
                           FontSize="16"
                           TextColor="#6B7280"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>

                <!-- 用户名输入框 / Username input field -->
                <Frame BackgroundColor="#F8F9FA"
                       HasShadow="False"
                       CornerRadius="12"
                       Padding="0">
                    <Grid ColumnDefinitions="*,Auto">
                        <Entry Grid.Column="0"
                               x:Name="UsernameEntry"
                               Text="{Binding Username}"
                               Placeholder="{Binding LocalizedTexts.Username}"
                               FontSize="16"
                               BackgroundColor="Transparent"
                               TextColor="#1F2937"
                               PlaceholderColor="#9CA3AF"
                               Margin="16,12,0,12"
                               ReturnType="Next"
                               ClearButtonVisibility="WhileEditing"
                               Focused="OnUsernameEntryFocused"/>
                    </Grid>
                </Frame>

                <!-- 密码输入框 -->
                <Frame BackgroundColor="#F8F9FA"
                       HasShadow="False"
                       CornerRadius="12"
                       Padding="0">
                    <Grid ColumnDefinitions="*,Auto">
                        <Entry Grid.Column="0"
                               x:Name="PasswordEntry"
                               Text="{Binding Password}"
                               Placeholder="{Binding LocalizedTexts.Password}"
                               IsPassword="{Binding IsPasswordVisible, Converter={StaticResource InverseBoolConverter}}"
                               FontSize="16"
                               BackgroundColor="Transparent"
                               TextColor="#1F2937"
                               PlaceholderColor="#9CA3AF"
                               Margin="16,12,0,12"
                               Keyboard="Default"
                               ReturnType="Done"
                               InputTransparent="False"
                               ClearButtonVisibility="WhileEditing"
                               Focused="OnPasswordEntryFocused"/>
                        <Button Grid.Column="1"
                                Text="👁️"
                                BackgroundColor="Transparent"
                                TextColor="#6B7280"
                                FontSize="18"
                                WidthRequest="50"
                                Command="{Binding TogglePasswordVisibilityCommand}"/>
                    </Grid>
                </Frame>

                <!-- 记住我和忘记密码 -->
                <Grid ColumnDefinitions="*,Auto" Margin="0,10,0,0">
                    <StackLayout Grid.Column="0" Orientation="Horizontal" Spacing="8">
                        <CheckBox IsChecked="{Binding RememberMe}"
                                  Color="#007ACC"/>
                        <Label Text="{Binding LocalizedTexts.RememberMe}"
                               FontSize="14"
                               TextColor="#6B7280"
                               VerticalOptions="Center"/>
                    </StackLayout>
                    <Button Grid.Column="1"
                            Text="{Binding LocalizedTexts.ForgotPassword}"
                            BackgroundColor="Transparent"
                            TextColor="#007ACC"
                            FontSize="14"
                            Command="{Binding ForgotPasswordCommand}"/>
                </Grid>

                <!-- 登录按钮 -->
                <Button Text="{Binding LocalizedTexts.Login}"
                        BackgroundColor="#007ACC"
                        TextColor="White"
                        FontSize="18"
                        FontAttributes="Bold"
                        CornerRadius="12"
                        HeightRequest="50"
                        Margin="0,20,0,0"
                        Command="{Binding LoginCommand}"
                        IsEnabled="{Binding IsLoginEnabled}"/>

                <!-- 加载指示器 -->
                <ActivityIndicator IsVisible="{Binding IsBusy}"
                                   IsRunning="{Binding IsBusy}"
                                   Color="#007ACC"
                                   HeightRequest="30"
                                   Margin="0,10,0,0"/>

                <!-- 注册链接 -->
                <StackLayout Orientation="Horizontal" 
                             HorizontalOptions="Center"
                             Spacing="5"
                             Margin="0,30,0,0">
                    <Label Text="Don't have an account?"
                           FontSize="14"
                           TextColor="#6B7280"
                            VerticalOptions="Center"/>   
                    <!--该位置因为按钮不能水平线一致，于是询问了ai -->
                    <Button Text="  Sign Up  "
                            BackgroundColor="Transparent"
                            TextColor="#007ACC"
                            FontSize="14"
                            FontAttributes="Bold"
                            Padding="0"
                            Command="{Binding RegisterCommand}"
                           
                            />
                </StackLayout>

                <!-- 底部空间 -->
                <BoxView HeightRequest="50" BackgroundColor="Transparent"/>
                
            </VerticalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
