// 引入数据模型命名空间 / Import data models namespace
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 数据库服务接口 / Database Service Interface
    /// 定义数据库操作的标准契约
    /// Defines standard contract for database operations
    /// </summary>
    public interface IDatabaseService
    {
        // 雇主相关方法 / Employer related methods
        /// <summary>获取所有雇主 / Get all employers</summary>
        Task<List<Employer>> GetEmployersAsync();
        /// <summary>根据ID获取雇主 / Get employer by ID</summary>
        Task<Employer?> GetEmployerAsync(int id);
        /// <summary>保存雇主信息 / Save employer information</summary>
        Task<int> SaveEmployerAsync(Employer employer);
        /// <summary>更新雇主信息 / Update employer information</summary>
        Task<int> UpdateEmployerAsync(Employer employer);
        /// <summary>删除雇主 / Delete employer</summary>
        Task<int> DeleteEmployerAsync(int id);
        /// <summary>检查是否有已确认的支付记录 / Check if has confirmed payment records</summary>
        Task<bool> HasConfirmedPaymentRecordsAsync(int employerId);
        /// <summary>获取已确认支付记录数量 / Get confirmed payment records count</summary>
        Task<int> GetConfirmedPaymentRecordsCountAsync(int employerId);

        // 班次相关方法 / Shift related methods
        /// <summary>按日期范围获取班次 / Get shifts by date range</summary>
        Task<List<Shift>> GetShiftsAsync(DateTime startDate, DateTime endDate);
        /// <summary>按日期范围获取班次 / Get shifts by date range</summary>
        Task<List<Shift>> GetShiftsByDateRangeAsync(DateTime startDate, DateTime endDate);
        /// <summary>按雇主获取班次 / Get shifts by employer</summary>
        Task<List<Shift>> GetShiftsByEmployerAsync(int employerId);
        /// <summary>获取包含雇主信息的班次 / Get shifts with employer info</summary>
        Task<List<Shift>> GetShiftsWithEmployerInfoAsync();
        /// <summary>获取今日班次 / Get today's shifts</summary>
        Task<List<Shift>> GetTodayShiftsAsync();
        /// <summary>获取即将到来的班次 / Get upcoming shifts</summary>
        Task<List<Shift>> GetUpcomingShiftsAsync(int days = 7);
        /// <summary>根据ID获取班次 / Get shift by ID</summary>
        Task<Shift?> GetShiftAsync(int shiftId);
        /// <summary>根据ID获取班次 / Get shift by ID</summary>
        Task<Shift?> GetShiftByIdAsync(int shiftId);
        /// <summary>保存班次 / Save shift</summary>
        Task<int> SaveShiftAsync(Shift shift);
        /// <summary>更新班次 / Update shift</summary>
        Task<int> UpdateShiftAsync(Shift shift);
        /// <summary>删除班次 / Delete shift</summary>
        Task<int> DeleteShiftAsync(int shiftId);
        /// <summary>删除班次 / Delete shift</summary>
        Task<int> DeleteShiftAsync(Shift shift);
        /// <summary>批量保存班次 / Save shifts in batch</summary>
        Task<int> SaveShiftsBatchAsync(List<Shift> shifts);

        // 工作记录相关方法 / Work record related methods
        /// <summary>获取所有工作记录 / Get all work records</summary>
        Task<List<WorkRecord>> GetWorkRecordsAsync();
        /// <summary>按班次获取工作记录 / Get work records by shift</summary>
        Task<List<WorkRecord>> GetWorkRecordsByShiftAsync(int shiftId);
        /// <summary>保存工作记录 / Save work record</summary>
        Task<int> SaveWorkRecordAsync(WorkRecord workRecord);
        /// <summary>更新工作记录 / Update work record</summary>
        Task<int> UpdateWorkRecordAsync(WorkRecord workRecord);
        /// <summary>删除工作记录 / Delete work record</summary>
        Task<int> DeleteWorkRecordAsync(int workRecordId);

        // 支付记录相关方法 / Payment record related methods
        /// <summary>获取所有支付记录 / Get all payment records</summary>
        Task<List<PaymentRecord>> GetPaymentRecordsAsync();
        /// <summary>按雇主获取支付记录 / Get payment records by employer</summary>
        Task<List<PaymentRecord>> GetPaymentRecordsByEmployerAsync(int employerId);
        /// <summary>保存支付记录 / Save payment record</summary>
        Task<int> SavePaymentRecordAsync(PaymentRecord paymentRecord);
        /// <summary>更新支付记录 / Update payment record</summary>
        Task<int> UpdatePaymentRecordAsync(PaymentRecord paymentRecord);
        /// <summary>删除支付记录 / Delete payment record</summary>
        Task<int> DeletePaymentRecordAsync(int paymentRecordId);

        // 数据库初始化 / Database initialization
        /// <summary>初始化数据库 / Initialize database</summary>
        Task InitAsync();
    }
}
