using SQLite;
using System.Text;
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 数据库连接测试服务
    /// Database connection testing service
    /// </summary>
    public class DatabaseConnectionTester
    {
        private readonly DatabaseService _databaseService;

        public DatabaseConnectionTester(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 执行完整的数据库连接测试
        /// Performs comprehensive database connection testing
        /// </summary>
        public async Task<DatabaseTestResult> RunComprehensiveTestAsync()
        {
            var result = new DatabaseTestResult();
            var log = new StringBuilder();

            try
            {
                log.AppendLine("=== 数据库连接综合测试 ===");
                log.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                log.AppendLine();

                // 1. 基础连接测试
                log.AppendLine("1. 基础连接测试...");
                result.ConnectionTest = await TestBasicConnectionAsync();
                log.AppendLine($"   结果: {(result.ConnectionTest ? "✅ 成功" : "❌ 失败")}");

                // 2. 数据库文件检查
                log.AppendLine("2. 数据库文件检查...");
                result.FileSystemTest = await TestDatabaseFileAsync();
                log.AppendLine($"   结果: {(result.FileSystemTest ? "✅ 成功" : "❌ 失败")}");

                // 3. 表结构验证
                log.AppendLine("3. 表结构验证...");
                result.SchemaTest = await TestDatabaseSchemaAsync();
                log.AppendLine($"   结果: {(result.SchemaTest ? "✅ 成功" : "❌ 失败")}");

                // 4. 读写操作测试
                log.AppendLine("4. 读写操作测试...");
                result.ReadWriteTest = await TestReadWriteOperationsAsync();
                log.AppendLine($"   结果: {(result.ReadWriteTest ? "✅ 成功" : "❌ 失败")}");

                // 5. 事务测试
                log.AppendLine("5. 事务测试...");
                result.TransactionTest = await TestTransactionOperationsAsync();
                log.AppendLine($"   结果: {(result.TransactionTest ? "✅ 成功" : "❌ 失败")}");

                // 6. 性能测试
                log.AppendLine("6. 性能测试...");
                result.PerformanceTest = await TestPerformanceAsync();
                log.AppendLine($"   结果: {(result.PerformanceTest ? "✅ 成功" : "❌ 失败")}");

                result.OverallSuccess = result.ConnectionTest && result.FileSystemTest && 
                                      result.SchemaTest && result.ReadWriteTest && 
                                      result.TransactionTest && result.PerformanceTest;

                log.AppendLine();
                log.AppendLine($"=== 测试完成 ===");
                log.AppendLine($"总体结果: {(result.OverallSuccess ? "✅ 全部通过" : "❌ 存在问题")}");
                log.AppendLine($"测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                result.TestLog = log.ToString();
                return result;
            }
            catch (Exception ex)
            {
                log.AppendLine($"测试过程中发生异常: {ex.Message}");
                result.TestLog = log.ToString();
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 测试基础数据库连接
        /// </summary>
        private async Task<bool> TestBasicConnectionAsync()
        {
            try
            {
                // 通过调用公共方法来测试数据库连接
                var employers = await _databaseService.GetEmployersAsync();
                var diagnostics = await _databaseService.GetComprehensiveDatabaseDiagnosticsAsync();
                System.Diagnostics.Debug.WriteLine("数据库诊断信息:");
                System.Diagnostics.Debug.WriteLine(diagnostics);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"基础连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据库文件系统
        /// </summary>
        private async Task<bool> TestDatabaseFileAsync()
        {
            try
            {
                var dbPath = Path.Combine(FileSystem.AppDataDirectory, "scheduler.db");
                var fileExists = File.Exists(dbPath);
                
                if (fileExists)
                {
                    var fileInfo = new FileInfo(dbPath);
                    System.Diagnostics.Debug.WriteLine($"数据库文件大小: {fileInfo.Length} bytes");
                    System.Diagnostics.Debug.WriteLine($"最后修改时间: {fileInfo.LastWriteTime}");
                }

                return fileExists;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"文件系统测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据库表结构
        /// </summary>
        private async Task<bool> TestDatabaseSchemaAsync()
        {
            try
            {
                var employers = await _databaseService.GetEmployersAsync();
                var startDate = DateTime.Today.AddDays(-30);
                var endDate = DateTime.Today.AddDays(30);
                var shifts = await _databaseService.GetShiftsAsync(startDate, endDate);
                var workRecords = await _databaseService.GetWorkRecordsAsync();
                
                System.Diagnostics.Debug.WriteLine($"雇主表记录数: {employers.Count}");
                System.Diagnostics.Debug.WriteLine($"班次表记录数: {shifts.Count}");
                System.Diagnostics.Debug.WriteLine($"工作记录表记录数: {workRecords.Count}");
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"表结构测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试读写操作
        /// </summary>
        private async Task<bool> TestReadWriteOperationsAsync()
        {
            try
            {
                // 创建测试雇主
                var testEmployer = new Employer
                {
                    Name = "测试雇主_连接测试",
                    ContactInfo = "<EMAIL>",
                    HourlyRate = 25.0m,
                    PaymentCycleDays = 30,
                    Color = "#FF0000",
                    WorkLocation = "测试地点",
                    Notes = "数据库连接测试用数据",
                    IsActive = true
                };

                // 插入测试
                var insertedId = await _databaseService.SaveEmployerAsync(testEmployer);
                System.Diagnostics.Debug.WriteLine($"插入测试雇主，ID: {insertedId}");

                // 读取测试
                var retrievedEmployer = await _databaseService.GetEmployerAsync(insertedId);
                if (retrievedEmployer == null)
                {
                    System.Diagnostics.Debug.WriteLine("读取测试失败：无法找到刚插入的雇主");
                    return false;
                }

                // 更新测试
                retrievedEmployer.Name = "测试雇主_已更新";
                await _databaseService.SaveEmployerAsync(retrievedEmployer);

                // 验证更新
                var updatedEmployer = await _databaseService.GetEmployerAsync(insertedId);
                if (updatedEmployer?.Name != "测试雇主_已更新")
                {
                    System.Diagnostics.Debug.WriteLine("更新测试失败：数据未正确更新");
                    return false;
                }

                // 删除测试
                await _databaseService.DeleteEmployerAsync(retrievedEmployer);
                var deletedEmployer = await _databaseService.GetEmployerAsync(insertedId);
                if (deletedEmployer != null)
                {
                    System.Diagnostics.Debug.WriteLine("删除测试失败：数据未被删除");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("读写操作测试全部通过");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读写操作测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试事务操作
        /// </summary>
        private async Task<bool> TestTransactionOperationsAsync()
        {
            try
            {
                // 测试数据重置功能（这是一个复杂的事务操作）
                var beforeCount = (await _databaseService.GetEmployersAsync()).Count;
                
                // 创建一些测试数据
                var testEmployer = new Employer
                {
                    Name = "事务测试雇主",
                    ContactInfo = "<EMAIL>",
                    HourlyRate = 30.0m,
                    PaymentCycleDays = 30,
                    Color = "#00FF00",
                    IsActive = true
                };

                await _databaseService.SaveEmployerAsync(testEmployer);
                
                // 验证数据已插入
                var afterInsertCount = (await _databaseService.GetEmployersAsync()).Count;
                if (afterInsertCount <= beforeCount)
                {
                    System.Diagnostics.Debug.WriteLine("事务测试失败：数据未正确插入");
                    return false;
                }

                // 测试数据清理事务
                var resetSuccess = await _databaseService.ForceCompleteDataClearAsync();
                if (!resetSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("事务测试失败：数据重置失败");
                    return false;
                }

                // 验证数据已清理
                var afterResetCount = (await _databaseService.GetEmployersAsync()).Count;
                if (afterResetCount != 0)
                {
                    System.Diagnostics.Debug.WriteLine($"事务测试失败：数据未完全清理，剩余 {afterResetCount} 条记录");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("事务操作测试通过");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"事务操作测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据库性能
        /// </summary>
        private async Task<bool> TestPerformanceAsync()
        {
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                // 批量插入测试
                var testEmployers = new List<Employer>();
                for (int i = 0; i < 100; i++)
                {
                    testEmployers.Add(new Employer
                    {
                        Name = $"性能测试雇主_{i}",
                        ContactInfo = $"perf{i}@test.com",
                        HourlyRate = 20.0m + i,
                        PaymentCycleDays = 30,
                        Color = "#0000FF",
                        IsActive = true
                    });
                }

                foreach (var employer in testEmployers)
                {
                    await _databaseService.SaveEmployerAsync(employer);
                }

                stopwatch.Stop();
                var insertTime = stopwatch.ElapsedMilliseconds;
                System.Diagnostics.Debug.WriteLine($"插入100条记录耗时: {insertTime}ms");

                // 查询性能测试
                stopwatch.Restart();
                var allEmployers = await _databaseService.GetEmployersAsync();
                stopwatch.Stop();
                var queryTime = stopwatch.ElapsedMilliseconds;
                System.Diagnostics.Debug.WriteLine($"查询{allEmployers.Count}条记录耗时: {queryTime}ms");

                // 清理测试数据
                await _databaseService.ForceCompleteDataClearAsync();

                // 性能标准：插入100条记录应在5秒内完成，查询应在1秒内完成
                var performanceOk = insertTime < 5000 && queryTime < 1000;
                System.Diagnostics.Debug.WriteLine($"性能测试结果: {(performanceOk ? "通过" : "未达标")}");
                
                return performanceOk;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"性能测试失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 数据库测试结果
    /// </summary>
    public class DatabaseTestResult
    {
        public bool ConnectionTest { get; set; }
        public bool FileSystemTest { get; set; }
        public bool SchemaTest { get; set; }
        public bool ReadWriteTest { get; set; }
        public bool TransactionTest { get; set; }
        public bool PerformanceTest { get; set; }
        public bool OverallSuccess { get; set; }
        public string TestLog { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
