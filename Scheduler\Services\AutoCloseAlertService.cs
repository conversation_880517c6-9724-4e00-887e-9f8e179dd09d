// 引入必要的命名空间 / Import necessary namespaces
using System;
using System.Threading.Tasks;
using CommunityToolkit.Maui.Alerts;  // MAUI提示组件 / MAUI alert components
using CommunityToolkit.Maui.Core;    // MAUI核心组件 / MAUI core components

namespace Scheduler.Services
{
    /// <summary>
    /// 自动关闭提示对话框服务接口 / Auto Close Alert Service Interface
    /// 使用 CommunityToolkit.Maui 的 Toast 和 Snackbar 实现5秒自动关闭功能
    /// Uses CommunityToolkit.Maui Toast and Snackbar to implement 5-second auto-close functionality
    /// </summary>
    public interface IAutoCloseAlertService
    {
        /// <summary>
        /// 显示成功消息（5秒后自动关闭） / Show success message (auto-close after 5 seconds)
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="title">标题 / Title</param>
        Task ShowSuccessAsync(string message, string title = "成功");

        /// <summary>
        /// 显示错误消息（5秒后自动关闭） / Show error message (auto-close after 5 seconds)
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="title">标题 / Title</param>
        Task ShowErrorAsync(string message, string title = "错误");

        /// <summary>
        /// 显示信息消息（5秒后自动关闭） / Show info message (auto-close after 5 seconds)
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="title">标题 / Title</param>
        Task ShowInfoAsync(string message, string title = "信息");

        /// <summary>
        /// 显示警告消息（5秒后自动关闭） / Show warning message (auto-close after 5 seconds)
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="title">标题 / Title</param>
        Task ShowWarningAsync(string message, string title = "警告");

        /// <summary>
        /// 显示确认对话框（不自动关闭） / Show confirmation dialog (does not auto-close)
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="title">标题 / Title</param>
        /// <param name="accept">确认按钮文本 / Accept button text</param>
        /// <param name="cancel">取消按钮文本 / Cancel button text</param>
        /// <returns>用户选择结果 / User selection result</returns>
        Task<bool> ShowConfirmAsync(string message, string title = "确认", string accept = "确定", string cancel = "取消");

        /// <summary>
        /// 显示自定义自动关闭Toast / Show custom auto-close Toast
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="duration">显示时长 / Display duration</param>
        Task ShowToastAsync(string message, ToastDuration duration = ToastDuration.Long);

        /// <summary>
        /// 显示自定义自动关闭Snackbar / Show custom auto-close Snackbar
        /// </summary>
        /// <param name="message">消息内容 / Message content</param>
        /// <param name="actionText">操作按钮文本 / Action button text</param>
        /// <param name="action">操作回调 / Action callback</param>
        /// <param name="duration">显示时长 / Display duration</param>
        Task ShowSnackbarAsync(string message, string actionText = "", Action? action = null, TimeSpan? duration = null);
    }

    /// <summary>
    /// 自动关闭提示对话框服务实现
    /// 使用 CommunityToolkit.Maui 的现代化通知组件
    /// </summary>
    public class AutoCloseAlertService : IAutoCloseAlertService
    {
        /// <summary>
        /// 显示成功消息（使用绿色Toast，5秒后自动关闭）
        /// </summary>
        public async Task ShowSuccessAsync(string message, string title = "成功")
        {
            var fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
            await ShowToastAsync(fullMessage, ToastDuration.Long);
        }

        /// <summary>
        /// 显示错误消息（使用红色Snackbar，5秒后自动关闭）
        /// </summary>
        public async Task ShowErrorAsync(string message, string title = "错误")
        {
            var fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
            await ShowSnackbarAsync(fullMessage, duration: TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// 显示信息消息（使用蓝色Toast，5秒后自动关闭）
        /// </summary>
        public async Task ShowInfoAsync(string message, string title = "信息")
        {
            var fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
            await ShowToastAsync(fullMessage, ToastDuration.Long);
        }

        /// <summary>
        /// 显示警告消息（使用橙色Snackbar，5秒后自动关闭）
        /// </summary>
        public async Task ShowWarningAsync(string message, string title = "警告")
        {
            var fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
            await ShowSnackbarAsync(fullMessage, duration: TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// 显示确认对话框（不自动关闭，使用传统DisplayAlert）
        /// </summary>
        public async Task<bool> ShowConfirmAsync(string message, string title = "确认", string accept = "确定", string cancel = "取消")
        {
            try
            {
                if (Application.Current?.MainPage == null)
                    return false;

                return await Application.Current.MainPage.DisplayAlert(title, message, accept, cancel);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示确认对话框失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示自定义自动关闭Toast
        /// </summary>
        public async Task ShowToastAsync(string message, ToastDuration duration = ToastDuration.Long)
        {
            try
            {
                var toast = Toast.Make(message, duration, 14);
                await toast.Show();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示Toast失败: {ex.Message}");
                // 降级到传统DisplayAlert
                await FallbackToDisplayAlert("提示", message);
            }
        }

        /// <summary>
        /// 显示自定义自动关闭Snackbar
        /// </summary>
        public async Task ShowSnackbarAsync(string message, string actionText = "", Action? action = null, TimeSpan? duration = null)
        {
            try
            {
                var snackbarDuration = duration ?? TimeSpan.FromSeconds(5);
                
                var snackbar = string.IsNullOrEmpty(actionText) 
                    ? Snackbar.Make(message, null, "确定", snackbarDuration)
                    : Snackbar.Make(message, action, actionText, snackbarDuration);

                await snackbar.Show();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示Snackbar失败: {ex.Message}");
                // 降级到传统DisplayAlert
                await FallbackToDisplayAlert("提示", message);
            }
        }

        /// <summary>
        /// 降级到传统DisplayAlert（当Toast/Snackbar失败时）
        /// </summary>
        private async Task FallbackToDisplayAlert(string title, string message)
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    await Application.Current.MainPage.DisplayAlert(title, message, "确定");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"降级DisplayAlert也失败: {ex.Message}");
            }
        }
    }
}
