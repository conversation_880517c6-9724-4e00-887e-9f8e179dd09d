using System.Collections.Concurrent;

namespace Scheduler.Services
{
    /// <summary>
    /// 设置变更跟踪器
    /// </summary>
    public class SettingsChangeTracker
    {
        private readonly ConcurrentDictionary<string, object?> _originalValues = new();
        private readonly ConcurrentDictionary<string, object?> _currentValues = new();
        private bool _isInitializing = true;

        /// <summary>
        /// 开始跟踪设置变更
        /// </summary>
        public void StartTracking()
        {
            _isInitializing = false;
        }

        /// <summary>
        /// 停止跟踪设置变更
        /// </summary>
        public void StopTracking()
        {
            _isInitializing = true;
        }

        /// <summary>
        /// 设置原始值
        /// </summary>
        /// <param name="key">设置键</param>
        /// <param name="value">原始值</param>
        public void SetOriginalValue(string key, object? value)
        {
            _originalValues[key] = value;
            _currentValues[key] = value;
        }

        /// <summary>
        /// 更新当前值
        /// </summary>
        /// <param name="key">设置键</param>
        /// <param name="value">当前值</param>
        /// <returns>是否发生了变更</returns>
        public bool UpdateCurrentValue(string key, object? value)
        {
            if (_isInitializing)
            {
                SetOriginalValue(key, value);
                return false;
            }

            _currentValues[key] = value;
            return HasChanged(key);
        }

        /// <summary>
        /// 检查指定设置是否发生变更
        /// </summary>
        /// <param name="key">设置键</param>
        /// <returns>是否发生变更</returns>
        public bool HasChanged(string key)
        {
            if (!_originalValues.TryGetValue(key, out var originalValue) ||
                !_currentValues.TryGetValue(key, out var currentValue))
            {
                return false;
            }

            return !Equals(originalValue, currentValue);
        }

        /// <summary>
        /// 检查是否有任何设置发生变更
        /// </summary>
        /// <returns>是否有变更</returns>
        public bool HasAnyChanges()
        {
            return _originalValues.Keys.Any(HasChanged);
        }

        /// <summary>
        /// 获取所有变更的设置
        /// </summary>
        /// <returns>变更的设置列表</returns>
        public List<SettingChange> GetChanges()
        {
            var changes = new List<SettingChange>();

            foreach (var key in _originalValues.Keys)
            {
                if (HasChanged(key))
                {
                    changes.Add(new SettingChange
                    {
                        Key = key,
                        OriginalValue = _originalValues[key],
                        NewValue = _currentValues[key]
                    });
                }
            }

            return changes;
        }

        /// <summary>
        /// 重置所有变更
        /// </summary>
        public void ResetChanges()
        {
            foreach (var key in _currentValues.Keys.ToList())
            {
                if (_originalValues.TryGetValue(key, out var originalValue))
                {
                    _currentValues[key] = originalValue;
                }
            }
        }

        /// <summary>
        /// 确认所有变更（将当前值设为原始值）
        /// </summary>
        public void CommitChanges()
        {
            foreach (var key in _currentValues.Keys.ToList())
            {
                if (_currentValues.TryGetValue(key, out var currentValue))
                {
                    _originalValues[key] = currentValue;
                }
            }
        }

        /// <summary>
        /// 清除所有跟踪数据
        /// </summary>
        public void Clear()
        {
            _originalValues.Clear();
            _currentValues.Clear();
        }
    }

    /// <summary>
    /// 设置变更信息
    /// </summary>
    public class SettingChange
    {
        public string Key { get; set; } = string.Empty;
        public object? OriginalValue { get; set; }
        public object? NewValue { get; set; }

        public string Description => $"{Key}: {OriginalValue} → {NewValue}";
    }

    /// <summary>
    /// 设置变更事件参数
    /// </summary>
    public class SettingChangedEventArgs : EventArgs
    {
        public string Key { get; set; } = string.Empty;
        public object? OldValue { get; set; }
        public object? NewValue { get; set; }
        public bool RequiresConfirmation { get; set; }
    }
}
