// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections
using System.Globalization;                  // 全球化支持 / Globalization support

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 日历页面视图模型 / Calendar Page ViewModel
    /// 管理日历视图和班次调度功能
    /// Manages calendar views and shift scheduling functionality
    /// </summary>
    public partial class CalendarViewModel : BaseViewModel, IRecipient<DataResetMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知调度器实例（可选） / Notification scheduler instance (optional)
        private readonly NotificationScheduler? _notificationScheduler;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化日历视图模型和相关服务 / Initialize calendar view model and related services
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        /// <param name="notificationScheduler">通知调度器（可选） / Notification scheduler (optional)</param>
        public CalendarViewModel(DatabaseService databaseService, LocalizationService localizationService, NotificationScheduler? notificationScheduler = null)
        {
            _databaseService = databaseService;
            _localizationService = localizationService;
            _notificationScheduler = notificationScheduler;
            Title = "Calendar";

            // 初始化集合 / Initialize collections
            CalendarDays = new ObservableCollection<CalendarDay>();
            WeekShifts = new ObservableCollection<Shift>();
            DayTimeline = new ObservableCollection<TimelineItem>();
            Employers = new ObservableCollection<Employer>();
            Conflicts = new ObservableCollection<Models.ShiftConflict>();

            // 设置初始日期和视图模式 / Set initial date and view mode
            CurrentDate = DateTime.Today;
            ViewMode = CalendarViewMode.Month;

            // 初始化显示属性 / Initialize display properties
            UpdateDisplayProperties();

            // 注册数据重置消息监听 / Register data reset message listener
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
        }

        #region Properties

        [ObservableProperty]
        private CalendarViewMode viewMode;

        [ObservableProperty]
        private DateTime currentDate;

        [ObservableProperty]
        private string displayTitle = string.Empty;

        [ObservableProperty]
        private ObservableCollection<CalendarDay> calendarDays;

        [ObservableProperty]
        private ObservableCollection<Shift> weekShifts;

        [ObservableProperty]
        private ObservableCollection<TimelineItem> dayTimeline;

        [ObservableProperty]
        private ObservableCollection<Employer> employers;

        [ObservableProperty]
        private ObservableCollection<Models.ShiftConflict> conflicts;

        [ObservableProperty]
        private int conflictCount;

        [ObservableProperty]
        private bool isWeekView;

        [ObservableProperty]
        private bool isDayView;

        [ObservableProperty]
        private bool isMonthView = true;

        [ObservableProperty]
        private Employer? selectedEmployerFilter;

        #endregion

        #region Commands

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // Load employers
                var employers = await _databaseService.GetEmployersAsync();
                Employers.Clear();
                foreach (var employer in employers)
                {
                    Employers.Add(employer);
                }

                // Load shifts based on current view mode
                switch (ViewMode)
                {
                    case CalendarViewMode.Month:
                        await LoadMonthDataAsync();
                        break;
                    case CalendarViewMode.Week:
                        await LoadWeekDataAsync();
                        break;
                    case CalendarViewMode.Day:
                        await LoadDayDataAsync();
                        break;
                }

                // Detect conflicts
                await DetectConflictsAsync();
            });
        }

        [RelayCommand]
        private async Task SwitchViewModeAsync()
        {
            // Cycle through Month -> Week -> Day -> Month
            ViewMode = ViewMode switch
            {
                CalendarViewMode.Month => CalendarViewMode.Week,
                CalendarViewMode.Week => CalendarViewMode.Day,
                CalendarViewMode.Day => CalendarViewMode.Month,
                _ => CalendarViewMode.Month
            };

            IsMonthView = ViewMode == CalendarViewMode.Month;
            IsWeekView = ViewMode == CalendarViewMode.Week;
            IsDayView = ViewMode == CalendarViewMode.Day;

            UpdateDisplayProperties();
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task NavigatePreviousAsync()
        {
            CurrentDate = ViewMode switch
            {
                CalendarViewMode.Month => CurrentDate.AddMonths(-1),
                CalendarViewMode.Week => CurrentDate.AddDays(-7),
                CalendarViewMode.Day => CurrentDate.AddDays(-1),
                _ => CurrentDate.AddDays(-1)
            };

            UpdateDisplayProperties();
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task NavigateNextAsync()
        {
            CurrentDate = ViewMode switch
            {
                CalendarViewMode.Month => CurrentDate.AddMonths(1),
                CalendarViewMode.Week => CurrentDate.AddDays(7),
                CalendarViewMode.Day => CurrentDate.AddDays(1),
                _ => CurrentDate.AddDays(1)
            };

            UpdateDisplayProperties();
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task GoToTodayAsync()
        {
            CurrentDate = DateTime.Today;
            UpdateDisplayProperties();
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task AddShiftAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // 导航到班次管理页面
                await Shell.Current.GoToAsync("SimpleShift");
            });
        }

        /// <summary>
        /// 检查班次是否与现有班次冲突
        /// </summary>
        public async Task<bool> CheckShiftConflictAsync(Shift newShift, int? excludeShiftId = null)
        {
            try
            {
                var dayStart = newShift.StartTime.Date;
                var dayEnd = dayStart.AddDays(1);
                var existingShifts = await _databaseService.GetShiftsAsync(dayStart, dayEnd);

                // 排除正在编辑的班次
                if (excludeShiftId.HasValue)
                {
                    existingShifts = existingShifts.Where(s => s.Id != excludeShiftId.Value).ToList();
                }

                // 检查时间冲突
                foreach (var existingShift in existingShifts)
                {
                    if (newShift.ConflictsWith(existingShift))
                    {
                        return true; // 发现冲突
                    }
                }

                return false; // 无冲突
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"检查班次冲突时出错: {ex.Message}");
                return true; // 出错时假设有冲突，防止保存
            }
        }

        /// <summary>
        /// 获取冲突的班次信息
        /// </summary>
        public async Task<List<Shift>> GetConflictingShiftsAsync(Shift newShift, int? excludeShiftId = null)
        {
            try
            {
                var dayStart = newShift.StartTime.Date;
                var dayEnd = dayStart.AddDays(1);
                var existingShifts = await _databaseService.GetShiftsAsync(dayStart, dayEnd);

                // 排除正在编辑的班次
                if (excludeShiftId.HasValue)
                {
                    existingShifts = existingShifts.Where(s => s.Id != excludeShiftId.Value).ToList();
                }

                // 查找冲突的班次
                var conflictingShifts = new List<Shift>();
                foreach (var existingShift in existingShifts)
                {
                    if (newShift.ConflictsWith(existingShift))
                    {
                        // 加载雇主信息
                        var employer = await _databaseService.GetEmployerAsync(existingShift.EmployerId);
                        if (employer != null)
                        {
                            existingShift.EmployerName = employer.Name;
                        }
                        conflictingShifts.Add(existingShift);
                    }
                }

                return conflictingShifts;
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"获取冲突班次时出错: {ex.Message}");
                return new List<Shift>();
            }
        }

        [RelayCommand]
        private async Task AddEmployerAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // 导航到雇主管理页面
                await Shell.Current.GoToAsync("EmployerView");
            });
        }

        [RelayCommand]
        private async Task EditShiftAsync(Shift shift)
        {
            await SafeExecuteAsync(async () =>
            {
                // 导航到班次编辑页面
                await Shell.Current.GoToAsync($"SimpleShift?shiftId={shift.Id}");
            });
        }

        [RelayCommand]
        private async Task DeleteShiftAsync(Shift shift)
        {
            var confirmed = await ShowConfirmAsync(
                $"Are you sure you want to delete the shift '{shift.Description}'?",
                "Delete Shift",
                "Delete",
                "Cancel");

            if (confirmed)
            {
                await SafeExecuteAsync(async () =>
                {
                    // 取消相关通知
                    if (_notificationScheduler != null)
                    {
                        await _notificationScheduler.CancelShiftNotificationsAsync(shift.Id);
                    }

                    await _databaseService.DeleteShiftAsync(shift);
                    await LoadDataAsync();
                    await ShowSuccessAsync("Shift deleted successfully");
                });
            }
        }

        [RelayCommand]
        private async Task FilterByEmployerAsync(Employer? employer)
        {
            SelectedEmployerFilter = employer;
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task ResolveConflictAsync(Models.ShiftConflict conflict)
        {
            await SafeExecuteAsync(async () =>
            {
                // 显示冲突解决选项
                var action = await Application.Current.MainPage.DisplayActionSheet(
                    "Resolve Shift Conflict", "Cancel", null,
                    "Edit New Shift", "Edit Conflicting Shift", "Keep Both");

                if (action == "Edit New Shift")
                {
                    await Shell.Current.GoToAsync($"//SimpleShift?shiftId={conflict.NewShift.Id}");
                }
                else if (action == "Edit Conflicting Shift")
                {
                    await Shell.Current.GoToAsync($"//SimpleShift?shiftId={conflict.ConflictingShift.Id}");
                }
            });
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsRefreshing = true;
            await LoadDataAsync();
            IsRefreshing = false;
        }

        #endregion

        #region Private Methods

        private async Task LoadMonthDataAsync()
        {
            var startOfMonth = new DateTime(CurrentDate.Year, CurrentDate.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
            
            // Get first day of calendar (might be from previous month)
            var startOfCalendar = startOfMonth.AddDays(-(int)startOfMonth.DayOfWeek);
            var endOfCalendar = endOfMonth.AddDays(6 - (int)endOfMonth.DayOfWeek);

            // Load shifts for the entire calendar period
            var shifts = await _databaseService.GetShiftsAsync(startOfCalendar, endOfCalendar);
            
            // Filter by employer if selected
            if (SelectedEmployerFilter != null)
            {
                shifts = shifts.Where(s => s.EmployerId == SelectedEmployerFilter.Id).ToList();
            }

            // Create calendar days
            CalendarDays.Clear();
            var currentDay = startOfCalendar;
            
            while (currentDay <= endOfCalendar)
            {
                var calendarDay = new CalendarDay(currentDay, currentDay.Month == CurrentDate.Month);
                
                // Add shifts for this day
                var dayShifts = shifts.Where(s => s.StartTime.Date == currentDay.Date).ToList();
                foreach (var shift in dayShifts)
                {
                    calendarDay.Shifts.Add(shift);
                }
                
                CalendarDays.Add(calendarDay);
                currentDay = currentDay.AddDays(1);
            }
        }

        private async Task LoadWeekDataAsync()
        {
            // Get start of week (Sunday)
            var startOfWeek = CurrentDate.AddDays(-(int)CurrentDate.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            // Load shifts for the week
            var shifts = await _databaseService.GetShiftsAsync(startOfWeek, endOfWeek.AddDays(1));

            // Filter by employer if selected
            if (SelectedEmployerFilter != null)
            {
                shifts = shifts.Where(s => s.EmployerId == SelectedEmployerFilter.Id).ToList();
            }

            WeekShifts.Clear();
            foreach (var shift in shifts.OrderBy(s => s.StartTime))
            {
                WeekShifts.Add(shift);
            }
        }

        private async Task LoadDayDataAsync()
        {
            // Load shifts for the current day
            var dayStart = CurrentDate.Date;
            var dayEnd = dayStart.AddDays(1);

            var shifts = await _databaseService.GetShiftsAsync(dayStart, dayEnd);

            // Filter by employer if selected
            if (SelectedEmployerFilter != null)
            {
                shifts = shifts.Where(s => s.EmployerId == SelectedEmployerFilter.Id).ToList();
            }

            // Load employer information for shifts
            var employers = await _databaseService.GetEmployersAsync();
            foreach (var shift in shifts)
            {
                var employer = employers.FirstOrDefault(e => e.Id == shift.EmployerId);
                if (employer != null)
                {
                    shift.EmployerName = employer.Name;
                }
            }

            // Create 24-hour timeline with continuous shift blocks
            DayTimeline.Clear();
            for (int hour = 0; hour < 24; hour++)
            {
                var timelineItem = new TimelineItem(hour);

                // Find all shifts that overlap with this hour
                var hourStart = new DateTime(CurrentDate.Year, CurrentDate.Month, CurrentDate.Day, hour, 0, 0);
                var hourEnd = hourStart.AddHours(1);

                var overlappingShifts = shifts.Where(shift =>
                    shift.StartTime < hourEnd && shift.EndTime > hourStart).ToList();

                // Add shift blocks for overlapping shifts
                foreach (var shift in overlappingShifts)
                {
                    timelineItem.AddShiftBlock(shift);
                }

                DayTimeline.Add(timelineItem);
            }

            // Update conflicts count
            await DetectConflictsAsync();
        }

        private async Task DetectConflictsAsync()
        {
            Conflicts.Clear();
            
            var allShifts = ViewMode == CalendarViewMode.Month 
                ? CalendarDays.SelectMany(d => d.Shifts).ToList()
                : WeekShifts.ToList();

            var conflicts = new List<Models.ShiftConflict>();
            
            for (int i = 0; i < allShifts.Count; i++)
            {
                for (int j = i + 1; j < allShifts.Count; j++)
                {
                    var shift1 = allShifts[i];
                    var shift2 = allShifts[j];
                    
                    if (shift1.ConflictsWith(shift2))
                    {
                        var conflict = new Models.ShiftConflict
                        {
                            NewShift = shift1,
                            ConflictingShift = shift2,
                            ConflictType = DetermineConflictType(shift1, shift2),
                            OverlapMinutes = CalculateOverlapMinutes(shift1, shift2)
                        };
                        conflicts.Add(conflict);
                    }
                }
            }

            foreach (var conflict in conflicts)
            {
                Conflicts.Add(conflict);
            }

            ConflictCount = Conflicts.Count;
            
            // Mark calendar days with conflicts
            if (ViewMode == CalendarViewMode.Month)
            {
                foreach (var day in CalendarDays)
                {
                    day.HasConflicts = conflicts.Any(c =>
                        c.NewShift.StartTime.Date == day.Date.Date ||
                        c.ConflictingShift.StartTime.Date == day.Date.Date);
                }
            }
        }

        private void UpdateDisplayProperties()
        {
            DisplayTitle = ViewMode switch
            {
                CalendarViewMode.Month => CurrentDate.ToString("MMMM yyyy", CultureInfo.InvariantCulture),
                CalendarViewMode.Week => GetWeekDisplayTitle(),
                CalendarViewMode.Day => CurrentDate.ToString("dddd, MMMM dd, yyyy", CultureInfo.InvariantCulture),
                _ => CurrentDate.ToString("MMMM yyyy", CultureInfo.InvariantCulture)
            };
        }

        private string GetWeekDisplayTitle()
        {
            var startOfWeek = CurrentDate.AddDays(-(int)CurrentDate.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);
            return $"{startOfWeek:MMM dd} - {endOfWeek:MMM dd, yyyy}";
        }

        /// <summary>
        /// 确定冲突类型
        /// </summary>
        private Models.ConflictType DetermineConflictType(Shift shift1, Shift shift2)
        {
            if (shift1.StartTime == shift2.StartTime && shift1.EndTime == shift2.EndTime)
                return Models.ConflictType.Exact;

            if (shift1.StartTime >= shift2.StartTime && shift1.EndTime <= shift2.EndTime)
                return Models.ConflictType.Contained;

            if (shift2.StartTime >= shift1.StartTime && shift2.EndTime <= shift1.EndTime)
                return Models.ConflictType.Contains;

            return Models.ConflictType.Overlap;
        }

        /// <summary>
        /// 计算重叠分钟数
        /// </summary>
        private int CalculateOverlapMinutes(Shift shift1, Shift shift2)
        {
            var overlapStart = shift1.StartTime > shift2.StartTime ? shift1.StartTime : shift2.StartTime;
            var overlapEnd = shift1.EndTime < shift2.EndTime ? shift1.EndTime : shift2.EndTime;

            if (overlapEnd <= overlapStart)
                return 0;

            return (int)(overlapEnd - overlapStart).TotalMinutes;
        }

        #endregion

        /// <summary>
        /// Called when page appears
        /// </summary>
        public async Task OnAppearingAsync()
        {
            await LoadDataAsync();
        }

        #region Message Handling

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            // 当数据被重置时，刷新日历数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"CalendarViewModel: Received data reset message at {message.ResetTime}");

                // 清空当前数据
                CalendarDays.Clear();
                WeekShifts.Clear();
                DayTimeline.Clear();
                Employers.Clear();
                Conflicts.Clear();

                // 重置显示属性
                UpdateDisplayProperties();

                // 重新加载数据（应该为空）
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine($"CalendarViewModel: Data refreshed after reset - CalendarDays: {CalendarDays.Count}, WeekShifts: {WeekShifts.Count}, Employers: {Employers.Count}");
            });
        }

        #endregion

        #region Localization

        /// <summary>
        /// Localized texts for UI binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new(_localizationService);

        #endregion
    }
}
