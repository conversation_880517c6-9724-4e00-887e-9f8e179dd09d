// 引入数据模型命名空间 / Import data models namespace
using Scheduler.Models;

namespace Scheduler.Services
{
    /// <summary>
    /// 班次管理服务 / Shift Management Service
    /// 负责班次的冲突检测、时间验证和调度管理
    /// Responsible for shift conflict detection, time validation and scheduling management
    /// </summary>
    public class ShiftManagementService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化班次管理服务 / Initialize shift management service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public ShiftManagementService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 检测班次冲突 / Detect shift conflicts
        /// 检查新班次与现有班次的时间冲突
        /// Check time conflicts between new shift and existing shifts
        /// </summary>
        /// <param name="newShift">新班次 / New shift</param>
        /// <returns>冲突列表 / Conflict list</returns>
        public async Task<List<ShiftConflict>> DetectConflictsAsync(Shift newShift)
        {
            var conflicts = new List<ShiftConflict>();
            var existingShifts = await _databaseService.GetConflictingShiftsAsync(newShift);

            foreach (var existingShift in existingShifts)
            {
                var conflict = new ShiftConflict
                {
                    NewShift = newShift,
                    ConflictingShift = existingShift,
                    ConflictType = DetermineConflictType(newShift, existingShift),
                    OverlapMinutes = CalculateOverlapMinutes(newShift, existingShift)
                };

                conflicts.Add(conflict);
            }

            return conflicts;
        }

        /// <summary>
        /// 验证班次时间安排 / Validate shift timing
        /// 检查班次时间的合理性和有效性
        /// Check reasonableness and validity of shift timing
        /// </summary>
        /// <param name="shift">班次对象 / Shift object</param>
        /// <returns>验证结果 / Validation result</returns>
        public async Task<ShiftValidationResult> ValidateShiftTimingAsync(Shift shift)
        {
            var result = new ShiftValidationResult { IsValid = true };

            // 基本时间验证 / Basic time validation
            if (shift.StartTime >= shift.EndTime)
            {
                result.IsValid = false;
                result.Errors.Add("班次结束时间必须晚于开始时间");
            }

            // 检查班次时长是否合理（不超过24小时）
            var duration = shift.EndTime - shift.StartTime;
            if (duration.TotalHours > 24)
            {
                result.IsValid = false;
                result.Errors.Add("班次时长不能超过24小时");
            }

            // 检查是否在过去时间
            if (shift.StartTime < DateTime.Now.AddHours(-1)) // 允许1小时的缓冲
            {
                result.Warnings.Add("班次开始时间已过期");
            }

            // 检查雇主是否存在
            var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
            if (employer == null)
            {
                result.IsValid = false;
                result.Errors.Add($"雇主ID {shift.EmployerId} 不存在");
            }
            else if (!employer.IsActive)
            {
                result.IsValid = false;
                result.Errors.Add($"雇主 {employer.Name} 已被禁用");
            }

            // 检查冲突
            var conflicts = await DetectConflictsAsync(shift);
            if (conflicts.Any())
            {
                result.HasConflicts = true;
                result.Conflicts = conflicts;
                result.Warnings.Add($"检测到 {conflicts.Count} 个时间冲突");
            }

            return result;
        }

        /// <summary>
        /// 自动更新班次状态
        /// </summary>
        public async Task AutoUpdateShiftStatusAsync()
        {
            var now = DateTime.Now;
            var shiftsToUpdate = new List<Shift>();

            // 获取需要更新状态的班次
            var scheduledShifts = await _databaseService.GetShiftsAsync(
                now.AddDays(-1), now.AddDays(1));

            foreach (var shift in scheduledShifts.Where(s => s.Status == ShiftStatus.Scheduled))
            {
                // 如果班次已开始但未标记为进行中
                if (now >= shift.StartTime && now < shift.EndTime)
                {
                    shift.Status = ShiftStatus.InProgress;
                    shiftsToUpdate.Add(shift);
                }
                // 如果班次已结束但未标记为完成
                else if (now >= shift.EndTime)
                {
                    // 检查是否有工作记录
                    var workRecord = await _databaseService.GetWorkRecordByShiftAsync(shift.Id);
                    if (workRecord != null && workRecord.IsCompleted)
                    {
                        shift.Status = ShiftStatus.Completed;
                    }
                    else
                    {
                        // 如果没有工作记录，标记为已取消
                        shift.Status = ShiftStatus.Cancelled;
                    }
                    shiftsToUpdate.Add(shift);
                }
            }

            // 批量更新
            if (shiftsToUpdate.Any())
            {
                await _databaseService.SaveShiftsBatchAsync(shiftsToUpdate);
                System.Diagnostics.Debug.WriteLine($"自动更新了 {shiftsToUpdate.Count} 个班次状态");
            }
        }

        /// <summary>
        /// 创建重复班次
        /// </summary>
        public async Task<List<Shift>> CreateRecurringShiftsAsync(Shift templateShift, int occurrences)
        {
            var shifts = new List<Shift>();
            var currentShift = templateShift;

            for (int i = 0; i < occurrences; i++)
            {
                var newShift = new Shift
                {
                    EmployerId = currentShift.EmployerId,
                    StartTime = currentShift.StartTime,
                    EndTime = currentShift.EndTime,
                    Location = currentShift.Location,
                    Description = currentShift.Description,
                    RecurrenceType = currentShift.RecurrenceType,
                    EstimatedHours = currentShift.EstimatedHours,
                    Status = ShiftStatus.Scheduled
                };

                // 验证班次
                var validation = await ValidateShiftTimingAsync(newShift);
                if (validation.IsValid)
                {
                    shifts.Add(newShift);
                }

                // 计算下一个班次时间
                currentShift = CalculateNextShift(currentShift);
            }

            // 批量保存
            if (shifts.Any())
            {
                await _databaseService.SaveShiftsBatchAsync(shifts);
            }

            return shifts;
        }

        /// <summary>
        /// 计算下一个重复班次
        /// </summary>
        private Shift CalculateNextShift(Shift currentShift)
        {
            var nextShift = new Shift
            {
                EmployerId = currentShift.EmployerId,
                Location = currentShift.Location,
                Description = currentShift.Description,
                RecurrenceType = currentShift.RecurrenceType,
                EstimatedHours = currentShift.EstimatedHours
            };

            switch (currentShift.RecurrenceType)
            {
                case RecurrenceType.Daily:
                    nextShift.StartTime = currentShift.StartTime.AddDays(1);
                    nextShift.EndTime = currentShift.EndTime.AddDays(1);
                    break;
                case RecurrenceType.Weekly:
                    nextShift.StartTime = currentShift.StartTime.AddDays(7);
                    nextShift.EndTime = currentShift.EndTime.AddDays(7);
                    break;
                case RecurrenceType.Monthly:
                    nextShift.StartTime = currentShift.StartTime.AddMonths(1);
                    nextShift.EndTime = currentShift.EndTime.AddMonths(1);
                    break;
                default:
                    throw new ArgumentException("不支持的重复类型");
            }

            return nextShift;
        }

        /// <summary>
        /// 确定冲突类型
        /// </summary>
        private ConflictType DetermineConflictType(Shift shift1, Shift shift2)
        {
            if (shift1.StartTime == shift2.StartTime && shift1.EndTime == shift2.EndTime)
                return ConflictType.Exact;
            
            if (shift1.StartTime >= shift2.StartTime && shift1.EndTime <= shift2.EndTime)
                return ConflictType.Contained;
            
            if (shift2.StartTime >= shift1.StartTime && shift2.EndTime <= shift1.EndTime)
                return ConflictType.Contains;
            
            return ConflictType.Overlap;
        }

        /// <summary>
        /// 计算重叠时间（分钟）
        /// </summary>
        private int CalculateOverlapMinutes(Shift shift1, Shift shift2)
        {
            var overlapStart = shift1.StartTime > shift2.StartTime ? shift1.StartTime : shift2.StartTime;
            var overlapEnd = shift1.EndTime < shift2.EndTime ? shift1.EndTime : shift2.EndTime;
            
            if (overlapStart >= overlapEnd)
                return 0;
            
            return (int)(overlapEnd - overlapStart).TotalMinutes;
        }
    }


}
