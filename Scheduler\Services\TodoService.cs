// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Models;                // 数据模型 / Data models
using System.Collections.ObjectModel; // 可观察集合 / Observable collections

namespace Scheduler.Services
{
    /// <summary>
    /// 待办事项服务 / Todo Service
    /// 管理待办事项的业务逻辑和数据操作
    /// Manages todo items business logic and data operations
    /// </summary>
    public class TodoService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 通知服务实例 / Notification service instance
        private readonly INotificationService _notificationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化待办事项服务 / Initialize todo service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="notificationService">通知服务 / Notification service</param>
        public TodoService(DatabaseService databaseService, INotificationService notificationService)
        {
            _databaseService = databaseService;
            _notificationService = notificationService;
        }

        #region 待办事项基础操作 / Todo Item Basic Operations

        /// <summary>
        /// 获取所有待办事项 / Get all todo items
        /// </summary>
        /// <returns>待办事项列表 / Todo items list</returns>
        public async Task<List<TodoItem>> GetAllTodoItemsAsync()
        {
            return await _databaseService.GetTodoItemsAsync();
        }

        /// <summary>
        /// 获取未完成的待办事项 / Get pending todo items
        /// </summary>
        /// <returns>未完成的待办事项列表 / Pending todo items list</returns>
        public async Task<List<TodoItem>> GetPendingTodoItemsAsync()
        {
            return await _databaseService.GetPendingTodoItemsAsync();
        }

        /// <summary>
        /// 获取已完成的待办事项（最近5条） / Get completed todo items (recent 5)
        /// </summary>
        /// <param name="count">返回数量 / Return count</param>
        /// <returns>已完成的待办事项列表 / Completed todo items list</returns>
        public async Task<List<TodoItem>> GetRecentCompletedTodoItemsAsync(int count = 5)
        {
            return await _databaseService.GetRecentCompletedTodoItemsAsync(count);
        }

        /// <summary>
        /// 获取指定类型的待办事项 / Get todo items by type
        /// </summary>
        /// <param name="type">待办事项类型 / Todo item type</param>
        /// <returns>指定类型的待办事项列表 / Todo items list of specified type</returns>
        public async Task<List<TodoItem>> GetTodoItemsByTypeAsync(TodoItemType type)
        {
            return await _databaseService.GetTodoItemsByTypeAsync(type);
        }

        /// <summary>
        /// 获取逾期的待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetOverdueTodoItemsAsync()
        {
            return await _databaseService.GetOverdueTodoItemsAsync();
        }

        /// <summary>
        /// 保存待办事项（包含通知安排）
        /// </summary>
        public async Task<int> SaveTodoItemAsync(TodoItem todoItem)
        {
            try
            {
                var result = await _databaseService.SaveTodoItemAsync(todoItem);

                // 如果有截止日期且启用了提醒，安排通知
                if (todoItem.DueDate.HasValue && todoItem.IsReminderEnabled && !todoItem.IsCompleted)
                {
                    await _notificationService.ScheduleTodoDeadlineReminderAsync(todoItem);
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 保存待办事项失败 - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 删除待办事项（包含取消通知）
        /// </summary>
        public async Task<int> DeleteTodoItemAsync(int todoItemId)
        {
            try
            {
                // 取消相关通知
                await _notificationService.CancelTodoNotificationsAsync(todoItemId);

                return await _databaseService.DeleteTodoItemAsync(todoItemId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 删除待办事项失败 - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 切换待办事项完成状态（包含通知管理）
        /// </summary>
        public async Task<bool> ToggleTodoItemCompletionAsync(TodoItem todoItem)
        {
            try
            {
                if (todoItem.IsCompleted)
                {
                    todoItem.MarkAsIncomplete();

                    // 如果重新标记为未完成且有截止日期，重新安排通知
                    if (todoItem.DueDate.HasValue && todoItem.IsReminderEnabled)
                    {
                        await _notificationService.ScheduleTodoDeadlineReminderAsync(todoItem);
                    }
                }
                else
                {
                    todoItem.MarkAsCompleted();

                    // 如果标记为完成，取消相关通知
                    await _notificationService.CancelTodoNotificationsAsync(todoItem.Id);
                }

                await _databaseService.SaveTodoItemAsync(todoItem);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 切换完成状态失败 - {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 班次相关待办事项

        /// <summary>
        /// 从班次创建待办事项
        /// </summary>
        public async Task<TodoItem> CreateTodoFromShiftAsync(Shift shift, string? customTitle = null)
        {
            var employer = await _databaseService.GetEmployerAsync(shift.EmployerId);
            var employerName = employer?.Name ?? "未知雇主";

            var todoItem = new TodoItem
            {
                Title = customTitle ?? $"班次工作 - {employerName}",
                Description = $"工作时间: {shift.StartTime:MM/dd HH:mm} - {shift.EndTime:HH:mm}\n地点: {shift.Location ?? "未指定"}\n时薪: ${shift.HourlyRate:F2}",
                Type = TodoItemType.Shift,
                Priority = TodoPriority.Normal,
                DueDate = shift.StartTime,
                RelatedEntityId = shift.Id,
                RelatedEntityType = "Shift",
                EmployerId = shift.EmployerId,
                IsReminderEnabled = true,
                ReminderTime = shift.StartTime.AddMinutes(-30) // 提前30分钟提醒
            };

            await SaveTodoItemAsync(todoItem);
            return todoItem;
        }

        /// <summary>
        /// 批量从班次创建待办事项
        /// </summary>
        public async Task<List<TodoItem>> CreateTodosFromShiftsAsync(List<Shift> shifts)
        {
            var todoItems = new List<TodoItem>();

            foreach (var shift in shifts)
            {
                var todoItem = await CreateTodoFromShiftAsync(shift);
                todoItems.Add(todoItem);
            }

            return todoItems;
        }

        /// <summary>
        /// 获取班次相关的待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetShiftTodoItemsAsync()
        {
            return await GetTodoItemsByTypeAsync(TodoItemType.Shift);
        }

        #endregion

        #region 支付相关待办事项

        /// <summary>
        /// 从支付记录创建待办事项
        /// </summary>
        public async Task<TodoItem> CreateTodoFromPaymentAsync(PaymentRecord payment, string? customTitle = null)
        {
            var employer = await _databaseService.GetEmployerAsync(payment.EmployerId);
            var employerName = employer?.Name ?? "未知雇主";

            var todoItem = new TodoItem
            {
                Title = customTitle ?? $"确认收入 - {employerName}",
                Description = $"工作期间: {payment.PeriodStart:MM/dd} - {payment.PeriodEnd:MM/dd}\n总金额: ${payment.TotalAmount:F2}\n工作时长: {payment.TotalHours:F1}小时",
                Type = TodoItemType.Payment,
                Priority = payment.IsOverdue ? TodoPriority.Urgent : TodoPriority.High,
                DueDate = payment.ExpectedPaymentDate,
                RelatedEntityId = payment.Id,
                RelatedEntityType = "PaymentRecord",
                EmployerId = payment.EmployerId,
                IsReminderEnabled = true,
                ReminderTime = payment.ExpectedPaymentDate?.AddDays(-1) // 提前1天提醒
            };

            await SaveTodoItemAsync(todoItem);
            return todoItem;
        }

        /// <summary>
        /// 获取支付相关的待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetPaymentTodoItemsAsync()
        {
            return await GetTodoItemsByTypeAsync(TodoItemType.Payment);
        }

        /// <summary>
        /// 获取未确认的支付待办事项
        /// </summary>
        public async Task<List<TodoItem>> GetPendingPaymentTodoItemsAsync()
        {
            var paymentTodos = await GetPaymentTodoItemsAsync();
            return paymentTodos.Where(t => !t.IsCompleted).ToList();
        }

        #endregion

        #region 自动化管理

        /// <summary>
        /// 自动从所有未完成班次创建待办事项
        /// </summary>
        public async Task<int> AutoCreateShiftTodoItemsAsync()
        {
            try
            {
                // 获取所有未来的班次
                var upcomingShifts = await _databaseService.GetUpcomingShiftsAsync(30); // 未来30天
                var existingShiftTodos = await GetShiftTodoItemsAsync();
                var existingShiftIds = existingShiftTodos
                    .Where(t => t.RelatedEntityId.HasValue)
                    .Select(t => t.RelatedEntityId.Value)
                    .ToHashSet();

                var createdCount = 0;

                foreach (var shift in upcomingShifts)
                {
                    // 检查是否已经存在对应的待办事项
                    if (!existingShiftIds.Contains(shift.Id))
                    {
                        await CreateTodoFromShiftAsync(shift);
                        createdCount++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"TodoService: 自动创建了 {createdCount} 个班次待办事项");
                return createdCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 自动创建班次待办事项失败 - {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 自动从所有未确认支付记录创建待办事项
        /// </summary>
        public async Task<int> AutoCreatePaymentTodoItemsAsync()
        {
            try
            {
                // 获取所有未确认的支付记录
                var pendingPayments = await _databaseService.GetPendingPaymentRecordsAsync();
                var existingPaymentTodos = await GetPaymentTodoItemsAsync();
                var existingPaymentIds = existingPaymentTodos
                    .Where(t => t.RelatedEntityId.HasValue)
                    .Select(t => t.RelatedEntityId.Value)
                    .ToHashSet();

                var createdCount = 0;

                foreach (var payment in pendingPayments)
                {
                    // 检查是否已经存在对应的待办事项
                    if (!existingPaymentIds.Contains(payment.Id))
                    {
                        await CreateTodoFromPaymentAsync(payment);
                        createdCount++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"TodoService: 自动创建了 {createdCount} 个支付待办事项");
                return createdCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 自动创建支付待办事项失败 - {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 同步待办事项状态（根据关联的实体状态更新）
        /// </summary>
        public async Task<int> SyncTodoItemStatusAsync()
        {
            try
            {
                var allTodos = await GetAllTodoItemsAsync();
                var updatedCount = 0;

                foreach (var todo in allTodos.Where(t => t.RelatedEntityId.HasValue))
                {
                    var shouldUpdate = false;

                    if (todo.RelatedEntityType == "PaymentRecord")
                    {
                        var payment = await _databaseService.GetPaymentRecordAsync(todo.RelatedEntityId.Value);
                        if (payment != null)
                        {
                            var shouldBeCompleted = payment.Status == PaymentStatus.Paid;
                            if (todo.IsCompleted != shouldBeCompleted)
                            {
                                if (shouldBeCompleted)
                                    todo.MarkAsCompleted();
                                else
                                    todo.MarkAsIncomplete();
                                shouldUpdate = true;
                            }
                        }
                    }
                    else if (todo.RelatedEntityType == "Shift")
                    {
                        var shift = await _databaseService.GetShiftAsync(todo.RelatedEntityId.Value);
                        if (shift != null)
                        {
                            var shouldBeCompleted = shift.Status == ShiftStatus.Completed;
                            if (todo.IsCompleted != shouldBeCompleted)
                            {
                                if (shouldBeCompleted)
                                    todo.MarkAsCompleted();
                                else
                                    todo.MarkAsIncomplete();
                                shouldUpdate = true;
                            }
                        }
                    }

                    if (shouldUpdate)
                    {
                        await SaveTodoItemAsync(todo);
                        updatedCount++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"TodoService: 同步了 {updatedCount} 个待办事项状态");
                return updatedCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoService: 同步待办事项状态失败 - {ex.Message}");
                return 0;
            }
        }

        #endregion
    }
}
