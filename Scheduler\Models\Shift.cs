// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 班次状态枚举 / Shift Status Enumeration
    /// 定义班次的各种状态
    /// Defines various states of a shift
    /// </summary>
    /// <remarks>
    /// 用于跟踪班次的生命周期状态，从计划到完成的整个过程
    /// Used to track the lifecycle status of shifts, from planning to completion
    /// </remarks>
    public enum ShiftStatus
    {
        /// <summary>已安排 / Scheduled - 班次已创建但尚未开始 / Shift created but not yet started</summary>
        Scheduled = 0,
        /// <summary>进行中 / In Progress - 班次正在执行 / Shift is currently being executed</summary>
        InProgress = 1,
        /// <summary>已完成 / Completed - 班次已正常结束 / Shift has ended normally</summary>
        Completed = 2,
        /// <summary>已取消 / Cancelled - 班次被取消 / Shift has been cancelled</summary>
        Cancelled = 3
    }

    /// <summary>
    /// 重复类型枚举 / Recurrence Type Enumeration
    /// 定义班次的重复模式
    /// Defines shift recurrence patterns
    /// </summary>
    /// <remarks>
    /// 支持创建重复性班次，减少用户重复输入的工作量
    /// Supports creating recurring shifts to reduce repetitive user input
    /// </remarks>
    public enum RecurrenceType
    {
        /// <summary>无重复 / None - 一次性班次 / One-time shift</summary>
        None = 0,
        /// <summary>每日重复 / Daily - 每天重复 / Repeats daily</summary>
        Daily = 1,
        /// <summary>每周重复 / Weekly - 每周重复 / Repeats weekly</summary>
        Weekly = 2,
        /// <summary>每月重复 / Monthly - 每月重复 / Repeats monthly</summary>
        Monthly = 3
    }

    /// <summary>
    /// 班次信息模型 - 存储工作班次的完整信息
    /// Shift information model - stores complete information about work shifts
    /// </summary>
    /// <remarks>
    /// 该模型包含班次的所有核心信息，包括时间、地点、雇主关联、
    /// 状态管理、重复设置等，是工作调度系统的核心数据结构
    /// </remarks>
    [Table("Shifts")]
    public class Shift
    {
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        [DataAnnotations.Required]
        [Indexed]
        public int EmployerId { get; set; }

        [DataAnnotations.Required]
        [Indexed]
        public DateTime StartTime { get; set; }

        [DataAnnotations.Required]
        public DateTime EndTime { get; set; }

        [SQLite.MaxLength(200)]
        public string? Location { get; set; }

        [SQLite.MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 时薪
        /// </summary>
        [DataAnnotations.Required]
        public decimal HourlyRate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SQLite.MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// 是否为重复班次
        /// </summary>
        public bool IsRecurring { get; set; } = false;

        /// <summary>
        /// 雇主名称（用于显示，不存储在数据库）
        /// </summary>
        [Ignore]
        public string? EmployerName { get; set; }

        /// <summary>
        /// Shift status
        /// </summary>
        [Indexed]
        public ShiftStatus Status { get; set; } = ShiftStatus.Scheduled;

        /// <summary>
        /// Recurrence type
        /// </summary>
        public RecurrenceType RecurrenceType { get; set; } = RecurrenceType.None;

        /// <summary>
        /// Recurrence interval (used with RecurrenceType)
        /// </summary>
        public int RecurrenceInterval { get; set; } = 1;

        /// <summary>
        /// Recurrence end date
        /// </summary>
        public DateTime? RecurrenceEndDate { get; set; }

        /// <summary>
        /// Estimated work hours
        /// </summary>
        public double EstimatedHours { get; set; }

        /// <summary>
        /// 班次提醒提前时间（分钟）
        /// Reminder time in minutes
        /// </summary>
        public int ReminderMinutes { get; set; } = 15;

        /// <summary>
        /// 是否启用班次开始前提醒通知
        /// Whether reminder is enabled
        /// </summary>
        public bool IsReminderEnabled { get; set; } = true;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        #region 通知设置 (Notification Settings)



        /// <summary>
        /// 通知ID（用于取消通知）
        /// Notification ID for cancellation
        /// </summary>
        public int? NotificationId { get; set; }

        #endregion

        /// <summary>
        /// Calculate shift duration in hours
        /// </summary>
        [Ignore]
        public double DurationHours => (EndTime - StartTime).TotalHours;

        /// <summary>
        /// 预计算的时间范围显示（优化性能，避免复杂绑定）
        /// </summary>
        [Ignore]
        public string TimeRangeDisplay => $"{StartTime:HH:mm} - {EndTime:HH:mm}";

        /// <summary>
        /// 预计算的日期时间范围显示（用于即将到来的班次）
        /// </summary>
        [Ignore]
        public string DateTimeRangeDisplay => $"{StartTime:MM/dd} {StartTime:HH:mm} - {EndTime:HH:mm}";

        /// <summary>
        /// 预计算的工作详情显示
        /// </summary>
        [Ignore]
        public string WorkDetailsDisplay => $"Hours: {DurationHours:F1}小时 | Salary / H: ${HourlyRate:F2}";

        /// <summary>
        /// 根据状态返回状态颜色
        /// </summary>
        [Ignore]
        public string StatusColor => Status switch
        {
            ShiftStatus.Scheduled => "#3B82F6",
            ShiftStatus.InProgress => "#10B981",
            ShiftStatus.Completed => "#059669",
            ShiftStatus.Cancelled => "#EF4444",
            _ => "#6B7280"
        };

        /// <summary>
        /// 检查是否有雇主信息
        /// </summary>
        [Ignore]
        public bool HasEmployer => !string.IsNullOrEmpty(EmployerName);

        /// <summary>
        /// 检查是否有地点信息
        /// </summary>
        [Ignore]
        public bool HasLocation => !string.IsNullOrEmpty(Location);

        /// <summary>
        /// Check if this shift conflicts with another shift's time
        /// </summary>
        /// <param name="other">Other shift to check against</param>
        /// <returns>True if there is a time conflict</returns>
        public bool ConflictsWith(Shift other)
        {
            return StartTime < other.EndTime && EndTime > other.StartTime;
        }
    }
}
