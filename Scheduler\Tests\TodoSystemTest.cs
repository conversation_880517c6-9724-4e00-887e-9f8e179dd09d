using System;
using System.Threading.Tasks;
using Scheduler.Services;
using Scheduler.Models;

/// <summary>
/// 模拟通知服务用于测试
/// </summary>
public class MockNotificationService : INotificationService
{
    public Task<bool> RequestNotificationPermissionAsync() => Task.FromResult(true);
    public Task<bool> CheckNotificationPermissionAsync() => Task.FromResult(true);
    public Task ScheduleShiftReminderAsync(Shift shift) => Task.CompletedTask;
    public Task ScheduleDailyWorkScheduleNotificationAsync(DateTime date) => Task.CompletedTask;
    public Task CancelShiftNotificationsAsync(int shiftId) => Task.CompletedTask;
    public Task SendBreakReminderAsync(string message) => Task.CompletedTask;
    public Task SendBreakReminderAsync(WorkRecord workRecord) => Task.CompletedTask;
    public Task SchedulePeriodicBreakRemindersAsync(WorkRecord workRecord) => Task.CompletedTask;
    public Task SchedulePaymentReminderAsync(PaymentRecord payment) => Task.CompletedTask;
    public Task CancelPaymentNotificationsAsync(int paymentId) => Task.CompletedTask;
    public Task SendPaymentDueNotificationAsync(PaymentRecord payment) => Task.CompletedTask;
    public Task SendPaymentConfirmationReminderAsync(PaymentRecord payment) => Task.CompletedTask;
    public Task SchedulePaymentRemindersAsync() => Task.CompletedTask;
    public Task ScheduleTodoDeadlineReminderAsync(TodoItem todoItem) => Task.CompletedTask;
    public Task CancelTodoNotificationsAsync(int todoId) => Task.CompletedTask;
    public Task ScheduleAllTodoRemindersAsync() => Task.CompletedTask;
    public Task SendTodoDeadlineNotificationAsync(TodoItem todoItem) => Task.CompletedTask;
    public Task HandleNotificationClickAsync(int notificationId) => Task.CompletedTask;
    public Task SendShiftConflictNotificationAsync(string message) => Task.CompletedTask;
    public Task SendShiftConflictNotificationAsync(List<ShiftConflict> conflicts) => Task.CompletedTask;
    public Task CancelNotificationAsync(int notificationId) => Task.CompletedTask;
    public Task CancelNotificationAsync(int notificationId, string reason) => Task.CompletedTask;
    public Task CancelAllNotificationsAsync() => Task.CompletedTask;
    public Task<List<NotificationRecord>> GetNotificationHistoryAsync(int limit = 100) => Task.FromResult(new List<NotificationRecord>());
    public Task ClearNotificationHistoryAsync() => Task.CompletedTask;
    public Task MarkNotificationAsReadAsync(int notificationId) => Task.CompletedTask;
    public Task ScheduleAllShiftRemindersAsync() => Task.CompletedTask;
    public Task CleanupExpiredNotificationsAsync() => Task.CompletedTask;
    public Task SendTestNotificationAsync() => Task.CompletedTask;
}

namespace Scheduler.Tests
{
    /// <summary>
    /// TodoList系统测试类
    /// </summary>
    public class TodoSystemTest
    {
        private readonly DatabaseService _databaseService;
        private readonly TodoService _todoService;

        public TodoSystemTest()
        {
            _databaseService = new DatabaseService();
            // 创建一个模拟的通知服务用于测试
            var mockNotificationService = new MockNotificationService();
            _todoService = new TodoService(_databaseService, mockNotificationService);
        }

        /// <summary>
        /// 测试TodoItem基本CRUD操作
        /// </summary>
        public async Task<bool> TestTodoItemCrudAsync()
        {
            try
            {
                // 创建测试TodoItem
                var todoItem = new TodoItem
                {
                    Title = "测试待办事项",
                    Description = "这是一个测试用的待办事项",
                    Type = TodoItemType.General,
                    Priority = TodoPriority.Normal,
                    IsCompleted = false,
                    CreatedAt = DateTime.Now
                };

                // 测试添加
                var addedId = await _databaseService.SaveTodoItemAsync(todoItem);
                if (addedId <= 0)
                {
                    Console.WriteLine("❌ 添加TodoItem失败");
                    return false;
                }
                Console.WriteLine($"✅ 成功添加TodoItem，ID: {addedId}");

                // 测试获取
                var retrievedItem = await _databaseService.GetTodoItemAsync(addedId);
                if (retrievedItem == null || retrievedItem.Title != todoItem.Title)
                {
                    Console.WriteLine("❌ 获取TodoItem失败");
                    return false;
                }
                Console.WriteLine($"✅ 成功获取TodoItem: {retrievedItem.Title}");

                // 测试更新
                retrievedItem.IsCompleted = true;
                retrievedItem.CompletedDate = DateTime.Now;
                var updateResult = await _databaseService.SaveTodoItemAsync(retrievedItem);
                if (updateResult <= 0)
                {
                    Console.WriteLine("❌ 更新TodoItem失败");
                    return false;
                }
                Console.WriteLine("✅ 成功更新TodoItem状态");

                // 测试删除
                var deleteResult = await _databaseService.DeleteTodoItemAsync(addedId);
                if (deleteResult <= 0)
                {
                    Console.WriteLine("❌ 删除TodoItem失败");
                    return false;
                }
                Console.WriteLine("✅ 成功删除TodoItem");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ TodoItem CRUD测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试TodoService自动创建功能
        /// </summary>
        public async Task<bool> TestTodoServiceAutoCreationAsync()
        {
            try
            {
                // 测试自动创建班次相关的待办事项
                var shiftTodoCount = await _todoService.AutoCreateShiftTodoItemsAsync();
                Console.WriteLine($"✅ 自动创建了 {shiftTodoCount} 个班次相关的待办事项");

                // 测试自动创建支付相关的待办事项
                var paymentTodoCount = await _todoService.AutoCreatePaymentTodoItemsAsync();
                Console.WriteLine($"✅ 自动创建了 {paymentTodoCount} 个支付相关的待办事项");

                // 测试状态同步
                var syncCount = await _todoService.SyncTodoItemStatusAsync();
                Console.WriteLine($"✅ 同步了 {syncCount} 个待办事项状态");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ TodoService自动创建测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试待办事项统计功能
        /// </summary>
        public async Task<bool> TestTodoStatisticsAsync()
        {
            try
            {
                // 获取统计信息
                var stats = await _databaseService.GetTodoItemStatisticsAsync();
                Console.WriteLine($"✅ 待办事项统计:");
                Console.WriteLine($"   总数: {stats.Total}");
                Console.WriteLine($"   待完成: {stats.Pending}");
                Console.WriteLine($"   已完成: {stats.Completed}");
                Console.WriteLine($"   逾期: {stats.Overdue}");

                // 获取待完成的待办事项
                var pendingItems = await _databaseService.GetPendingTodoItemsAsync();
                Console.WriteLine($"✅ 获取到 {pendingItems.Count} 个待完成的待办事项");

                // 获取最近完成的待办事项
                var recentCompleted = await _databaseService.GetRecentCompletedTodoItemsAsync(5);
                Console.WriteLine($"✅ 获取到 {recentCompleted.Count} 个最近完成的待办事项");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 待办事项统计测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task<bool> RunAllTestsAsync()
        {
            Console.WriteLine("🚀 开始TodoList系统测试...\n");

            var test1 = await TestTodoItemCrudAsync();
            Console.WriteLine();

            var test2 = await TestTodoServiceAutoCreationAsync();
            Console.WriteLine();

            var test3 = await TestTodoStatisticsAsync();
            Console.WriteLine();

            var allPassed = test1 && test2 && test3;
            
            if (allPassed)
            {
                Console.WriteLine("🎉 所有TodoList系统测试通过！");
            }
            else
            {
                Console.WriteLine("❌ 部分TodoList系统测试失败");
            }

            return allPassed;
        }
    }
}
