// 引入必要的命名空间 / Import necessary namespaces
using Microsoft.Maui.Controls; // MAUI控件 / MAUI controls

namespace Scheduler.Extensions
{
    /// <summary>
    /// 输入视图扩展方法 / Input View Extension Methods
    /// 为输入控件提供额外的功能和便捷操作
    /// Provides additional functionality and convenient operations for input controls
    /// </summary>
    public static class InputViewExtensions
    {
        /// <summary>
        /// 启用键盘自动显示 / Enable Keyboard Auto Show
        /// 确保输入控件能够正常显示键盘
        /// Ensures input controls can properly display keyboard
        /// </summary>
        /// <param name="inputView">输入视图 / Input view</param>
        public static void EnableKeyboardAutoShow(this InputView inputView)
        {
            if (inputView == null) return;

            System.Diagnostics.Debug.WriteLine($"InputView: 启用键盘自动显示 / Enable keyboard auto show - {inputView.GetType().Name}");

            // 确保输入控件处于可用状态 / Ensure input control is in usable state
            inputView.IsEnabled = true;
            if (inputView is Entry entry)
            {
                entry.IsReadOnly = false;
                entry.InputTransparent = false;
            }
            else if (inputView is Editor editor)
            {
                editor.IsReadOnly = false;
                editor.InputTransparent = false;
            }
        }

        /// <summary>
        /// 设置简单焦点 / Set Focus Simple
        /// 在主线程上设置控件焦点
        /// Set control focus on main thread
        /// </summary>
        /// <param name="inputView">输入视图 / Input view</param>
        public static void SetFocusSimple(this InputView inputView)
        {
            if (inputView == null) return;

            System.Diagnostics.Debug.WriteLine($"InputView: 设置焦点 / Set focus - {inputView.GetType().Name}");

            // 在主线程上设置焦点以确保正确执行 / Set focus on main thread to ensure proper execution
            MainThread.BeginInvokeOnMainThread(() =>
            {
                inputView.Focus();
            });
        }

        /// <summary>
        /// 设置光标到末尾 / Set Cursor To End
        /// 将光标位置设置到文本末尾
        /// Set cursor position to the end of text
        /// </summary>
        /// <param name="inputView">输入视图 / Input view</param>
        public static void SetCursorToEnd(this InputView inputView)
        {
            if (inputView == null) return;

            try
            {
                // 使用MAUI特定的方法设置光标位置 / Use MAUI-specific method to set cursor position
                if (inputView is Entry entry)
                {
                    var textLength = entry.Text?.Length ?? 0;
                    entry.CursorPosition = textLength;
                }
                else if (inputView is Editor editor)
                {
                    var textLength = editor.Text?.Length ?? 0;
                    editor.CursorPosition = textLength;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"InputView: 设置光标位置失败 / Set cursor position failed - {ex.Message}");
            }
        }
    }
}
