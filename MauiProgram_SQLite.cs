using Microsoft.Extensions.Logging;
using DishMeAPP.Services;
using DishMeAPP.Data;

namespace DishMeAPP;

/// <summary>
/// MAUI程序配置类 - 配置SQLite数据库和依赖注入
/// MAUI Program Configuration Class - Configure SQLite database and dependency injection
/// </summary>
public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

        // 注册SQLite数据库服务
        // Register SQLite database services
        builder.Services.AddSingleton<RecipeStorageService>();
        
        // 注册数据库上下文（如果需要在其他地方直接使用）
        // Register database context (if needed for direct use elsewhere)
        builder.Services.AddTransient<RecipeDbContext>();

        System.Diagnostics.Debug.WriteLine("SQLite数据库服务已注册到依赖注入容器");

#if DEBUG
        builder.Services.AddLogging(logging =>
        {
            logging.AddDebug();
        });
#endif

        return builder.Build();
    }
}
