// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 工作日志页面视图模型 / Work Log Page ViewModel
    /// 管理工作记录的显示、筛选和统计功能
    /// Manages work record display, filtering and statistics functionality
    /// </summary>
    public partial class LogViewModel : BaseViewModel, IRecipient<EmployerUpdatedMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化工作日志视图模型 / Initialize work log view model
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="localizationService">本地化服务 / Localization service</param>
        public LogViewModel(DatabaseService databaseService, LocalizationService localizationService)
        {
            _databaseService = databaseService;
            _localizationService = localizationService;
            Title = "Work Log";

            // 初始化集合 / Initialize collections
            WorkRecords = new ObservableCollection<WorkRecord>();
            Employers = new ObservableCollection<Employer>();
            FilteredWorkRecords = new ObservableCollection<WorkRecord>();

            SelectedTimeRange = "Today";

            // 监听属性变化以更新Mark按钮可见性 / Listen to property changes to update Mark button visibility
            PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(IsLogConfirmationEnabled))
                {
                    UpdateQuickActionVisibility();
                }
            };

            // 注册消息监听，接收设置变化通知 / Register message listeners for settings change notifications
            WeakReferenceMessenger.Default.Register<LogConfirmationSettingChangedMessage>(this, (r, m) =>
            {
                IsLogConfirmationEnabled = m.IsEnabled;
            });

            // 注册雇主更新消息接收 / Register employer update message reception
            WeakReferenceMessenger.Default.Register(this);
        }

        #region Observable Properties

        /// <summary>
        /// 当前时间
        /// </summary>
        [ObservableProperty]
        private string currentTime = DateTime.Now.ToString("HH:mm");

        /// <summary>
        /// 当前日期
        /// </summary>
        [ObservableProperty]
        private string currentDate = DateTime.Now.ToString("dddd, MMMM dd, yyyy", System.Globalization.CultureInfo.InvariantCulture);

        /// <summary>
        /// 今日已工作时长
        /// </summary>
        [ObservableProperty]
        private double todayWorkedHours;

        /// <summary>
        /// 今日班次数量
        /// </summary>
        [ObservableProperty]
        private int todayShiftsCount;

        /// <summary>
        /// 工作状态文本
        /// </summary>
        [ObservableProperty]
        private string workStatusText = "准备开始工作";

        /// <summary>
        /// 工作记录集合
        /// </summary>
        public ObservableCollection<WorkRecord> WorkRecords { get; }

        /// <summary>
        /// 筛选后的工作记录
        /// </summary>
        public ObservableCollection<WorkRecord> FilteredWorkRecords { get; }

        /// <summary>
        /// 雇主集合
        /// </summary>
        public ObservableCollection<Employer> Employers { get; }

        /// <summary>
        /// 选中的时间范围
        /// </summary>
        [ObservableProperty]
        private string selectedTimeRange;

        /// <summary>
        /// 选中的雇主
        /// </summary>
        [ObservableProperty]
        private Employer? selectedEmployer;

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 是否正在工作
        /// </summary>
        [ObservableProperty]
        private bool isCurrentlyWorking;

        /// <summary>
        /// 当前工作记录
        /// </summary>
        [ObservableProperty]
        private WorkRecord? currentWorkRecord;

        /// <summary>
        /// 是否可以打卡上班
        /// </summary>
        [ObservableProperty]
        private bool canMarkIn = true;

        /// <summary>
        /// 是否可以打卡下班
        /// </summary>
        [ObservableProperty]
        private bool canMarkOut;

        /// <summary>
        /// Log确认功能是否启用
        /// </summary>
        [ObservableProperty]
        private bool isLogConfirmationEnabled = true;

        /// <summary>
        /// 快速操作区域是否可见
        /// </summary>
        [ObservableProperty]
        private bool isQuickActionsVisible = true;

        /// <summary>
        /// 是否有工作记录
        /// </summary>
        [ObservableProperty]
        private bool hasWorkRecords;

        /// <summary>
        /// 空状态文本
        /// </summary>
        [ObservableProperty]
        private string emptyStateText = "暂无工作记录";

        #endregion

        #region Commands

        /// <summary>
        /// 加载数据命令
        /// </summary>
        [RelayCommand]
        private async Task LoadDataAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                // 加载雇主列表
                await LoadEmployersAsync();
                
                // 加载工作记录
                await LoadWorkRecordsAsync();
                
                // 更新今日统计
                await UpdateTodayStatisticsAsync();
                
                // 检查当前工作状态
                await CheckCurrentWorkStatusAsync();
                
                // 应用筛选
                ApplyFilters();
            });
        }

        /// <summary>
        /// 刷新命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsRefreshing = true;
            await LoadDataAsync();
            IsRefreshing = false;
        }

        /// <summary>
        /// 打卡上班命令
        /// </summary>
        [RelayCommand]
        private async Task MarkInAsync()
        {
            if (!CanMarkIn) return;

            await SafeExecuteAsync(async () =>
            {
                // 检查是否有今日的班次安排
                var todayShifts = await _databaseService.GetTodayShiftsAsync();
                var currentShift = todayShifts.FirstOrDefault(s => s.Status == ShiftStatus.Scheduled);

                if (currentShift == null)
                {
                    var createShift = await ShowConfirmAsync(
                        "今日没有安排的班次，是否创建临时工作记录？", 
                        "确认", 
                        "创建", 
                        "取消");
                    
                    if (!createShift) return;
                    
                    // 创建临时班次
                    currentShift = new Shift
                    {
                        EmployerId = Employers.FirstOrDefault()?.Id ?? 1,
                        StartTime = DateTime.Now,
                        EndTime = DateTime.Now.AddHours(8),
                        Location = "临时工作地点",
                        Description = "手动打卡工作",
                        Status = ShiftStatus.InProgress
                    };
                    
                    await _databaseService.SaveShiftAsync(currentShift);
                }

                // 创建工作记录
                var workRecord = new WorkRecord
                {
                    ShiftId = currentShift.Id,
                    ClockInTime = DateTime.Now,
                    ActualLocation = currentShift.Location,
                    Notes = "开始工作",
                    IsOnTime = true
                };

                await _databaseService.SaveWorkRecordAsync(workRecord);
                
                // 更新班次状态
                currentShift.Status = ShiftStatus.InProgress;
                await _databaseService.SaveShiftAsync(currentShift);

                CurrentWorkRecord = workRecord;
                IsCurrentlyWorking = true;
                UpdateWorkStatus();
                
                await LoadDataAsync();
                await ShowSuccessAsync("打卡成功！", "上班打卡");
            });
        }

        /// <summary>
        /// 打卡下班命令
        /// </summary>
        [RelayCommand]
        private async Task MarkOutAsync()
        {
            if (!CanMarkOut || CurrentWorkRecord == null) return;

            await SafeExecuteAsync(async () =>
            {
                CurrentWorkRecord.ClockOutTime = DateTime.Now;
                CurrentWorkRecord.UpdatedAt = DateTime.Now;
                
                // 计算实际工作时长
                if (CurrentWorkRecord.CalculatedHours.HasValue)
                {
                    CurrentWorkRecord.ActualHours = CurrentWorkRecord.CalculatedHours.Value;
                }

                await _databaseService.SaveWorkRecordAsync(CurrentWorkRecord);
                
                // 更新班次状态
                var shift = await _databaseService.GetShiftByIdAsync(CurrentWorkRecord.ShiftId);
                if (shift != null)
                {
                    shift.Status = ShiftStatus.Completed;
                    await _databaseService.SaveShiftAsync(shift);
                }

                CurrentWorkRecord = null;
                IsCurrentlyWorking = false;
                UpdateWorkStatus();
                
                await LoadDataAsync();
                await ShowSuccessAsync("打卡成功！", "下班打卡");
            });
        }

        /// <summary>
        /// 添加笔记命令
        /// </summary>
        [RelayCommand]
        private async Task AddNoteAsync()
        {
            // 这里应该打开一个输入对话框或导航到笔记编辑页面
            await ShowSuccessAsync("添加笔记功能开发中", "提示");
        }

        /// <summary>
        /// 查看记录详情命令
        /// </summary>
        [RelayCommand]
        private async Task ViewRecordDetailAsync(WorkRecord record)
        {
            if (record == null) return;
            
            // 这里应该导航到详情页面或显示详情弹窗
            await ShowSuccessAsync($"查看记录详情：{record.ClockInTime:HH:mm} - {record.ClockOutTime:HH:mm}", "记录详情");
        }

        /// <summary>
        /// 编辑记录命令
        /// </summary>
        [RelayCommand]
        private async Task EditRecordAsync(WorkRecord record)
        {
            if (record == null) return;
            
            // 这里应该导航到编辑页面
            await ShowSuccessAsync($"编辑记录功能开发中", "提示");
        }

        /// <summary>
        /// 删除记录命令
        /// </summary>
        [RelayCommand]
        private async Task DeleteRecordAsync(WorkRecord record)
        {
            if (record == null) return;

            var confirm = await ShowConfirmAsync(
                "确定要删除这条工作记录吗？此操作不可撤销。", 
                "确认删除", 
                "删除", 
                "取消");
            
            if (!confirm) return;

            await SafeExecuteAsync(async () =>
            {
                await _databaseService.DeleteWorkRecordAsync(record.Id);
                await LoadDataAsync();
                await ShowSuccessAsync("记录已删除", "删除成功");
            });
        }

        /// <summary>
        /// 按时间范围筛选命令
        /// </summary>
        [RelayCommand]
        private void FilterByTimeRange(string timeRange)
        {
            SelectedTimeRange = timeRange;
            ApplyFilters();
        }

        /// <summary>
        /// 按雇主筛选命令
        /// </summary>
        [RelayCommand]
        private void FilterByEmployer(Employer? employer)
        {
            SelectedEmployer = employer;
            ApplyFilters();
        }

        /// <summary>
        /// 搜索命令
        /// </summary>
        [RelayCommand]
        private void Search()
        {
            ApplyFilters();
        }

        /// <summary>
        /// 清除筛选命令
        /// </summary>
        [RelayCommand]
        private void ClearFilters()
        {
            SelectedTimeRange = "Today";
            SelectedEmployer = null;
            SearchText = string.Empty;
            ApplyFilters();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 加载雇主列表
        /// </summary>
        private async Task LoadEmployersAsync()
        {
            var employers = await _databaseService.GetEmployersAsync();
            Employers.Clear();
            foreach (var employer in employers)
            {
                Employers.Add(employer);
            }
        }

        /// <summary>
        /// 加载工作记录
        /// </summary>
        private async Task LoadWorkRecordsAsync()
        {
            var records = await _databaseService.GetWorkRecordsAsync();
            WorkRecords.Clear();
            foreach (var record in records.OrderByDescending(r => r.ClockInTime))
            {
                WorkRecords.Add(record);
            }
            HasWorkRecords = WorkRecords.Any();
        }

        /// <summary>
        /// 更新今日统计
        /// </summary>
        private async Task UpdateTodayStatisticsAsync()
        {
            var today = DateTime.Today;
            var todayRecords = WorkRecords.Where(r =>
                r.ClockInTime?.Date == today).ToList();

            TodayShiftsCount = todayRecords.Count;
            TodayWorkedHours = todayRecords.Sum(r => r.FinalHours);
        }

        /// <summary>
        /// 检查当前工作状态
        /// </summary>
        private async Task CheckCurrentWorkStatusAsync()
        {
            var activeRecord = WorkRecords.FirstOrDefault(r =>
                r.ClockInTime.HasValue && !r.ClockOutTime.HasValue);

            if (activeRecord != null)
            {
                CurrentWorkRecord = activeRecord;
                IsCurrentlyWorking = true;
            }
            else
            {
                CurrentWorkRecord = null;
                IsCurrentlyWorking = false;
            }

            UpdateWorkStatus();
        }

        /// <summary>
        /// 更新工作状态
        /// </summary>
        private void UpdateWorkStatus()
        {
            if (IsCurrentlyWorking && CurrentWorkRecord != null)
            {
                var workDuration = DateTime.Now - CurrentWorkRecord.ClockInTime!.Value;
                WorkStatusText = $"正在工作中 ({workDuration.Hours:D2}:{workDuration.Minutes:D2})";
                CanMarkIn = false;
                CanMarkOut = true;
            }
            else
            {
                WorkStatusText = "准备开始工作";
                CanMarkIn = true;
                CanMarkOut = false;
            }

            UpdateQuickActionVisibility();
        }

        /// <summary>
        /// 更新快速操作可见性
        /// </summary>
        private void UpdateQuickActionVisibility()
        {
            IsQuickActionsVisible = IsLogConfirmationEnabled;
        }

        /// <summary>
        /// 应用筛选条件
        /// </summary>
        private void ApplyFilters()
        {
            var filtered = WorkRecords.AsEnumerable();

            // 时间范围筛选
            switch (SelectedTimeRange)
            {
                case "Today":
                    filtered = filtered.Where(r => r.ClockInTime?.Date == DateTime.Today);
                    break;
                case "Week":
                    var weekStart = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                    filtered = filtered.Where(r => r.ClockInTime?.Date >= weekStart);
                    break;
                case "Month":
                    var monthStart = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    filtered = filtered.Where(r => r.ClockInTime?.Date >= monthStart);
                    break;
            }

            // 雇主筛选
            if (SelectedEmployer != null)
            {
                filtered = filtered.Where(r =>
                {
                    // 需要通过ShiftId获取Shift信息来筛选雇主
                    // 这里简化处理，实际应该联表查询
                    return true;
                });
            }

            // 搜索筛选
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                filtered = filtered.Where(r =>
                    (r.ActualLocation?.ToLower().Contains(searchLower) == true) ||
                    (r.Notes?.ToLower().Contains(searchLower) == true) ||
                    (r.TasksCompleted?.ToLower().Contains(searchLower) == true));
            }

            FilteredWorkRecords.Clear();
            foreach (var record in filtered.OrderByDescending(r => r.ClockInTime))
            {
                FilteredWorkRecords.Add(record);
            }

            EmptyStateText = FilteredWorkRecords.Any() ? "" :
                string.IsNullOrWhiteSpace(SearchText) ? "暂无工作记录" : "未找到匹配的记录";
        }

        /// <summary>
        /// 从本地存储加载Log确认设置
        /// </summary>
        private async Task LoadLogConfirmationSettingAsync()
        {
            try
            {
                var setting = await SecureStorage.GetAsync("LogConfirmationEnabled");
                IsLogConfirmationEnabled = bool.Parse(setting ?? "true");
                UpdateQuickActionVisibility();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading log confirmation setting: {ex.Message}");
                IsLogConfirmationEnabled = true;
            }
        }

        /// <summary>
        /// 页面出现时调用
        /// </summary>
        public async Task OnAppearingAsync()
        {
            await LoadLogConfirmationSettingAsync();
            await LoadDataAsync();

            // 启动时间更新定时器
            Application.Current?.Dispatcher.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                var now = DateTime.Now;
                CurrentTime = now.ToString("HH:mm");
                CurrentDate = now.ToString("dddd, MMMM dd, yyyy", System.Globalization.CultureInfo.InvariantCulture);

                // 如果正在工作，更新工作状态
                if (IsCurrentlyWorking)
                {
                    UpdateWorkStatus();
                }

                return true;
            });
        }

        #endregion

        #region Message Handling

        /// <summary>
        /// 接收雇主更新消息
        /// </summary>
        public void Receive(EmployerUpdatedMessage message)
        {
            // 在UI线程上重新加载雇主数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await LoadEmployersAsync();
            });
        }

        #endregion

        /// <summary>
        /// 本地化文本 - Localized texts for XAML binding
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);
    }
}
