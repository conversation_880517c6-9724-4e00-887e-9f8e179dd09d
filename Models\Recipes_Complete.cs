using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DishMeAPP.Models
{
    /// <summary>
    /// Recipe Model Class - Represents complete recipe information
    /// 菜谱模型类 - 表示一个完整的菜谱信息
    /// Enhanced with Entity Framework Core data annotations for SQLite database support
    /// </summary>
    public class Recipe : INotifyPropertyChanged
    {
        /// <summary>
        /// Unique identifier for the recipe - Primary key, auto-increment
        /// 菜谱唯一标识符 - 主键，自动递增
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Recipe name - Required field, maximum 200 characters
        /// 菜谱名称 - 必填字段，最大长度200字符
        /// </summary>
        [Required(ErrorMessage = "Recipe name cannot be empty")]
        [MaxLength(200, ErrorMessage = "Recipe name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Cooking steps - Long text field
        /// 制作步骤 - 长文本字段
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string Steps { get; set; } = string.Empty;

        /// <summary>
        /// Ingredients list - Long text field
        /// 食材清单 - 长文本字段
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string Ingredients { get; set; } = string.Empty;

        /// <summary>
        /// Recipe category - Maximum 100 characters
        /// 菜谱分类 - 最大长度100字符
        /// </summary>
        [MaxLength(100)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Image path - Maximum 500 characters
        /// 图片路径 - 最大长度500字符
        /// </summary>
        [MaxLength(500)]
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// Creation time - Automatically set
        /// 创建时间 - 自动设置
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Update time - Automatically updated
        /// 更新时间 - 自动更新
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Cooking time in minutes - Optional field
        /// 制作时间（分钟）- 可选字段
        /// </summary>
        public int? CookingTimeMinutes { get; set; }

        /// <summary>
        /// Difficulty level (1-5) - Default is 1
        /// 难度等级（1-5）- 默认为1
        /// </summary>
        [Range(1, 5, ErrorMessage = "Difficulty level must be between 1 and 5")]
        public int DifficultyLevel { get; set; } = 1;

        /// <summary>
        /// Number of servings - Default is 1
        /// 份数 - 默认为1
        /// </summary>
        [Range(1, 20, ErrorMessage = "Servings must be between 1 and 20")]
        public int Servings { get; set; } = 1;

        /// <summary>
        /// Is favorite - Default is false
        /// 是否收藏 - 默认为false
        /// </summary>
        public bool IsFavorite { get; set; } = false;

        /// <summary>
        /// Tags - Comma-separated tag list, maximum 500 characters
        /// 标签 - 用逗号分隔的标签列表，最大长度500字符
        /// </summary>
        [MaxLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Nutrition information - Stored in JSON format, long text field
        /// 营养信息 - JSON格式存储，长文本字段
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? NutritionInfo { get; set; }

        // The following methods remain unchanged to ensure UI layer compatibility
        // 以下方法保持不变以确保UI层兼容性

        /// <summary>
        /// Convert to Dish object (for order system)
        /// 转换为Dish对象（用于订单系统）
        /// </summary>
        /// <returns>Corresponding Dish object</returns>
        public Dish ToDish()
        {
            return new Dish
            {
                Id = Id,
                Name = Name,
                Category = GetDishCategory(),
                Image = GetDisplayImagePath(),
                Description = $"{Ingredients}\n\n{Steps}".Trim()
            };
        }

        /// <summary>
        /// Get display name for recipe category
        /// 获取菜谱分类的显示名称
        /// </summary>
        /// <returns>Category display name</returns>
        private string GetDishCategory()
        {
            return string.IsNullOrWhiteSpace(Category) ? "Other" : Category;
        }

        /// <summary>
        /// Get image path for display
        /// 获取显示用的图片路径
        /// </summary>
        /// <returns>Image path</returns>
        private string GetDisplayImagePath()
        {
            // If no image, return default placeholder
            // 如果没有图片，返回默认占位符
            if (string.IsNullOrWhiteSpace(ImagePath))
            {
                return "recipe_placeholder.png"; // Default image placeholder
            }

            return ImagePath;
        }

        // INotifyPropertyChanged implementation
        // INotifyPropertyChanged 实现
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Trigger property change notification
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">Property name</param>
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Set property value and trigger notification
        /// 设置属性值并触发通知
        /// </summary>
        /// <typeparam name="T">Property type</typeparam>
        /// <param name="field">Field reference</param>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Property name</param>
        /// <returns>Whether a change occurred</returns>
        protected bool SetProperty<T>(ref T field, T value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Override ToString method for debugging
        /// 重写ToString方法，用于调试
        /// </summary>
        /// <returns>String representation of the object</returns>
        public override string ToString()
        {
            return $"Recipe: {Name} (ID: {Id}, Category: {Category}, Created: {CreatedAt:yyyy-MM-dd})";
        }

        /// <summary>
        /// Override Equals method
        /// 重写Equals方法
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>Whether objects are equal</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Recipe other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Override GetHashCode method
        /// 重写GetHashCode方法
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        /// <summary>
        /// Validate recipe data integrity
        /// 验证菜谱数据的完整性
        /// </summary>
        /// <returns>Validation result and error messages</returns>
        public (bool IsValid, List<string> Errors) Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Name))
                errors.Add("Recipe name cannot be empty");

            if (Name.Length > 200)
                errors.Add("Recipe name cannot exceed 200 characters");

            if (DifficultyLevel < 1 || DifficultyLevel > 5)
                errors.Add("Difficulty level must be between 1 and 5");

            if (Servings < 1 || Servings > 20)
                errors.Add("Servings must be between 1 and 20");

            if (CookingTimeMinutes.HasValue && CookingTimeMinutes.Value < 0)
                errors.Add("Cooking time cannot be negative");

            if (!string.IsNullOrEmpty(Category) && Category.Length > 100)
                errors.Add("Category cannot exceed 100 characters");

            if (!string.IsNullOrEmpty(ImagePath) && ImagePath.Length > 500)
                errors.Add("Image path cannot exceed 500 characters");

            if (!string.IsNullOrEmpty(Tags) && Tags.Length > 500)
                errors.Add("Tags cannot exceed 500 characters");

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// Create a copy of the recipe
        /// 创建菜谱的副本
        /// </summary>
        /// <returns>Recipe copy</returns>
        public Recipe Clone()
        {
            return new Recipe
            {
                // Note: Don't copy Id, let database auto-generate new ID
                // 注意：不复制Id，让数据库自动生成新的ID
                Name = $"{Name} (Copy)",
                Steps = Steps,
                Ingredients = Ingredients,
                Category = Category,
                ImagePath = ImagePath,
                CookingTimeMinutes = CookingTimeMinutes,
                DifficultyLevel = DifficultyLevel,
                Servings = Servings,
                IsFavorite = false, // Copy is not favorite by default
                Tags = Tags,
                NutritionInfo = NutritionInfo,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// Get formatted cooking time display
        /// 获取格式化的制作时间显示
        /// </summary>
        /// <returns>Formatted cooking time string</returns>
        public string GetFormattedCookingTime()
        {
            if (!CookingTimeMinutes.HasValue || CookingTimeMinutes.Value <= 0)
                return "Not specified";

            var minutes = CookingTimeMinutes.Value;
            if (minutes < 60)
                return $"{minutes} min";

            var hours = minutes / 60;
            var remainingMinutes = minutes % 60;

            if (remainingMinutes == 0)
                return $"{hours} hr";

            return $"{hours} hr {remainingMinutes} min";
        }

        /// <summary>
        /// Get difficulty level display text
        /// 获取难度等级显示文本
        /// </summary>
        /// <returns>Difficulty level text</returns>
        public string GetDifficultyText()
        {
            return DifficultyLevel switch
            {
                1 => "Very Easy",
                2 => "Easy",
                3 => "Medium",
                4 => "Hard",
                5 => "Very Hard",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get tags as a list
        /// 获取标签列表
        /// </summary>
        /// <returns>List of tags</returns>
        public List<string> GetTagsList()
        {
            if (string.IsNullOrWhiteSpace(Tags))
                return new List<string>();

            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToList();
        }

        /// <summary>
        /// Set tags from a list
        /// 从列表设置标签
        /// </summary>
        /// <param name="tagsList">List of tags</param>
        public void SetTagsList(List<string> tagsList)
        {
            if (tagsList == null || tagsList.Count == 0)
            {
                Tags = null;
                return;
            }

            var validTags = tagsList.Where(tag => !string.IsNullOrWhiteSpace(tag))
                                   .Select(tag => tag.Trim())
                                   .Distinct();

            Tags = string.Join(", ", validTags);
        }

        /// <summary>
        /// Check if recipe has a specific tag
        /// 检查菜谱是否包含特定标签
        /// </summary>
        /// <param name="tag">Tag to check</param>
        /// <returns>True if recipe has the tag</returns>
        public bool HasTag(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag) || string.IsNullOrWhiteSpace(Tags))
                return false;

            var tagsList = GetTagsList();
            return tagsList.Any(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get recipe summary for display
        /// 获取菜谱摘要用于显示
        /// </summary>
        /// <returns>Recipe summary</returns>
        public string GetSummary()
        {
            var parts = new List<string>();

            if (!string.IsNullOrWhiteSpace(Category))
                parts.Add($"Category: {Category}");

            parts.Add($"Difficulty: {GetDifficultyText()}");
            parts.Add($"Servings: {Servings}");

            if (CookingTimeMinutes.HasValue)
                parts.Add($"Time: {GetFormattedCookingTime()}");

            return string.Join(" | ", parts);
        }
    }
}
