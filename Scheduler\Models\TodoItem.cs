// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using CommunityToolkit.Mvvm.ComponentModel;                     // MVVM组件模型 / MVVM component model
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 待办事项类型枚举 / Todo Item Type Enumeration
    /// 定义不同类型的待办事项
    /// Defines different types of todo items
    /// </summary>
    public enum TodoItemType
    {
        /// <summary>一般任务 / General Task</summary>
        General = 0,
        /// <summary>班次相关 / Shift Related</summary>
        Shift = 1,
        /// <summary>支付相关 / Payment Related</summary>
        Payment = 2,
        /// <summary>提醒事项 / Reminder Item</summary>
        Reminder = 3
    }

    /// <summary>
    /// 待办事项优先级枚举 / Todo Priority Enumeration
    /// 定义待办事项的优先级级别
    /// Defines priority levels for todo items
    /// </summary>
    public enum TodoPriority
    {
        /// <summary>低优先级 / Low Priority</summary>
        Low = 0,
        /// <summary>普通优先级 / Normal Priority</summary>
        Normal = 1,
        /// <summary>高优先级 / High Priority</summary>
        High = 2,
        /// <summary>紧急 / Urgent</summary>
        Urgent = 3
    }

    /// <summary>
    /// 待办事项模型 / Todo Item Model
    /// 管理所有类型的待办任务
    /// Manages all types of todo tasks
    /// </summary>
    [Table("TodoItems")]
    public partial class TodoItem : ObservableObject
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 待办事项标题 / Todo Item Title
        /// 待办事项的主要标题或名称
        /// Main title or name of the todo item
        /// </summary>
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 待办事项描述
        /// </summary>
        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        [Indexed]
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 待办事项类型
        /// </summary>
        [Indexed]
        public TodoItemType Type { get; set; } = TodoItemType.General;

        /// <summary>
        /// 优先级
        /// </summary>
        public TodoPriority Priority { get; set; } = TodoPriority.Normal;

        /// <summary>
        /// 截止日期
        /// </summary>
        [Indexed]
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// 完成日期
        /// </summary>
        public DateTime? CompletedDate { get; set; }

        /// <summary>
        /// 关联的实体ID（如ShiftId、PaymentRecordId等）
        /// </summary>
        public int? RelatedEntityId { get; set; }

        /// <summary>
        /// 关联的实体类型
        /// </summary>
        [SQLite.MaxLength(50)]
        public string? RelatedEntityType { get; set; }

        /// <summary>
        /// 关联的雇主ID
        /// </summary>
        [Indexed]
        public int? EmployerId { get; set; }

        /// <summary>
        /// 是否启用提醒
        /// </summary>
        public bool IsReminderEnabled { get; set; } = false;

        /// <summary>
        /// 提醒时间
        /// </summary>
        public DateTime? ReminderTime { get; set; }

        /// <summary>
        /// 附加数据（JSON格式）
        /// </summary>
        [SQLite.MaxLength(2000)]
        public string? AdditionalData { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        #region 计算属性

        /// <summary>
        /// 是否逾期
        /// </summary>
        [Ignore]
        public bool IsOverdue
        {
            get
            {
                if (!DueDate.HasValue || IsCompleted)
                    return false;
                return DateTime.Now > DueDate.Value;
            }
        }

        /// <summary>
        /// 逾期天数
        /// </summary>
        [Ignore]
        public int OverdueDays
        {
            get
            {
                if (!IsOverdue) return 0;
                return (DateTime.Now - DueDate!.Value).Days;
            }
        }

        /// <summary>
        /// 优先级颜色
        /// </summary>
        [Ignore]
        public string PriorityColor => Priority switch
        {
            TodoPriority.Low => "#6B7280",
            TodoPriority.Normal => "#3B82F6",
            TodoPriority.High => "#F59E0B",
            TodoPriority.Urgent => "#EF4444",
            _ => "#6B7280"
        };

        /// <summary>
        /// 优先级文本
        /// </summary>
        [Ignore]
        public string PriorityText => Priority switch
        {
            TodoPriority.Low => "Low",
            TodoPriority.Normal => "Normal",
            TodoPriority.High => "High",
            TodoPriority.Urgent => "Urgent",
            _ => "Normal"
        };

        /// <summary>
        /// 类型文本
        /// </summary>
        [Ignore]
        public string TypeText => Type switch
        {
            TodoItemType.General => "General",
            TodoItemType.Shift => "Shift",
            TodoItemType.Payment => "Payment",
            TodoItemType.Reminder => "Reminder",
            _ => "General"
        };

        /// <summary>
        /// 状态文本
        /// </summary>
        [Ignore]
        public string StatusText
        {
            get
            {
                if (IsCompleted)
                    return "Completed";
                if (IsOverdue)
                    return $"Overdue {OverdueDays} days";
                if (DueDate.HasValue)
                {
                    var daysUntilDue = (DueDate.Value - DateTime.Now).Days;
                    if (daysUntilDue == 0)
                        return "Due today";
                    if (daysUntilDue == 1)
                        return "Due tomorrow";
                    if (daysUntilDue > 0)
                        return $"Due in {daysUntilDue} days";
                }
                return "In progress";
            }
        }

        /// <summary>
        /// 状态颜色
        /// </summary>
        [Ignore]
        public string StatusColor
        {
            get
            {
                if (IsCompleted)
                    return "#10B981";
                if (IsOverdue)
                    return "#EF4444";
                if (DueDate.HasValue)
                {
                    var daysUntilDue = (DueDate.Value - DateTime.Now).Days;
                    if (daysUntilDue <= 1)
                        return "#F59E0B";
                }
                return "#3B82F6";
            }
        }

        /// <summary>
        /// 显示文本
        /// </summary>
        [Ignore]
        public string DisplayText => $"{Title} - {TypeText}";

        #endregion

        /// <summary>
        /// 标记为完成
        /// </summary>
        public void MarkAsCompleted()
        {
            IsCompleted = true;
            CompletedDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// 取消完成状态
        /// </summary>
        public void MarkAsIncomplete()
        {
            IsCompleted = false;
            CompletedDate = null;
            UpdatedAt = DateTime.Now;
        }
    }
}
