// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.ViewModels;
using System.ComponentModel;

namespace Scheduler.Views;

/// <summary>
/// 编辑雇主视图 / Edit Employer View
/// 用于编辑现有雇主信息的表单页面
/// Form page for editing existing employer information
/// </summary>
public partial class EditEmployerView : ContentPage
{
    // 编辑雇主视图模型实例 / Edit employer view model instance
    private EditEmployerViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化编辑雇主视图并配置事件监听 / Initialize edit employer view and configure event listening
    /// </summary>
    /// <param name="viewModel">编辑雇主视图模型 / Edit employer view model</param>
    public EditEmployerView(EditEmployerViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = viewModel;

        // 监听视图模型属性变化以处理焦点设置 / Listen to view model property changes for focus handling
        _viewModel.PropertyChanged += OnViewModelPropertyChanged;
    }

    /// <summary>
    /// 处理视图模型属性变化 / Handle view model property changes
    /// 当输入验证失败时自动设置焦点到错误字段 / Automatically set focus to error fields when input validation fails
    /// </summary>
    /// <param name="sender">事件发送者 / Event sender</param>
    /// <param name="e">属性变化事件参数 / Property changed event args</param>
    private void OnViewModelPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        // 当姓名字段背景色变为错误色时设置焦点 / Set focus when name field background color changes to error color
        if (e.PropertyName == nameof(EditEmployerViewModel.NameEntryBackgroundColor))
        {
            if (_viewModel.NameEntryBackgroundColor == Colors.LightPink)
            {
                MainThread.BeginInvokeOnMainThread(() => NameEntry?.Focus());
            }
        }
        // 当时薪字段背景色变为错误色时设置焦点 / Set focus when hourly rate field background color changes to error color
        else if (e.PropertyName == nameof(EditEmployerViewModel.HourlyRateEntryBackgroundColor))
        {
            if (_viewModel.HourlyRateEntryBackgroundColor == Colors.LightPink)
            {
                MainThread.BeginInvokeOnMainThread(() => HourlyRateEntry?.Focus());
            }
        }
        // 当付款周期字段背景色变为错误色时设置焦点 / Set focus when payment cycle field background color changes to error color
        else if (e.PropertyName == nameof(EditEmployerViewModel.PaymentCycleDaysEntryBackgroundColor))
        {
            if (_viewModel.PaymentCycleDaysEntryBackgroundColor == Colors.LightPink)
            {
                MainThread.BeginInvokeOnMainThread(() => PaymentCycleDaysEntry?.Focus());
            }
        }
    }

    /// <summary>
    /// 页面消失时的处理 / Handle page disappearing
    /// 取消事件订阅以防止内存泄漏 / Unsubscribe events to prevent memory leaks
    /// </summary>
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        if (_viewModel != null)
        {
            // 取消属性变化事件订阅 / Unsubscribe property changed event
            _viewModel.PropertyChanged -= OnViewModelPropertyChanged;
        }
    }
}
