// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 通知类型枚举 / Notification Type Enumeration
    /// 定义系统中不同类型的通知
    /// Defines different types of notifications in the system
    /// </summary>
    public enum NotificationType
    {
        WorkReminder = 0,       // 工作提醒 / Work Reminder
        BreakReminder = 1,      // 休息提醒 / Break Reminder
        PaymentDue = 2,         // 支付到期 / Payment Due
        ShiftConflict = 3,      // 班次冲突 / Shift Conflict
        SystemUpdate = 4        // 系统更新 / System Update
    }

    /// <summary>
    /// 通知状态枚举 / Notification Status Enumeration
    /// 定义通知的不同状态
    /// Defines different states of notifications
    /// </summary>
    public enum NotificationStatus
    {
        Pending = 0,    // 待发送 / Pending
        Sent = 1,       // 已发送 / Sent
        Read = 2,       // 已读 / Read
        Dismissed = 3   // 已忽略 / Dismissed
    }

    /// <summary>
    /// 通知记录模型 / Notification Record Model
    /// 用于存储和管理系统通知记录
    /// Used to store and manage system notification records
    /// </summary>
    [Table("NotificationRecords")]
    public class NotificationRecord
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID / User ID
        /// 接收通知的用户标识
        /// Identifier of the user receiving the notification
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 通知类型 / Notification Type
        /// 通知的具体类型分类
        /// Specific type classification of the notification
        /// </summary>
        [Indexed]
        public NotificationType Type { get; set; }

        /// <summary>
        /// 通知标题
        /// </summary>
        [DataAnnotations.Required, SQLite.MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知内容
        /// </summary>
        [SQLite.MaxLength(1000)]
        public string? Message { get; set; }

        /// <summary>
        /// 通知状态
        /// </summary>
        [Indexed]
        public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

        /// <summary>
        /// 计划发送时间
        /// </summary>
        [Indexed]
        public DateTime? ScheduledAt { get; set; }

        /// <summary>
        /// 实际发送时间
        /// </summary>
        public DateTime? SentAt { get; set; }

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadAt { get; set; }

        /// <summary>
        /// 相关实体ID（如ShiftId、PaymentRecordId等）
        /// </summary>
        public int? RelatedEntityId { get; set; }

        /// <summary>
        /// 相关实体类型
        /// </summary>
        [SQLite.MaxLength(50)]
        public string? RelatedEntityType { get; set; }

        /// <summary>
        /// 附加数据（JSON格式）
        /// </summary>
        [SQLite.MaxLength(2000)]
        public string? AdditionalData { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
