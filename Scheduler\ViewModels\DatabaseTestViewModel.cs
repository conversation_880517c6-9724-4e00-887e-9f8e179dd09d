// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using Scheduler.Services;                     // 应用服务 / Application services

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 数据库测试页面视图模型 / Database Test Page ViewModel
    /// 提供数据库连接测试和诊断功能
    /// Provides database connection testing and diagnostic functionality
    /// </summary>
    public partial class DatabaseTestViewModel : ObservableObject
    {
        // 数据库连接测试器实例 / Database connection tester instance
        private readonly DatabaseConnectionTester _connectionTester;
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 演示数据服务实例 / Demo data service instance
        private readonly DemoDataService _demoDataService;
        // 本地化服务实例 / Localization service instance
        private readonly LocalizationService _localizationService;

        // 测试结果文本 / Test results text
        [ObservableProperty]
        private string testResults = "点击'运行测试'开始数据库连接测试...";

        // 测试运行状态 / Test running state
        [ObservableProperty]
        private bool isTestRunning = false;

        // 测试完成状态 / Test completed state
        [ObservableProperty]
        private bool testCompleted = false;

        // 所有测试是否通过 / Whether all tests passed
        [ObservableProperty]
        private bool allTestsPassed = false;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化数据库测试视图模型 / Initialize database test view model
        /// </summary>
        /// <param name="connectionTester">数据库连接测试器 / Database connection tester</param>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="demoDataService">演示数据服务 / Demo data service</param>
        /// <param name="localizationService">本地化服务（可选） / Localization service (optional)</param>
        public DatabaseTestViewModel(DatabaseConnectionTester connectionTester, DatabaseService databaseService, DemoDataService demoDataService, LocalizationService? localizationService = null)
        {
            _connectionTester = connectionTester;
            _databaseService = databaseService;
            _demoDataService = demoDataService;
            _localizationService = localizationService ?? DependencyService.Get<LocalizationService>();
        }

        /// <summary>
        /// 本地化文本属性 / Localized texts property
        /// 提供界面显示的本地化文本 / Provides localized texts for UI display
        /// </summary>
        public LocalizedTexts LocalizedTexts => new LocalizedTexts(_localizationService);

        /// <summary>
        /// 运行数据库连接测试
        /// </summary>
        [RelayCommand]
        private async Task RunDatabaseTestAsync()
        {
            if (IsTestRunning) return;

            try
            {
                IsTestRunning = true;
                TestCompleted = false;
                AllTestsPassed = false;
                TestResults = "正在运行数据库连接测试，请稍候...\n\n";

                // 运行综合测试
                var result = await _connectionTester.RunComprehensiveTestAsync();

                // 更新结果
                TestResults = result.TestLog;
                AllTestsPassed = result.OverallSuccess;
                TestCompleted = true;

                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    TestResults += $"\n\n错误信息: {result.ErrorMessage}";
                }

                // 显示结果摘要
                var summary = $"\n\n=== 测试摘要 ===\n";
                summary += $"连接测试: {(result.ConnectionTest ? "✅" : "❌")}\n";
                summary += $"文件系统测试: {(result.FileSystemTest ? "✅" : "❌")}\n";
                summary += $"表结构测试: {(result.SchemaTest ? "✅" : "❌")}\n";
                summary += $"读写操作测试: {(result.ReadWriteTest ? "✅" : "❌")}\n";
                summary += $"事务操作测试: {(result.TransactionTest ? "✅" : "❌")}\n";
                summary += $"性能测试: {(result.PerformanceTest ? "✅" : "❌")}\n";
                summary += $"\n总体结果: {(result.OverallSuccess ? "✅ 全部通过" : "❌ 存在问题")}";

                TestResults += summary;
            }
            catch (Exception ex)
            {
                TestResults = $"测试过程中发生异常:\n{ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}";
                AllTestsPassed = false;
                TestCompleted = true;
            }
            finally
            {
                IsTestRunning = false;
            }
        }

        /// <summary>
        /// 运行重置功能测试
        /// </summary>
        [RelayCommand]
        private async Task RunResetTestAsync()
        {
            if (IsTestRunning) return;

            try
            {
                IsTestRunning = true;
                TestResults = "正在测试数据重置功能...\n\n";

                // 1. 创建测试数据
                TestResults += "1. 创建测试数据...\n";
                var testEmployer = new Models.Employer
                {
                    Name = "重置测试雇主",
                    ContactInfo = "<EMAIL>",
                    HourlyRate = 25.0m,
                    PaymentCycleDays = 30,
                    Color = "#FF0000",
                    IsActive = true
                };

                var employerId = await _databaseService.SaveEmployerAsync(testEmployer);
                TestResults += $"   ✅ 创建测试雇主，ID: {employerId}\n\n";

                // 2. 验证数据存在
                TestResults += "2. 验证数据存在...\n";
                var employers = await _databaseService.GetEmployersAsync();
                TestResults += $"   当前雇主数量: {employers.Count}\n\n";

                // 3. 执行重置
                TestResults += "3. 执行数据重置...\n";

                // 设置用户手动重置标记，防止自动创建演示数据
                _demoDataService.SetUserManualResetFlag();
                TestResults += "   已设置手动重置标记，防止自动创建演示数据\n";

                var resetSuccess = await _databaseService.ForceCompleteDataClearAsync();
                TestResults += $"   重置结果: {(resetSuccess ? "✅ 成功" : "❌ 失败")}\n\n";

                // 4. 验证重置结果
                TestResults += "4. 验证重置结果...\n";
                var employersAfterReset = await _databaseService.GetEmployersAsync();
                var startDate = DateTime.Today.AddDays(-30);
                var endDate = DateTime.Today.AddDays(30);
                var shiftsAfterReset = await _databaseService.GetShiftsAsync(startDate, endDate);
                var workRecordsAfterReset = await _databaseService.GetWorkRecordsAsync();
                var paymentRecordsAfterReset = await _databaseService.GetPaymentRecordsAsync();

                TestResults += $"   重置后雇主数量: {employersAfterReset.Count}\n";
                TestResults += $"   重置后班次数量: {shiftsAfterReset.Count}\n";
                TestResults += $"   重置后工作记录数量: {workRecordsAfterReset.Count}\n";
                TestResults += $"   重置后薪资记录数量: {paymentRecordsAfterReset.Count}\n\n";

                // 5. 验证演示数据防护机制
                TestResults += "5. 验证演示数据防护机制...\n";
                var shouldCreateDemo = await _demoDataService.ShouldCreateDemoDataAsync();
                var hasResetFlag = _demoDataService.HasUserManualResetFlag();

                TestResults += $"   是否应创建演示数据: {(shouldCreateDemo ? "是" : "否")}\n";
                TestResults += $"   是否存在重置标记: {(hasResetFlag ? "是" : "否")}\n";

                bool resetProtectionWorking = !shouldCreateDemo && hasResetFlag;
                TestResults += $"   重置防护机制: {(resetProtectionWorking ? "✅ 正常工作" : "❌ 存在问题")}\n\n";

                // 6. 测试结果
                var allDataCleared = employersAfterReset.Count == 0 &&
                                   shiftsAfterReset.Count == 0 &&
                                   workRecordsAfterReset.Count == 0 &&
                                   paymentRecordsAfterReset.Count == 0;

                TestResults += "=== 重置测试结果 ===\n";
                TestResults += $"数据重置: {(resetSuccess ? "✅" : "❌")}\n";
                TestResults += $"数据清理: {(allDataCleared ? "✅" : "❌")}\n";
                TestResults += $"防护机制: {(resetProtectionWorking ? "✅" : "❌")}\n";
                TestResults += $"总体结果: {(resetSuccess && allDataCleared && resetProtectionWorking ? "✅ 重置功能正常" : "❌ 重置功能异常")}\n";

                if (resetSuccess && allDataCleared && resetProtectionWorking)
                {
                    TestResults += "\n🎉 修复验证成功！\n";
                    TestResults += "- 数据库已完全清空\n";
                    TestResults += "- 演示数据防护机制正常工作\n";
                    TestResults += "- 首页应显示空数据状态\n";
                    TestResults += "- Pay页面应显示空薪资数据\n";
                    TestResults += "- 不会自动创建演示数据\n";
                }

                AllTestsPassed = resetSuccess && allDataCleared && resetProtectionWorking;
                TestCompleted = true;
            }
            catch (Exception ex)
            {
                TestResults += $"\n❌ 重置测试失败: {ex.Message}";
                AllTestsPassed = false;
                TestCompleted = true;
            }
            finally
            {
                IsTestRunning = false;
            }
        }

        /// <summary>
        /// 重新启用演示数据创建
        /// </summary>
        [RelayCommand]
        private async Task EnableDemoDataAsync()
        {
            try
            {
                _demoDataService.ClearUserManualResetFlag();
                TestResults += "\n✅ 已清除重置标记，演示数据创建已重新启用\n";
                TestResults += "下次访问首页时，如果数据库为空，将自动创建演示数据\n";
                TestResults += "演示数据将包括：雇主、班次、工作记录和薪资记录\n";
            }
            catch (Exception ex)
            {
                TestResults += $"\n❌ 启用演示数据失败: {ex.Message}\n";
            }
        }

        /// <summary>
        /// 清空测试结果
        /// </summary>
        [RelayCommand]
        private void ClearResults()
        {
            TestResults = "点击'运行测试'开始数据库连接测试...";
            TestCompleted = false;
            AllTestsPassed = false;
        }

        /// <summary>
        /// 返回设置页面
        /// </summary>
        [RelayCommand]
        private async Task GoBackAsync()
        {
            await Shell.Current.GoToAsync("..");
        }
    }
}
