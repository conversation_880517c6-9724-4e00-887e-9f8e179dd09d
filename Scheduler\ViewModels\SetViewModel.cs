using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Plugin.LocalNotification;
using Scheduler.Models;
using Scheduler.Services;
using System.Globalization;

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 设置页面视图模型 - 管理应用程序的所有配置和偏好设置
    /// Settings page ViewModel - manages all application configurations and preferences
    /// </summary>
    /// <remarks>
    /// 该ViewModel负责处理设置页面的所有功能，包括：
    /// - 用户个人资料管理
    /// - 通知设置和权限管理
    /// - 语言切换和本地化
    /// - 数据导出和备份功能
    /// - 时区和时间格式设置
    /// - 日历集成和闹钟设置
    /// - 设置变更追踪和确认机制
    /// </remarks>
    public partial class SetViewModel : BaseViewModel
    {
        // 服务依赖项 - 使用可空类型支持可选服务注入
        private readonly DataValidationService? _validationService;
        private readonly NotificationService? _notificationService;
        private readonly NotificationSettingsService? _notificationSettingsService;
        private readonly PermissionService? _permissionService;
        private readonly NotificationTestService? _notificationTestService;
        private readonly BackupService? _backupService;
        private readonly DatabaseService _databaseService; // 必需服务
        private readonly LocalizationService _localizationService; // 本地化服务
        private readonly SettingsChangeTracker _changeTracker; // 设置变更追踪器

        public SetViewModel(
            DatabaseService databaseService,
            DataValidationService? validationService = null,
            NotificationService? notificationService = null,
            NotificationSettingsService? notificationSettingsService = null,
            PermissionService? permissionService = null,
            NotificationTestService? notificationTestService = null,
            BackupService? backupService = null)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _validationService = validationService;
            _notificationService = notificationService;
            _notificationSettingsService = notificationSettingsService;
            _permissionService = permissionService;
            _notificationTestService = notificationTestService;
            _backupService = backupService;
            _localizationService = LocalizationService.Instance;
            _changeTracker = new SettingsChangeTracker();

            Title = _localizationService.GetLocalizedString("Settings");
            LoadUserProfile();
            LoadSettings();

            // 监听本地化变更
            _localizationService.PropertyChanged += OnLocalizationChanged;

            // 监听属性变化
            PropertyChanged += OnPropertyChanged;

            // 初始化权限状态
            _ = Task.Run(async () => await LoadPermissionStatusAsync());
        }

        /// <summary>
        /// 属性变化事件处理
        /// </summary>
        private async void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 只有在用户实际修改设置时才处理变更
            if (_changeTracker.UpdateCurrentValue(e.PropertyName ?? string.Empty, GetPropertyValue(e.PropertyName)))
            {
                switch (e.PropertyName)
                {
                    // TEMPORARILY DISABLED: Log Confirmation
                    /*
                    case nameof(IsLogConfirmationEnabled):
                        await SaveLogConfirmationSetting();
                        break;
                    */
                    case nameof(IsPayConfirmationEnabled):
                        await SavePayConfirmationSetting();
                        break;
                    case nameof(IsWorkReminderEnabled):
                        await SaveWorkReminderSetting();
                        break;
                    case nameof(IsBreakReminderEnabled):
                        await SaveBreakReminderSetting();
                        break;
                    case nameof(IsPaymentReminderEnabled):
                        await SavePaymentReminderSetting();
                        break;
                    case nameof(CurrentLanguage):
                        await SaveLanguageSetting();
                        break;
                }
            }
        }

        /// <summary>
        /// 本地化变更事件处理
        /// </summary>
        private void OnLocalizationChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // 更新所有本地化文本
            Title = _localizationService.GetLocalizedString("Settings");
            OnPropertyChanged(nameof(LocalizedTexts));
        }

        #region Observable Properties

        /// <summary>
        /// 用户头像URL
        /// </summary>
        [ObservableProperty]
        private string userAvatarUrl = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        [ObservableProperty]
        private string userName = "User";

        /// <summary>
        /// 用户邮箱
        /// </summary>
        [ObservableProperty]
        private string userEmail = string.Empty;

        /// <summary>
        /// 注册时间
        /// </summary>
        [ObservableProperty]
        private string memberSince = $"Member since {DateTime.Now:MMM yyyy}";

        // TEMPORARILY DISABLED: Log Confirmation Property
        /*
        /// <summary>
        /// Log确认开关状态
        /// </summary>
        [ObservableProperty]
        private bool isLogConfirmationEnabled;
        */

        /// <summary>
        /// Pay确认开关状态
        /// </summary>
        [ObservableProperty]
        private bool isPayConfirmationEnabled;

        /// <summary>
        /// 工作提醒开关状态
        /// </summary>
        [ObservableProperty]
        private bool isWorkReminderEnabled;

        /// <summary>
        /// 休息提醒开关状态
        /// </summary>
        [ObservableProperty]
        private bool isBreakReminderEnabled;

        /// <summary>
        /// 工资确认提醒开关状态
        /// </summary>
        [ObservableProperty]
        private bool isPaymentReminderEnabled;

        #region 新增通知设置属性

        /// <summary>
        /// 班次提醒提前时间（分钟）
        /// </summary>
        [ObservableProperty]
        private int shiftReminderMinutes = 15;

        /// <summary>
        /// 是否启用每日工作安排通知
        /// </summary>
        [ObservableProperty]
        private bool isDailyScheduleEnabled = true;

        /// <summary>
        /// 每日工作安排通知时间
        /// </summary>
        [ObservableProperty]
        private TimeSpan dailyScheduleTime = new TimeSpan(8, 0, 0);

        /// <summary>
        /// 休息提醒间隔（小时）
        /// </summary>
        [ObservableProperty]
        private int breakReminderInterval = 2;

        /// <summary>
        /// 是否启用声音
        /// </summary>
        [ObservableProperty]
        private bool isSoundEnabled = true;

        /// <summary>
        /// 是否启用振动
        /// </summary>
        [ObservableProperty]
        private bool isVibrationEnabled = true;

        /// <summary>
        /// 是否启用免打扰时间
        /// </summary>
        [ObservableProperty]
        private bool isQuietHoursEnabled = false;

        /// <summary>
        /// 免打扰开始时间
        /// </summary>
        [ObservableProperty]
        private TimeSpan quietHoursStart = new TimeSpan(22, 0, 0);

        /// <summary>
        /// 免打扰结束时间
        /// </summary>
        [ObservableProperty]
        private TimeSpan quietHoursEnd = new TimeSpan(7, 0, 0);

        /// <summary>
        /// 免打扰时间文本显示
        /// </summary>
        public string QuietHoursText => $"{QuietHoursStart:hh\\:mm} - {QuietHoursEnd:hh\\:mm}";

        #endregion

        #region 权限管理属性

        /// <summary>
        /// 通知权限状态文本
        /// </summary>
        [ObservableProperty]
        private string notificationPermissionStatus = "未知";

        /// <summary>
        /// 通知权限状态颜色
        /// </summary>
        [ObservableProperty]
        private string notificationPermissionColor = "#666666";

        /// <summary>
        /// 位置权限状态文本
        /// </summary>
        [ObservableProperty]
        private string locationPermissionStatus = "未知";

        /// <summary>
        /// 位置权限状态颜色
        /// </summary>
        [ObservableProperty]
        private string locationPermissionColor = "#666666";

        #endregion

        /// <summary>
        /// 当前时区
        /// </summary>
        [ObservableProperty]
        private string currentTimeZone = "Sydney";

        /// <summary>
        /// 时间格式（24小时制）
        /// </summary>
        [ObservableProperty]
        private bool is24HourFormat = true;

        /// <summary>
        /// 时间格式显示文本
        /// </summary>
        [ObservableProperty]
        private string timeFormatText = "24 Hour";

        /// <summary>
        /// 当前语言
        /// </summary>
        [ObservableProperty]
        private string currentLanguage = "English";

        /// <summary>
        /// 应用版本
        /// </summary>
        [ObservableProperty]
        private string appVersion = "1.0.0";

        /// <summary>
        /// 当前语言代码
        /// </summary>
        [ObservableProperty]
        private string currentLanguageCode = "en";

        /// <summary>
        /// 本地化文本集合
        /// </summary>
        public LocalizedTexts LocalizedTexts => new(_localizationService);

        #endregion

        #region Commands

        /// <summary>
        /// 编辑个人资料命令
        /// </summary>
        [RelayCommand]
        private async Task EditProfileAsync()
        {
            await ShowSuccessAsync("Profile editing feature coming soon!", "Edit Profile");
        }

        // TEMPORARILY DISABLED: Log Confirmation Methods
        /*
        /// <summary>
        /// 保存Log确认设置
        /// </summary>
        private async Task SaveLogConfirmationSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 保存设置到本地存储
                await SecureStorage.SetAsync("LogConfirmationEnabled", IsLogConfirmationEnabled.ToString());

                // 发送消息通知其他页面设置已更改
                WeakReferenceMessenger.Default.Send(new LogConfirmationSettingChangedMessage(IsLogConfirmationEnabled));

                var status = IsLogConfirmationEnabled ? "enabled" : "disabled";
                await ShowSuccessAsync($"Log confirmation {status}", "Settings Updated");
            });
        }

        /// <summary>
        /// 保存Log确认设置（公共方法，供UI调用）
        /// </summary>
        public async Task SaveLogConfirmationSettingAsync(bool isEnabled)
        {
            IsLogConfirmationEnabled = isEnabled;
            await SaveLogConfirmationSetting();
        }
        */

        /// <summary>
        /// 保存Pay确认设置
        /// </summary>
        private async Task SavePayConfirmationSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 保存设置到本地存储
                await SecureStorage.SetAsync("PayConfirmationEnabled", IsPayConfirmationEnabled.ToString());

                var status = IsPayConfirmationEnabled ? "enabled" : "disabled";
                await ShowSuccessAsync($"Pay confirmation {status}", "Settings Updated");
            });
        }

        /// <summary>
        /// 保存工作提醒设置
        /// </summary>
        private async Task SaveWorkReminderSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 保存设置到本地存储
                await SecureStorage.SetAsync("WorkReminderEnabled", IsWorkReminderEnabled.ToString());

                // 配置本地通知
                await ConfigureWorkReminders();

                var status = IsWorkReminderEnabled ? "enabled" : "disabled";
                await ShowSuccessAsync($"Work reminders {status}", "Notification Settings");
            });
        }

        /// <summary>
        /// 保存休息提醒设置
        /// </summary>
        private async Task SaveBreakReminderSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 保存设置到本地存储
                await SecureStorage.SetAsync("BreakReminderEnabled", IsBreakReminderEnabled.ToString());

                // 配置本地通知
                await ConfigureBreakReminders();

                var status = IsBreakReminderEnabled ? "enabled" : "disabled";
                await ShowSuccessAsync($"Break reminders {status}", "Notification Settings");
            });
        }

        /// <summary>
        /// 保存工资确认提醒设置
        /// </summary>
        private async Task SavePaymentReminderSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 保存设置到本地存储
                await SecureStorage.SetAsync("PaymentReminderEnabled", IsPaymentReminderEnabled.ToString());

                // 配置本地通知
                await ConfigurePaymentReminders();

                var status = IsPaymentReminderEnabled ? "enabled" : "disabled";
                await ShowSuccessAsync($"Payment reminders {status}", "Notification Settings");
            });
        }

        /// <summary>
        /// 时区设置命令
        /// </summary>
        [RelayCommand]
        private async Task SelectTimeZoneAsync()
        {
            var timeZones = new[] { "Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide", "Darwin", "Hobart" };
            var result = await Application.Current!.MainPage!.DisplayActionSheet(
                "Select Time Zone", "Cancel", null, timeZones);

            if (!string.IsNullOrEmpty(result) && result != "Cancel")
            {
                CurrentTimeZone = result;
                await SecureStorage.SetAsync("TimeZone", result);
                await ShowSuccessAsync($"Time zone set to {result}", "Settings Updated");
            }
        }

        /// <summary>
        /// 时间格式设置命令
        /// </summary>
        [RelayCommand]
        private async Task ToggleTimeFormatAsync()
        {
            Is24HourFormat = !Is24HourFormat;
            TimeFormatText = Is24HourFormat ? "24 Hour" : "12 Hour";
            
            await SecureStorage.SetAsync("Is24HourFormat", Is24HourFormat.ToString());
            await ShowSuccessAsync($"Time format set to {TimeFormatText}", "Settings Updated");
        }

        /// <summary>
        /// 语言设置命令
        /// </summary>
        [RelayCommand]
        private async Task SelectLanguageAsync()
        {
            var supportedLanguages = _localizationService.GetSupportedLanguages();
            var languageNames = supportedLanguages.Select(l => l.DisplayName).ToArray();

            var result = await Application.Current!.MainPage!.DisplayActionSheet(
                _localizationService.GetLocalizedString("SelectLanguage"),
                _localizationService.GetLocalizedString("Cancel"),
                null,
                languageNames);

            if (!string.IsNullOrEmpty(result) && result != _localizationService.GetLocalizedString("Cancel"))
            {
                var selectedLanguage = supportedLanguages.FirstOrDefault(l => l.DisplayName == result);
                if (selectedLanguage != null)
                {
                    CurrentLanguageCode = selectedLanguage.Code;
                    CurrentLanguage = selectedLanguage.NativeName;

                    // 应用语言变更
                    _localizationService.SetLanguage(selectedLanguage.Code);

                    // 保存到本地存储
                    await SecureStorage.SetAsync("LanguageCode", selectedLanguage.Code);
                    await SecureStorage.SetAsync("Language", selectedLanguage.NativeName);

                    // 显示成功消息
                    var message = $"{_localizationService.GetLocalizedString("Language")} {_localizationService.GetLocalizedString("SettingsUpdated")}";
                    await ShowSuccessAsync(message, _localizationService.GetLocalizedString("SettingsUpdated"));
                }
            }
        }

        /// <summary>
        /// 保存语言设置
        /// </summary>
        private async Task SaveLanguageSetting()
        {
            await SafeExecuteAsync(async () =>
            {
                // 应用语言变更
                _localizationService.SetLanguage(CurrentLanguageCode);

                // 保存到本地存储
                await SecureStorage.SetAsync("LanguageCode", CurrentLanguageCode);
                await SecureStorage.SetAsync("Language", CurrentLanguage);

                // 显示成功消息
                var message = $"{_localizationService.GetLocalizedString("Language")} {_localizationService.GetLocalizedString("SettingsUpdated")}";
                await ShowSuccessAsync(message, _localizationService.GetLocalizedString("SettingsUpdated"));
            });
        }

        /// <summary>
        /// 闹钟设置命令
        /// </summary>
        [RelayCommand]
        private async Task OpenAlarmSettingsAsync()
        {
            try
            {
                // 尝试打开系统闹钟应用
                var uri = DeviceInfo.Platform == DevicePlatform.iOS 
                    ? "clock-alarm://" 
                    : "android.intent.action.SET_ALARM";
                
                await Launcher.OpenAsync(uri);
            }
            catch
            {
                await ShowErrorAsync("Unable to open alarm settings. Please open your phone's alarm app manually.", "System Integration");
            }
        }

        /// <summary>
        /// 日历导出命令
        /// </summary>
        [RelayCommand]
        private async Task ExportToCalendarAsync()
        {
            await ShowSuccessAsync("Calendar export feature coming soon!", "Export to Calendar");
        }

        /// <summary>
        /// 数据导出命令
        /// </summary>
        [RelayCommand]
        private async Task ExportDataAsync()
        {
            await ShowSuccessAsync("Data export feature coming soon!", "Export Data");
        }

        /// <summary>
        /// 备份同步命令
        /// </summary>
        [RelayCommand]
        private async Task BackupSyncAsync()
        {
            await ShowSuccessAsync("Backup & sync feature coming soon!", "Backup & Sync");
        }

        /// <summary>
        /// 核武器级数据库重置命令
        /// </summary>
        [RelayCommand]
        private async Task NuclearDatabaseResetAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("SetViewModel: Starting nuclear database reset process...");

                // 第一次确认
                var firstConfirm = await Application.Current!.MainPage!.DisplayAlert(
                    "⚠️ NUCLEAR DATABASE RESET ⚠️",
                    "This will COMPLETELY DESTROY the database and recreate it from scratch!\n\n" +
                    "This action will:\n" +
                    "• Delete the entire database file\n" +
                    "• Remove ALL data permanently\n" +
                    "• Recreate a fresh database\n" +
                    "• Reset ALL settings to defaults\n\n" +
                    "⚠️ THIS CANNOT BE UNDONE! ⚠️\n\n" +
                    "Are you absolutely sure?",
                    "Yes, DESTROY Everything",
                    "Cancel");

                if (!firstConfirm)
                {
                    System.Diagnostics.Debug.WriteLine("SetViewModel: Nuclear reset cancelled by user (first confirmation)");
                    return;
                }

                // 第二次确认
                var secondConfirm = await Application.Current!.MainPage!.DisplayAlert(
                    "🚨 FINAL WARNING 🚨",
                    "You are about to PERMANENTLY DESTROY all your data!\n\n" +
                    "Type 'DESTROY' in your mind and confirm if you really want to proceed.\n\n" +
                    "This is your LAST CHANCE to cancel!",
                    "DESTROY ALL DATA",
                    "Cancel");

                if (!secondConfirm)
                {
                    System.Diagnostics.Debug.WriteLine("SetViewModel: Nuclear reset cancelled by user (second confirmation)");
                    return;
                }

                IsBusy = true;
                System.Diagnostics.Debug.WriteLine("SetViewModel: User confirmed nuclear database reset, executing...");

                // 执行核武器级数据库重置
                var (resetSuccess, detailedLog) = await _databaseService.NuclearDatabaseResetAsync();

                System.Diagnostics.Debug.WriteLine("=== NUCLEAR RESET DETAILED LOG ===");
                System.Diagnostics.Debug.WriteLine(detailedLog);
                System.Diagnostics.Debug.WriteLine("=== END NUCLEAR RESET LOG ===");

                // 强制刷新所有 ViewModel 和 UI
                System.Diagnostics.Debug.WriteLine("SetViewModel: Starting force refresh after nuclear reset...");
                await ForceRefreshAllViewModelsAsync();

                // 发送数据重置消息
                WeakReferenceMessenger.Default.Send(new DataResetMessage());
                System.Diagnostics.Debug.WriteLine("SetViewModel: Data reset message sent after nuclear reset");

                // 等待 UI 更新
                await Task.Delay(1000);

                // 验证重置结果
                var isDataCleared = await _databaseService.VerifyDataClearedAsync();
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Nuclear reset verification result: {isDataCleared}");

                // 显示结果
                if (resetSuccess && isDataCleared)
                {
                    await ShowSuccessAsync(
                        "🚀 NUCLEAR DATABASE RESET SUCCESSFUL! 🚀\n\n" +
                        "✅ Database completely destroyed and recreated\n" +
                        "✅ All data permanently removed\n" +
                        "✅ Fresh database initialized\n" +
                        "✅ All tables recreated\n" +
                        "✅ Default settings restored\n\n" +
                        "The app is now in a completely pristine state!\n" +
                        "You can start adding new data immediately.",
                        "Nuclear Reset Complete");
                }
                else
                {
                    // 获取详细的诊断信息
                    var diagnostics = await _databaseService.GetComprehensiveDatabaseDiagnosticsAsync();

                    await ShowErrorAsync(
                        $"Nuclear database reset encountered issues:\n\n" +
                        $"Reset Operation: {(resetSuccess ? "SUCCESS" : "FAILED")}\n" +
                        $"Data Verification: {(isDataCleared ? "PASSED" : "FAILED")}\n\n" +
                        "Detailed diagnostics have been logged to Visual Studio Output.\n\n" +
                        "Common solutions:\n" +
                        "• Restart the app completely\n" +
                        "• Check file permissions\n" +
                        "• Try the regular Reset All Data instead\n" +
                        "• Restart your device if files are locked",
                        "Nuclear Reset Issues");
                }

                System.Diagnostics.Debug.WriteLine("SetViewModel: Nuclear database reset process completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Error during nuclear database reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Stack trace: {ex.StackTrace}");

                await ShowErrorAsync(
                    $"Critical error during nuclear database reset:\n\n{ex.Message}\n\n" +
                    "Please restart the app and try again. If the problem persists, " +
                    "you may need to manually delete the app data.",
                    "Critical Error");
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 重置所有数据命令
        /// </summary>
        [RelayCommand]
        private async Task ResetAllDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("SetViewModel: Starting data reset process...");

                // 显示二次确认对话框
                var confirm = await Application.Current!.MainPage!.DisplayAlert(
                    "Confirm Data Reset",
                    "Are you sure you want to delete ALL data? This action cannot be undone.",
                    "Reset All Data",
                    "Cancel");

                if (!confirm)
                {
                    System.Diagnostics.Debug.WriteLine("SetViewModel: Data reset cancelled by user");
                    return;
                }

                IsBusy = true;
                System.Diagnostics.Debug.WriteLine("SetViewModel: User confirmed data reset, executing...");
                System.Diagnostics.Debug.WriteLine("=== STARTING COMPREHENSIVE RESET TEST ===");

                // 先显示重置前的数据状态
                System.Diagnostics.Debug.WriteLine("=== DATA BEFORE RESET ===");
                await _databaseService.DebugShowAllDataAsync();

                // 显示数据库连接信息
                System.Diagnostics.Debug.WriteLine("=== DATABASE INFO ===");
                await _databaseService.DebugShowDatabaseInfoAsync();

                // 执行数据重置
                System.Diagnostics.Debug.WriteLine("=== EXECUTING RESET ===");
                var resetSuccess = await _databaseService.CompleteDataResetAsync();
                System.Diagnostics.Debug.WriteLine($"Reset operation result: {resetSuccess}");

                // 显示重置后的数据状态
                System.Diagnostics.Debug.WriteLine("=== DATA AFTER RESET ===");
                await _databaseService.DebugShowAllDataAsync();

                // 验证数据是否真正被清除
                var isDataCleared = await _databaseService.VerifyDataClearedAsync();
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Final data verification result: {isDataCleared}");

                // 强制刷新所有 ViewModel 和 UI
                System.Diagnostics.Debug.WriteLine("SetViewModel: Starting force refresh of all ViewModels...");
                await ForceRefreshAllViewModelsAsync();

                // 发送消息通知其他页面刷新数据
                WeakReferenceMessenger.Default.Send(new DataResetMessage());
                System.Diagnostics.Debug.WriteLine("SetViewModel: Data reset message sent to other ViewModels");

                // 等待一段时间确保消息处理完成
                await Task.Delay(1000);

                // 再次验证 UI 状态
                System.Diagnostics.Debug.WriteLine("SetViewModel: Verifying UI state after message propagation...");
                var uiStateValid = await VerifyUIStateAfterResetAsync();
                System.Diagnostics.Debug.WriteLine($"SetViewModel: UI state verification result: {uiStateValid}");

                // 显示成功提示
                if (resetSuccess && isDataCleared)
                {
                    await ShowSuccessAsync(
                        "All data has been reset successfully!\n\n" +
                        "✅ All employers removed\n" +
                        "✅ All shifts cleared\n" +
                        "✅ All work records deleted\n" +
                        "✅ All payment records cleared\n" +
                        "✅ All notification records cleared\n" +
                        "✅ All user data cleared\n" +
                        "✅ All audit logs cleared\n" +
                        "✅ User settings preserved\n\n" +
                        "The app is now in a completely clean state, ready for new data.",
                        "Data Reset Complete");
                }
                else if (resetSuccess && !isDataCleared)
                {
                    await ShowSuccessAsync(
                        "Data reset completed with warnings!\n\n" +
                        "✅ Reset operation executed\n" +
                        "⚠️ Some data may remain\n" +
                        "✅ User settings preserved\n\n" +
                        "Most data has been cleared. You may restart the app for a complete refresh.",
                        "Data Reset Completed");
                }
                else
                {
                    // 显示详细的错误信息
                    var errorDetails = $"Data reset operation failed.\n\n" +
                                     $"Reset Success: {resetSuccess}\n" +
                                     $"Data Cleared: {isDataCleared}\n" +
                                     $"UI State Valid: {uiStateValid}\n\n" +
                                     "Check Visual Studio Output window for detailed logs.";

                    await ShowErrorAsync(errorDetails, "Reset Failed - Debug Info");
                }
                System.Diagnostics.Debug.WriteLine("SetViewModel: Data reset process completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Error during data reset: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Stack trace: {ex.StackTrace}");
                await ShowErrorAsync($"Failed to reset data: {ex.Message}", "Reset Failed");
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 强制刷新所有 ViewModel 的数据
        /// </summary>
        private async Task ForceRefreshAllViewModelsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("SetViewModel: Force refreshing all ViewModels...");

                // 这里可以添加对其他 ViewModel 的直接刷新调用
                // 由于我们使用消息传递机制，主要依赖 DataResetMessage

                // 强制垃圾回收，清理可能的内存缓存
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                System.Diagnostics.Debug.WriteLine("SetViewModel: Force refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Error during force refresh: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证 UI 状态是否正确反映了数据重置
        /// </summary>
        private async Task<bool> VerifyUIStateAfterResetAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("SetViewModel: Verifying UI state...");

                // 等待一段时间让 UI 更新
                await Task.Delay(500);

                // 这里可以添加对各个页面 ViewModel 状态的检查
                // 由于我们无法直接访问其他 ViewModel 的实例，
                // 我们主要依赖数据库验证和消息传递机制

                // 再次验证数据库状态
                var isDataCleared = await _databaseService.VerifyDataClearedAsync();
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Database verification in UI check: {isDataCleared}");

                return isDataCleared;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SetViewModel: Error during UI state verification: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据库状态命令 - 用于调试
        /// </summary>
        [RelayCommand]
        private async Task TestDatabaseStateAsync()
        {
            try
            {
                IsBusy = true;

                // 获取详细的数据库诊断信息
                var diagnostics = await _databaseService.GetComprehensiveDatabaseDiagnosticsAsync();

                // 显示诊断信息
                await Application.Current!.MainPage!.DisplayAlert(
                    "Database Diagnostics",
                    "Detailed diagnostics have been logged to Visual Studio Output window.\n\n" +
                    "Check the Output window (Debug) for complete information about:\n" +
                    "• Database file path and status\n" +
                    "• Connection state\n" +
                    "• Table structure\n" +
                    "• Data counts\n" +
                    "• File permissions",
                    "OK");
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"Error getting database diagnostics:\n\n{ex.Message}", "Diagnostics Error");
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// 关于应用命令
        /// </summary>
        [RelayCommand]
        private async Task AboutAppAsync()
        {
            await ShowSuccessAsync($"Scheduler App\nVersion {AppVersion}\n\nA work schedule management app for busy workers.", "About");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 加载用户资料
        /// </summary>
        private void LoadUserProfile()
        {
            // 这里可以从数据库或API加载用户资料
            // 暂时使用模拟数据
            UserName = "John Worker";
            UserEmail = "<EMAIL>";
            MemberSince = $"Member since {DateTime.Now.AddMonths(-6):MMM yyyy}";
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private async void LoadSettings()
        {
            try
            {
                // 停止变更跟踪，避免在初始化时触发变更事件
                _changeTracker.StopTracking();

                // 从本地存储加载设置
                // TEMPORARILY DISABLED: Log Confirmation Loading
                /*
                var logConfirmation = await SecureStorage.GetAsync("LogConfirmationEnabled");
                IsLogConfirmationEnabled = bool.Parse(logConfirmation ?? "true");
                */

                var payConfirmation = await SecureStorage.GetAsync("PayConfirmationEnabled");
                IsPayConfirmationEnabled = bool.Parse(payConfirmation ?? "true");

                var timeZone = await SecureStorage.GetAsync("TimeZone");
                CurrentTimeZone = timeZone ?? "Sydney";

                var timeFormat = await SecureStorage.GetAsync("Is24HourFormat");
                Is24HourFormat = bool.Parse(timeFormat ?? "true");
                TimeFormatText = Is24HourFormat ? _localizationService.GetLocalizedString("Hour24") : _localizationService.GetLocalizedString("Hour12");

                // 加载语言设置
                var languageCode = await SecureStorage.GetAsync("LanguageCode");
                CurrentLanguageCode = languageCode ?? "en";

                var language = await SecureStorage.GetAsync("Language");
                CurrentLanguage = language ?? _localizationService.GetLocalizedString("English");

                // 应用语言设置
                _localizationService.SetLanguage(CurrentLanguageCode);

                // 加载通知设置
                var workReminder = await SecureStorage.GetAsync("WorkReminderEnabled");
                IsWorkReminderEnabled = bool.Parse(workReminder ?? "true");

                var breakReminder = await SecureStorage.GetAsync("BreakReminderEnabled");
                IsBreakReminderEnabled = bool.Parse(breakReminder ?? "true");

                var paymentReminder = await SecureStorage.GetAsync("PaymentReminderEnabled");
                IsPaymentReminderEnabled = bool.Parse(paymentReminder ?? "true");

                // 获取应用版本
                AppVersion = AppInfo.VersionString;

                // 设置原始值用于变更跟踪
                SetOriginalValues();

                // 开始变更跟踪
                _changeTracker.StartTracking();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置工作提醒通知
        /// </summary>
        private async Task ConfigureWorkReminders()
        {
            try
            {
                if (IsWorkReminderEnabled)
                {
                    // 请求通知权限
                    await LocalNotificationCenter.Current.RequestNotificationPermission();

                    // 创建工作提醒通知（示例：每天早上8点提醒）
                    var request = new NotificationRequest
                    {
                        NotificationId = 1001,
                        Title = "Work Reminder",
                        Subtitle = "Scheduler",
                        Description = "Don't forget to check your work schedule for today!",
                        BadgeNumber = 1,
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = DateTime.Today.AddHours(8), // 早上8点
                            NotifyRepeatInterval = TimeSpan.FromDays(1) // 每天重复
                        }
                    };

                    await LocalNotificationCenter.Current.Show(request);
                }
                else
                {
                    // 取消工作提醒通知
                    LocalNotificationCenter.Current.Cancel(1001);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置工作提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置休息提醒通知
        /// </summary>
        private async Task ConfigureBreakReminders()
        {
            try
            {
                if (IsBreakReminderEnabled)
                {
                    // 请求通知权限
                    await LocalNotificationCenter.Current.RequestNotificationPermission();

                    // 创建休息提醒通知（示例：工作2小时后提醒休息）
                    var request = new NotificationRequest
                    {
                        NotificationId = 1002,
                        Title = "Break Time",
                        Subtitle = "Scheduler",
                        Description = "Time to take a break! You've been working hard.",
                        BadgeNumber = 1,
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = DateTime.Now.AddHours(2), // 2小时后
                            NotifyRepeatInterval = TimeSpan.FromHours(2) // 每2小时重复
                        }
                    };

                    await LocalNotificationCenter.Current.Show(request);
                }
                else
                {
                    // 取消休息提醒通知
                    LocalNotificationCenter.Current.Cancel(1002);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置休息提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置工资确认提醒通知
        /// </summary>
        private async Task ConfigurePaymentReminders()
        {
            try
            {
                if (IsPaymentReminderEnabled)
                {
                    // 请求通知权限
                    await LocalNotificationCenter.Current.RequestNotificationPermission();

                    // 创建工资确认提醒通知（示例：每月最后一天提醒）
                    var lastDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

                    var request = new NotificationRequest
                    {
                        NotificationId = 1003,
                        Title = "Payment Confirmation",
                        Subtitle = "Scheduler",
                        Description = "Don't forget to confirm your payment records for this month!",
                        BadgeNumber = 1,
                        Schedule = new NotificationRequestSchedule
                        {
                            NotifyTime = lastDayOfMonth.AddHours(18), // 月末下午6点
                            NotifyRepeatInterval = TimeSpan.FromDays(30) // 每月重复
                        }
                    };

                    await LocalNotificationCenter.Current.Show(request);
                }
                else
                {
                    // 取消工资确认提醒通知
                    LocalNotificationCenter.Current.Cancel(1003);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置工资确认提醒失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置原始值用于变更跟踪
        /// </summary>
        private void SetOriginalValues()
        {
            // TEMPORARILY DISABLED: Log Confirmation Tracking
            // _changeTracker.SetOriginalValue(nameof(IsLogConfirmationEnabled), IsLogConfirmationEnabled);
            _changeTracker.SetOriginalValue(nameof(IsPayConfirmationEnabled), IsPayConfirmationEnabled);
            _changeTracker.SetOriginalValue(nameof(IsWorkReminderEnabled), IsWorkReminderEnabled);
            _changeTracker.SetOriginalValue(nameof(IsBreakReminderEnabled), IsBreakReminderEnabled);
            _changeTracker.SetOriginalValue(nameof(IsPaymentReminderEnabled), IsPaymentReminderEnabled);
            _changeTracker.SetOriginalValue(nameof(CurrentTimeZone), CurrentTimeZone);
            _changeTracker.SetOriginalValue(nameof(Is24HourFormat), Is24HourFormat);
            _changeTracker.SetOriginalValue(nameof(CurrentLanguage), CurrentLanguage);
            _changeTracker.SetOriginalValue(nameof(CurrentLanguageCode), CurrentLanguageCode);
        }

        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <param name="propertyName">属性名</param>
        /// <returns>属性值</returns>
        private object? GetPropertyValue(string? propertyName)
        {
            return propertyName switch
            {
                // TEMPORARILY DISABLED: Log Confirmation Value
                // nameof(IsLogConfirmationEnabled) => IsLogConfirmationEnabled,
                nameof(IsPayConfirmationEnabled) => IsPayConfirmationEnabled,
                nameof(IsWorkReminderEnabled) => IsWorkReminderEnabled,
                nameof(IsBreakReminderEnabled) => IsBreakReminderEnabled,
                nameof(IsPaymentReminderEnabled) => IsPaymentReminderEnabled,
                nameof(CurrentTimeZone) => CurrentTimeZone,
                nameof(Is24HourFormat) => Is24HourFormat,
                nameof(CurrentLanguage) => CurrentLanguage,
                nameof(CurrentLanguageCode) => CurrentLanguageCode,
                _ => null
            };
        }

        #region 权限管理方法

        /// <summary>
        /// 加载权限状态
        /// </summary>
        private async Task LoadPermissionStatusAsync()
        {
            if (_permissionService == null)
                return;

            try
            {
                // 检查通知权限
                var notificationStatus = await _permissionService.CheckNotificationPermissionAsync();
                UpdateNotificationPermissionStatus(notificationStatus);

                // 检查位置权限
                var locationStatus = await _permissionService.CheckLocationPermissionAsync();
                UpdateLocationPermissionStatus(locationStatus);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载权限状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新通知权限状态显示
        /// </summary>
        private void UpdateNotificationPermissionStatus(Services.PermissionStatus status)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                NotificationPermissionStatus = status switch
                {
                    Services.PermissionStatus.Granted => "已允许",
                    Services.PermissionStatus.Denied => "已拒绝",
                    Services.PermissionStatus.Restricted => "受限制",
                    Services.PermissionStatus.Limited => "有限制",
                    _ => "未知"
                };

                NotificationPermissionColor = status switch
                {
                    Services.PermissionStatus.Granted => "#10B981",
                    Services.PermissionStatus.Denied => "#EF4444",
                    Services.PermissionStatus.Restricted => "#F59E0B",
                    Services.PermissionStatus.Limited => "#F59E0B",
                    _ => "#666666"
                };
            });
        }

        /// <summary>
        /// 更新位置权限状态显示
        /// </summary>
        private void UpdateLocationPermissionStatus(Services.PermissionStatus status)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                LocationPermissionStatus = status switch
                {
                    Services.PermissionStatus.Granted => "已允许",
                    Services.PermissionStatus.Denied => "已拒绝",
                    Services.PermissionStatus.Restricted => "受限制",
                    Services.PermissionStatus.Limited => "有限制",
                    _ => "未知"
                };

                LocationPermissionColor = status switch
                {
                    Services.PermissionStatus.Granted => "#10B981",
                    Services.PermissionStatus.Denied => "#EF4444",
                    Services.PermissionStatus.Restricted => "#F59E0B",
                    Services.PermissionStatus.Limited => "#F59E0B",
                    _ => "#666666"
                };
            });
        }

        /// <summary>
        /// 检查通知权限命令
        /// </summary>
        [RelayCommand]
        private async Task CheckNotificationPermissionAsync()
        {
            if (_permissionService == null)
            {
                await ShowErrorAsync("权限服务不可用");
                return;
            }

            try
            {
                var hasPermission = await _permissionService.EnsureNotificationPermissionAsync();
                var status = await _permissionService.CheckNotificationPermissionAsync();
                UpdateNotificationPermissionStatus(status);

                if (hasPermission)
                {
                    await ShowSuccessAsync("通知权限已获取");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"检查通知权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查位置权限命令
        /// </summary>
        [RelayCommand]
        private async Task CheckLocationPermissionAsync()
        {
            if (_permissionService == null)
            {
                await ShowErrorAsync("权限服务不可用");
                return;
            }

            try
            {
                var status = await _permissionService.RequestLocationPermissionAsync();
                UpdateLocationPermissionStatus(status);

                var statusText = status switch
                {
                    Services.PermissionStatus.Granted => "位置权限已获取",
                    Services.PermissionStatus.Denied => "位置权限被拒绝",
                    _ => $"位置权限状态: {status}"
                };

                if (status == Services.PermissionStatus.Granted)
                {
                    await ShowSuccessAsync(statusText);
                }
                else
                {
                    await ShowErrorAsync(statusText);
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"检查位置权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开应用设置命令
        /// </summary>
        [RelayCommand]
        private async Task OpenAppSettingsAsync()
        {
            if (_permissionService == null)
            {
                await ShowErrorAsync("权限服务不可用");
                return;
            }

            try
            {
                await _permissionService.OpenAppSettingsAsync();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"打开应用设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试通知功能命令
        /// </summary>
        [RelayCommand]
        private async Task TestNotificationAsync()
        {
            if (_notificationTestService == null)
            {
                await ShowErrorAsync("通知测试服务不可用");
                return;
            }

            try
            {
                IsBusy = true;

                // 显示开始测试的消息
                await ShowSuccessAsync("开始测试通知功能，请查看调试输出...");

                // 运行所有测试
                await _notificationTestService.RunAllTestsAsync();

                // 生成测试报告
                var report = await _notificationTestService.GenerateTestReportAsync();
                System.Diagnostics.Debug.WriteLine(report);

                await ShowSuccessAsync("通知功能测试完成！请查看调试输出获取详细结果。");
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"测试通知功能失败: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// 本地化文本集合
    /// </summary>
    public class LocalizedTexts
    {
        private readonly LocalizationService _localizationService;

        public LocalizedTexts(LocalizationService localizationService)
        {
            _localizationService = localizationService;
        }

        // Settings Page
        public string Settings => _localizationService.GetLocalizedString("Settings");
        public string Edit => _localizationService.GetLocalizedString("Edit");
        public string WorkSettings => _localizationService.GetLocalizedString("WorkSettings");
        public string LogConfirmation => _localizationService.GetLocalizedString("LogConfirmation");
        public string LogConfirmationDesc => _localizationService.GetLocalizedString("LogConfirmationDesc");
        public string PayConfirmation => _localizationService.GetLocalizedString("PayConfirmation");
        public string PayConfirmationDesc => _localizationService.GetLocalizedString("PayConfirmationDesc");
        public string Notifications => _localizationService.GetLocalizedString("Notifications");
        public string WorkReminders => _localizationService.GetLocalizedString("WorkReminders");
        public string WorkRemindersDesc => _localizationService.GetLocalizedString("WorkRemindersDesc");
        public string BreakReminders => _localizationService.GetLocalizedString("BreakReminders");
        public string BreakRemindersDesc => _localizationService.GetLocalizedString("BreakRemindersDesc");
        public string PaymentNotifications => _localizationService.GetLocalizedString("PaymentNotifications");
        public string PaymentNotificationsDesc => _localizationService.GetLocalizedString("PaymentNotificationsDesc");
        public string DisplayTime => _localizationService.GetLocalizedString("DisplayTime");
        public string TimeZone => _localizationService.GetLocalizedString("TimeZone");
        public string TimeFormat => _localizationService.GetLocalizedString("TimeFormat");
        public string Language => _localizationService.GetLocalizedString("Language");
        public string SystemIntegration => _localizationService.GetLocalizedString("SystemIntegration");
        public string AlarmSettings => _localizationService.GetLocalizedString("AlarmSettings");
        public string AlarmSettingsDesc => _localizationService.GetLocalizedString("AlarmSettingsDesc");
        public string ExportToCalendar => _localizationService.GetLocalizedString("ExportToCalendar");
        public string ExportToCalendarDesc => _localizationService.GetLocalizedString("ExportToCalendarDesc");
        public string DataManagement => _localizationService.GetLocalizedString("DataManagement");
        public string ExportData => _localizationService.GetLocalizedString("ExportData");
        public string ExportDataDesc => _localizationService.GetLocalizedString("ExportDataDesc");
        public string BackupSync => _localizationService.GetLocalizedString("BackupSync");
        public string BackupSyncDesc => _localizationService.GetLocalizedString("BackupSyncDesc");
        public string AboutApp => _localizationService.GetLocalizedString("AboutApp");

        // Messages
        public string SettingsUpdated => _localizationService.GetLocalizedString("SettingsUpdated");
        public string NotificationSettings => _localizationService.GetLocalizedString("NotificationSettings");
        public string Enabled => _localizationService.GetLocalizedString("Enabled");
        public string Disabled => _localizationService.GetLocalizedString("Disabled");

        // Login Page
        public string Username => _localizationService.GetLocalizedString("Username");
        public string Password => _localizationService.GetLocalizedString("Password");
        public string RememberMe => _localizationService.GetLocalizedString("RememberMe");
        public string ForgotPassword => _localizationService.GetLocalizedString("ForgotPassword");
        public string Login => _localizationService.GetLocalizedString("Login");
        public string WorkManagementSystem => _localizationService.GetLocalizedString("WorkManagementSystem");

        // TodoList Page
        public string SyncStatus => _localizationService.GetLocalizedString("SyncStatus");
        public string ToggleView => _localizationService.GetLocalizedString("ToggleView");
        public string Refresh => _localizationService.GetLocalizedString("Refresh");
        public string NoPendingTodoItems => _localizationService.GetLocalizedString("NoPendingTodoItems");
        public string RecentlyCompletedTasks => _localizationService.GetLocalizedString("RecentlyCompletedTasks");

        // AllEmployer Page
        public string EmployerManagement => _localizationService.GetLocalizedString("EmployerManagement");
        public string AddEmployer => _localizationService.GetLocalizedString("AddEmployer");
        public string NoEmployerInfo => _localizationService.GetLocalizedString("NoEmployerInfo");
        public string ClickAddEmployerToStart => _localizationService.GetLocalizedString("ClickAddEmployerToStart");
        public string EditSelected => _localizationService.GetLocalizedString("EditSelected");
        public string DeleteSelected => _localizationService.GetLocalizedString("DeleteSelected");
        public string BackToHome => _localizationService.GetLocalizedString("BackToHome");

        // PayView Page
        public string ManageEmployers => _localizationService.GetLocalizedString("ManageEmployers");
        public string PendingPayments => _localizationService.GetLocalizedString("PendingPayments");
        public string RecentlyConfirmed => _localizationService.GetLocalizedString("RecentlyConfirmed");

        // Common Actions
        public string Add => _localizationService.GetLocalizedString("Add");
        public string Delete => _localizationService.GetLocalizedString("Delete");
        public string Save => _localizationService.GetLocalizedString("Save");
        public string Cancel => _localizationService.GetLocalizedString("Cancel");
        public string OK => _localizationService.GetLocalizedString("OK");
        public string Yes => _localizationService.GetLocalizedString("Yes");
        public string No => _localizationService.GetLocalizedString("No");

        // CalendarView - Edit already defined above

        // SetView - WorkSettings and Notifications already defined above
        public string ShiftReminderTime => _localizationService.GetLocalizedString("ShiftReminderTime");
        public string ShiftReminderDescription => _localizationService.GetLocalizedString("ShiftReminderDescription");
        public string MinutesFormat => _localizationService.GetLocalizedString("MinutesFormat");
        public string DailySchedule => _localizationService.GetLocalizedString("DailySchedule");
        public string DailyScheduleDescription => _localizationService.GetLocalizedString("DailyScheduleDescription");
        public string DailyNotificationTime => _localizationService.GetLocalizedString("DailyNotificationTime");
        public string DailyNotificationDescription => _localizationService.GetLocalizedString("DailyNotificationDescription");
        public string BreakReminderInterval => _localizationService.GetLocalizedString("BreakReminderInterval");
        public string BreakReminderDescription => _localizationService.GetLocalizedString("BreakReminderDescription");
        public string HoursFormat => _localizationService.GetLocalizedString("HoursFormat");
        public string NotificationBehavior => _localizationService.GetLocalizedString("NotificationBehavior");
        public string NotificationSound => _localizationService.GetLocalizedString("NotificationSound");
        public string NotificationSoundDescription => _localizationService.GetLocalizedString("NotificationSoundDescription");
        public string VibrationReminder => _localizationService.GetLocalizedString("VibrationReminder");
        public string VibrationDescription => _localizationService.GetLocalizedString("VibrationDescription");
        public string QuietHours => _localizationService.GetLocalizedString("QuietHours");
        public string QuietHoursDescription => _localizationService.GetLocalizedString("QuietHoursDescription");
        public string QuietHoursPeriod => _localizationService.GetLocalizedString("QuietHoursPeriod");
        public string QuietHoursPeriodDescription => _localizationService.GetLocalizedString("QuietHoursPeriodDescription");
        public string PermissionManagement => _localizationService.GetLocalizedString("PermissionManagement");
        public string NotificationPermission => _localizationService.GetLocalizedString("NotificationPermission");
        public string NotificationPermissionDescription => _localizationService.GetLocalizedString("NotificationPermissionDescription");
        public string Check => _localizationService.GetLocalizedString("Check");
        public string LocationPermission => _localizationService.GetLocalizedString("LocationPermission");
        public string LocationPermissionDescription => _localizationService.GetLocalizedString("LocationPermissionDescription");
        public string OpenAppSettings => _localizationService.GetLocalizedString("OpenAppSettings");
        public string TestNotification => _localizationService.GetLocalizedString("TestNotification");
        // DisplayTime, SystemIntegration, DataManagement, AboutApp already defined above

        // ShiftManagementView
        public string SmartTimeSelection => _localizationService.GetLocalizedString("SmartTimeSelection");
        public string StartTime => _localizationService.GetLocalizedString("StartTime");
        public string WorkHours => _localizationService.GetLocalizedString("WorkHours");
        public string EndTimeAutoCalculated => _localizationService.GetLocalizedString("EndTimeAutoCalculated");
        public string SmartWorkPreview => _localizationService.GetLocalizedString("SmartWorkPreview");
        public string JobPreview => _localizationService.GetLocalizedString("JobPreview");
        public string WorkHoursLabel => _localizationService.GetLocalizedString("WorkHoursLabel");
        public string HourlyRateLabel => _localizationService.GetLocalizedString("HourlyRateLabel");
        public string EstimatedRevenue => _localizationService.GetLocalizedString("EstimatedRevenue");
        public string TimeRangeDisplay => _localizationService.GetLocalizedString("TimeRangeDisplay");
        public string RecurringShiftSettings => _localizationService.GetLocalizedString("RecurringShiftSettings");
        public string RecurringShiftTitleAndSwitch => _localizationService.GetLocalizedString("RecurringShiftTitleAndSwitch");
        public string RecurringSettingsDetails => _localizationService.GetLocalizedString("RecurringSettingsDetails");
        public string CyclePeriod => _localizationService.GetLocalizedString("CyclePeriod");
        public string RecurringEndSettings => _localizationService.GetLocalizedString("RecurringEndSettings");
        public string RecurringPreviewInfo => _localizationService.GetLocalizedString("RecurringPreviewInfo");
        public string RecurringShiftNotificationRestriction => _localizationService.GetLocalizedString("RecurringShiftNotificationRestriction");
        public string RecurringShiftNotificationMessage => _localizationService.GetLocalizedString("RecurringShiftNotificationMessage");
        public string NotificationSettingsTitleAndSwitch => _localizationService.GetLocalizedString("NotificationSettingsTitleAndSwitch");
        public string NotificationSettingsDetails => _localizationService.GetLocalizedString("NotificationSettingsDetails");
        public string ReminderTimeSelection => _localizationService.GetLocalizedString("ReminderTimeSelection");
        public string ReminderTime => _localizationService.GetLocalizedString("ReminderTime");
        public string NotificationPreviewInfo => _localizationService.GetLocalizedString("NotificationPreviewInfo");

        // NotificationHistoryView
        public string NotificationHistory => _localizationService.GetLocalizedString("NotificationHistory");
        public string ShiftNotificationManagement => _localizationService.GetLocalizedString("ShiftNotificationManagement");
        // TestNotification already defined above
        public string NoShiftsWithNotifications => _localizationService.GetLocalizedString("NoShiftsWithNotifications");
        public string ReminderMinutesFormat => _localizationService.GetLocalizedString("ReminderMinutesFormat");
        public string CancelNotification => _localizationService.GetLocalizedString("CancelNotification");
        public string RefreshData => _localizationService.GetLocalizedString("RefreshData");
        public string SearchPlaceholder => _localizationService.GetLocalizedString("SearchPlaceholder");
        public string Search => _localizationService.GetLocalizedString("Search");
        public string NotificationType => _localizationService.GetLocalizedString("NotificationType");
        public string NotificationStatus => _localizationService.GetLocalizedString("NotificationStatus");
        public string StartDate => _localizationService.GetLocalizedString("StartDate");
        public string EndDate => _localizationService.GetLocalizedString("EndDate");
        public string ClearFilters => _localizationService.GetLocalizedString("ClearFilters");
        // Refresh already defined above
        public string ClearAll => _localizationService.GetLocalizedString("ClearAll");
        public string NoNotificationRecords => _localizationService.GetLocalizedString("NoNotificationRecords");
        public string TryAdjustingFilters => _localizationService.GetLocalizedString("TryAdjustingFilters");
        public string ScheduledTime => _localizationService.GetLocalizedString("ScheduledTime");
        public string ReadTime => _localizationService.GetLocalizedString("ReadTime");
        public string MarkAsRead => _localizationService.GetLocalizedString("MarkAsRead");
        public string Resend => _localizationService.GetLocalizedString("Resend");
        // Delete already defined above

        // NotificationSettingsView
        public string MainNotificationToggle => _localizationService.GetLocalizedString("MainNotificationToggle");
        public string EnableNotifications => _localizationService.GetLocalizedString("EnableNotifications");
        public string EnableSound => _localizationService.GetLocalizedString("EnableSound");
        public string EnableVibration => _localizationService.GetLocalizedString("EnableVibration");
        public string ShiftReminderSettings => _localizationService.GetLocalizedString("ShiftReminderSettings");
        public string EnableShiftReminders => _localizationService.GetLocalizedString("EnableShiftReminders");
        public string AdvanceReminderTime => _localizationService.GetLocalizedString("AdvanceReminderTime");
        public string EnableDailyScheduleNotifications => _localizationService.GetLocalizedString("EnableDailyScheduleNotifications");
        public string BreakReminderSettings => _localizationService.GetLocalizedString("BreakReminderSettings");
        public string EnableBreakReminders => _localizationService.GetLocalizedString("EnableBreakReminders");
        public string ReminderInterval => _localizationService.GetLocalizedString("ReminderInterval");
        public string OnlyDuringWorkHours => _localizationService.GetLocalizedString("OnlyDuringWorkHours");
        public string PaymentReminderSettings => _localizationService.GetLocalizedString("PaymentReminderSettings");
        public string EnablePaymentReminders => _localizationService.GetLocalizedString("EnablePaymentReminders");
        public string AdvanceReminderDays => _localizationService.GetLocalizedString("AdvanceReminderDays");
        public string EnablePaymentConfirmationReminders => _localizationService.GetLocalizedString("EnablePaymentConfirmationReminders");
        public string QuietHoursSettings => _localizationService.GetLocalizedString("QuietHoursSettings");
        public string EnableQuietHours => _localizationService.GetLocalizedString("EnableQuietHours");
        // StartTime already defined above
        public string EndTime => _localizationService.GetLocalizedString("EndTime");
        public string Hour => _localizationService.GetLocalizedString("Hour");
        public string Minute => _localizationService.GetLocalizedString("Minute");
        public string SaveSettings => _localizationService.GetLocalizedString("SaveSettings");
        public string RequestNotificationPermission => _localizationService.GetLocalizedString("RequestNotificationPermission");
        public string ResetToDefaultSettings => _localizationService.GetLocalizedString("ResetToDefaultSettings");

        // NotificationTestView
        public string NotificationFunctionTest => _localizationService.GetLocalizedString("NotificationFunctionTest");
        public string NotificationPermissionManagement => _localizationService.GetLocalizedString("NotificationPermissionManagement");
        public string CheckPermission => _localizationService.GetLocalizedString("CheckPermission");
        public string RequestPermission => _localizationService.GetLocalizedString("RequestPermission");
        public string CustomNotificationTest => _localizationService.GetLocalizedString("CustomNotificationTest");
        public string NotificationTitle => _localizationService.GetLocalizedString("NotificationTitle");
        public string EnterNotificationTitle => _localizationService.GetLocalizedString("EnterNotificationTitle");
        public string NotificationContent => _localizationService.GetLocalizedString("NotificationContent");
        public string EnterNotificationContent => _localizationService.GetLocalizedString("EnterNotificationContent");
        public string DelayTimeFormat => _localizationService.GetLocalizedString("DelayTimeFormat");
        public string InstantNotification => _localizationService.GetLocalizedString("InstantNotification");
        public string DelayedNotification => _localizationService.GetLocalizedString("DelayedNotification");
        public string FeatureNotificationTest => _localizationService.GetLocalizedString("FeatureNotificationTest");
        public string TestShiftReminder => _localizationService.GetLocalizedString("TestShiftReminder");
        public string TestTodoReminder => _localizationService.GetLocalizedString("TestTodoReminder");
        public string TestPaymentReminder => _localizationService.GetLocalizedString("TestPaymentReminder");
        public string TestBreakReminder => _localizationService.GetLocalizedString("TestBreakReminder");
        public string SystemManagement => _localizationService.GetLocalizedString("SystemManagement");
        public string RescheduleAllNotifications => _localizationService.GetLocalizedString("RescheduleAllNotifications");
        public string TestInstructions => _localizationService.GetLocalizedString("TestInstructions");
        public string InstantNotificationDesc => _localizationService.GetLocalizedString("InstantNotificationDesc");
        public string DelayedNotificationDesc => _localizationService.GetLocalizedString("DelayedNotificationDesc");
        public string ShiftReminderDesc => _localizationService.GetLocalizedString("ShiftReminderDesc");
        public string TodoReminderDesc => _localizationService.GetLocalizedString("TodoReminderDesc");
        public string PaymentReminderDesc => _localizationService.GetLocalizedString("PaymentReminderDesc");
        public string BreakReminderDesc => _localizationService.GetLocalizedString("BreakReminderDesc");
        public string RescheduleDesc => _localizationService.GetLocalizedString("RescheduleDesc");

        // AllPaymentRecordsView
        public string AllPaymentRecords => _localizationService.GetLocalizedString("AllPaymentRecords");
        public string TotalRecords => _localizationService.GetLocalizedString("TotalRecords");
        public string PendingConfirmation => _localizationService.GetLocalizedString("PendingConfirmation");
        public string Confirmed => _localizationService.GetLocalizedString("Confirmed");
        public string SelectAllToggle => _localizationService.GetLocalizedString("SelectAllToggle");
        public string SelectedFormat => _localizationService.GetLocalizedString("SelectedFormat");
        public string NoPaymentRecords => _localizationService.GetLocalizedString("NoPaymentRecords");
        public string WorkPeriodFormat => _localizationService.GetLocalizedString("WorkPeriodFormat");
        public string ToFormat => _localizationService.GetLocalizedString("ToFormat");
        // HoursFormat 已在上面定义 (line 1444)
        public string CancelConfirmation => _localizationService.GetLocalizedString("CancelConfirmation");

        // PaymentTodoView
        public string PaymentTodoStatistics => _localizationService.GetLocalizedString("PaymentTodoStatistics");
        public string PendingConfirmationCount => _localizationService.GetLocalizedString("PendingConfirmationCount");
        public string OverdueCount => _localizationService.GetLocalizedString("OverdueCount");
        public string PendingAmount => _localizationService.GetLocalizedString("PendingAmount");
        public string ConfirmedAmount => _localizationService.GetLocalizedString("ConfirmedAmount");
        public string HideCompleted => _localizationService.GetLocalizedString("HideCompleted");
        public string ShowCompleted => _localizationService.GetLocalizedString("ShowCompleted");
        public string OverduePayments => _localizationService.GetLocalizedString("OverduePayments");
        public string NoOverduePayments => _localizationService.GetLocalizedString("NoOverduePayments");
        public string UrgentMark => _localizationService.GetLocalizedString("UrgentMark");
        public string PaymentInfo => _localizationService.GetLocalizedString("PaymentInfo");
        public string ViewDetails => _localizationService.GetLocalizedString("ViewDetails");
        public string Confirm => _localizationService.GetLocalizedString("Confirm");
        // PendingPayments 已在上面定义 (line 1420)
        public string NoPendingPayments => _localizationService.GetLocalizedString("NoPendingPayments");
        public string PaymentIcon => _localizationService.GetLocalizedString("PaymentIcon");
        public string ConfirmedPayments => _localizationService.GetLocalizedString("ConfirmedPayments");
        public string NoConfirmedPayments => _localizationService.GetLocalizedString("NoConfirmedPayments");
        public string CompletedMark => _localizationService.GetLocalizedString("CompletedMark");
        public string CompletedTimeFormat => _localizationService.GetLocalizedString("CompletedTimeFormat");
        public string LoadingIndicator => _localizationService.GetLocalizedString("LoadingIndicator");
        // Refresh 已在上面定义 (line 1405)
        // Cancel 已在上面定义 (line 1427)

        // EditEmployerView
        public string EditEmployer => _localizationService.GetLocalizedString("EditEmployer");
        public string EditEmployerDescription => _localizationService.GetLocalizedString("EditEmployerDescription");
        public string BasicInformation => _localizationService.GetLocalizedString("BasicInformation");
        public string EmployerName => _localizationService.GetLocalizedString("EmployerName");
        public string EmployerNamePlaceholder => _localizationService.GetLocalizedString("EmployerNamePlaceholder");
        public string ContactInfo => _localizationService.GetLocalizedString("ContactInfo");
        public string ContactInfoPlaceholder => _localizationService.GetLocalizedString("ContactInfoPlaceholder");
        public string WorkLocation => _localizationService.GetLocalizedString("WorkLocation");
        public string WorkLocationPlaceholder => _localizationService.GetLocalizedString("WorkLocationPlaceholder");
        public string SalaryInformation => _localizationService.GetLocalizedString("SalaryInformation");
        public string HourlyRate => _localizationService.GetLocalizedString("HourlyRate");
        public string PaymentCycleDays => _localizationService.GetLocalizedString("PaymentCycleDays");
        public string DisplaySettings => _localizationService.GetLocalizedString("DisplaySettings");
        public string IdentificationColor => _localizationService.GetLocalizedString("IdentificationColor");
        public string ColorSelectionGrid => _localizationService.GetLocalizedString("ColorSelectionGrid");
        public string ColorCircleButton => _localizationService.GetLocalizedString("ColorCircleButton");
        public string SelectedMark => _localizationService.GetLocalizedString("SelectedMark");
        public string ColorName => _localizationService.GetLocalizedString("ColorName");
        public string CurrentColorDisplay => _localizationService.GetLocalizedString("CurrentColorDisplay");
        public string CurrentColor => _localizationService.GetLocalizedString("CurrentColor");
        public string EmploymentStatus => _localizationService.GetLocalizedString("EmploymentStatus");
        public string NotesInformation => _localizationService.GetLocalizedString("NotesInformation");
        public string NotesPlaceholder => _localizationService.GetLocalizedString("NotesPlaceholder");
        public string ActionButtons => _localizationService.GetLocalizedString("ActionButtons");
        public string SaveChanges => _localizationService.GetLocalizedString("SaveChanges");
        public string CancelEdit => _localizationService.GetLocalizedString("CancelEdit");

        /// <summary>
        /// 筛选 - Filter
        /// </summary>
        public string Filter => _localizationService.GetLocalizedString("Filter");

        // 注释：Add 属性已在第1424行定义 - Add property already defined at line 1424

        /// <summary>
        /// 暂无班次记录 - No shift records
        /// </summary>
        public string NoShiftRecords => _localizationService.GetLocalizedString("NoShiftRecords");

        /// <summary>
        /// 点击右上角添加按钮创建第一个班次 - Click the add button in the top right to create your first shift
        /// </summary>
        public string ClickAddToCreateFirstShift => _localizationService.GetLocalizedString("ClickAddToCreateFirstShift");

        /// <summary>
        /// 立即添加 - Add Now
        /// </summary>
        public string AddNow => _localizationService.GetLocalizedString("AddNow");

        /// <summary>
        /// 最小化班次管理页面 - Minimal Shift Management Page
        /// </summary>
        public string MinimalShiftManagementPage => _localizationService.GetLocalizedString("MinimalShiftManagementPage");

        /// <summary>
        /// 选择雇主 - Select Employer
        /// </summary>
        public string SelectEmployer => _localizationService.GetLocalizedString("SelectEmployer");

        // 注释：HourlyRateLabel 属性已在第1472行定义 - HourlyRateLabel property already defined at line 1472

        /// <summary>
        /// 返回 - Return
        /// </summary>
        public string Return => _localizationService.GetLocalizedString("Return");

        /// <summary>
        /// 班次管理(简化版) - Shift Management (Simplified)
        /// </summary>
        public string ShiftManagementSimplified => _localizationService.GetLocalizedString("ShiftManagementSimplified");

        /// <summary>
        /// 页面加载成功 - Page loaded successfully
        /// </summary>
        public string PageLoadedSuccessfully => _localizationService.GetLocalizedString("PageLoadedSuccessfully");

        /// <summary>
        /// 保存班次 - Save Shift
        /// </summary>
        public string SaveShift => _localizationService.GetLocalizedString("SaveShift");

        /// <summary>
        /// 导航工作正常消息 - Navigation works message
        /// </summary>
        public string NavigationWorksMessage => _localizationService.GetLocalizedString("NavigationWorksMessage");

        /// <summary>
        /// 尝试导航到完整页面 - Try navigate to full page
        /// </summary>
        public string TryNavigateToFullPage => _localizationService.GetLocalizedString("TryNavigateToFullPage");

        /// <summary>
        /// 打开完整的Schedule页面 - Open Full Schedule Page
        /// </summary>
        public string OpenFullSchedulePage => _localizationService.GetLocalizedString("OpenFullSchedulePage");

        /// <summary>
        /// 返回主页 - Return to Home
        /// </summary>
        public string ReturnToHome => _localizationService.GetLocalizedString("ReturnToHome");

        /// <summary>
        /// 快速操作 - Quick Actions
        /// </summary>
        public string QuickActions => _localizationService.GetLocalizedString("QuickActions");

        /// <summary>
        /// 上班打卡 - Clock In
        /// </summary>
        public string ClockIn => _localizationService.GetLocalizedString("ClockIn");

        /// <summary>
        /// 下班打卡 - Clock Out
        /// </summary>
        public string ClockOut => _localizationService.GetLocalizedString("ClockOut");

        /// <summary>
        /// 添加笔记 - Add Note
        /// </summary>
        public string AddNote => _localizationService.GetLocalizedString("AddNote");

        /// <summary>
        /// 详情 - Details
        /// </summary>
        public string Details => _localizationService.GetLocalizedString("Details");

        // 注释：Edit 属性已在第1359行定义 - Edit property already defined at line 1359

        /// <summary>
        /// 测试班次管理页面 - Test Shift Management Page
        /// </summary>
        public string TestShiftManagementPage => _localizationService.GetLocalizedString("TestShiftManagementPage");

        // AutoCloseTestView strings
        public string AutoCloseTestTitle => _localizationService.GetLocalizedString("AutoCloseTestTitle");
        public string AutoCloseTestHeader => _localizationService.GetLocalizedString("AutoCloseTestHeader");
        public string AutoCloseTestInstructions => _localizationService.GetLocalizedString("AutoCloseTestInstructions");
        public string TestSuccessToast => _localizationService.GetLocalizedString("TestSuccessToast");
        public string TestErrorSnackbar => _localizationService.GetLocalizedString("TestErrorSnackbar");
        public string TestInfoToast => _localizationService.GetLocalizedString("TestInfoToast");
        public string TestWarningSnackbar => _localizationService.GetLocalizedString("TestWarningSnackbar");
        public string AutoCloseNote => _localizationService.GetLocalizedString("AutoCloseNote");
        public string EmployerCountFormat => _localizationService.GetLocalizedString("EmployerCountFormat");
        public string RecordCountFormat => _localizationService.GetLocalizedString("RecordCountFormat");
        public string WorkHoursFormat => _localizationService.GetLocalizedString("WorkHoursFormat");

        // DatabaseTestView strings
        public string DatabaseTestTitle => _localizationService.GetLocalizedString("DatabaseTestTitle");
        public string DatabaseTestHeader => _localizationService.GetLocalizedString("DatabaseTestHeader");
        public string DatabaseTestDescription => _localizationService.GetLocalizedString("DatabaseTestDescription");
        public string TestOptions => _localizationService.GetLocalizedString("TestOptions");
        public string TestRunning => _localizationService.GetLocalizedString("TestRunning");
        public string RunConnectionTest => _localizationService.GetLocalizedString("RunConnectionTest");
        public string TestResetFunction => _localizationService.GetLocalizedString("TestResetFunction");
        public string ReenableDemoData => _localizationService.GetLocalizedString("ReenableDemoData");
        public string ClearResults => _localizationService.GetLocalizedString("ClearResults");
        public string AllTestsPassed => _localizationService.GetLocalizedString("AllTestsPassed");
        public string TestsFoundIssues => _localizationService.GetLocalizedString("TestsFoundIssues");
        public string RunningTests => _localizationService.GetLocalizedString("RunningTests");
        public string BackToSettings => _localizationService.GetLocalizedString("BackToSettings");

        // KeyboardTestView strings
        public string KeyboardTestTitle => _localizationService.GetLocalizedString("KeyboardTestTitle");
        public string KeyboardTestHeader => _localizationService.GetLocalizedString("KeyboardTestHeader");
        public string SimplifiedEntryTest => _localizationService.GetLocalizedString("SimplifiedEntryTest");
        public string BasicTextInput => _localizationService.GetLocalizedString("BasicTextInput");
        public string NumericInput => _localizationService.GetLocalizedString("NumericInput");
        public string EmailInput => _localizationService.GetLocalizedString("EmailInput");
        public string MultilineTextInput => _localizationService.GetLocalizedString("MultilineTextInput");
        public string TestInstruction1 => _localizationService.GetLocalizedString("TestInstruction1");
        public string TestInstruction2 => _localizationService.GetLocalizedString("TestInstruction2");
        public string TestInstruction3 => _localizationService.GetLocalizedString("TestInstruction3");

        // BaseViewModel Messages
        public string Error => _localizationService.GetLocalizedString("Error");
        public string Success => _localizationService.GetLocalizedString("Success");
        // Confirm, OK, Cancel 已在上面定义 (lines 1596, 1428, 1427)
        public string OperationFailed => _localizationService.GetLocalizedString("OperationFailed");

        // NotificationHistoryViewModel Messages
        public string LoadingNotificationHistory => _localizationService.GetLocalizedString("LoadingNotificationHistory");
        public string LoadedNotificationCount => _localizationService.GetLocalizedString("LoadedNotificationCount");
        public string LoadNotificationHistoryFailed => _localizationService.GetLocalizedString("LoadNotificationHistoryFailed");
        public string LoadingShiftsWithNotifications => _localizationService.GetLocalizedString("LoadingShiftsWithNotifications");
        public string FoundShiftsWithNotifications => _localizationService.GetLocalizedString("FoundShiftsWithNotifications");
        public string LoadShiftNotificationsFailed => _localizationService.GetLocalizedString("LoadShiftNotificationsFailed");
        public string ShiftNotificationCancelled => _localizationService.GetLocalizedString("ShiftNotificationCancelled");
        public string CancelShiftNotificationFailed => _localizationService.GetLocalizedString("CancelShiftNotificationFailed");
        public string SendingTestNotification => _localizationService.GetLocalizedString("SendingTestNotification");
        public string TestNotificationFailedNoPermission => _localizationService.GetLocalizedString("TestNotificationFailedNoPermission");
        public string TestNotificationTitle => _localizationService.GetLocalizedString("TestNotificationTitle");
        public string TestNotificationDescription => _localizationService.GetLocalizedString("TestNotificationDescription");
        public string TestNotificationSent => _localizationService.GetLocalizedString("TestNotificationSent");
        public string SendTestNotificationFailed => _localizationService.GetLocalizedString("SendTestNotificationFailed");
        public string NotificationMarkedAsRead => _localizationService.GetLocalizedString("NotificationMarkedAsRead");
        public string MarkNotificationFailed => _localizationService.GetLocalizedString("MarkNotificationFailed");
        public string NotificationRecordDeleted => _localizationService.GetLocalizedString("NotificationRecordDeleted");
        public string DeleteNotificationRecordFailed => _localizationService.GetLocalizedString("DeleteNotificationRecordFailed");
        public string ClearingAllNotificationRecords => _localizationService.GetLocalizedString("ClearingAllNotificationRecords");
        public string AllNotificationRecordsCleared => _localizationService.GetLocalizedString("AllNotificationRecordsCleared");
        public string ClearNotificationRecordsFailed => _localizationService.GetLocalizedString("ClearNotificationRecordsFailed");
        public string ResendingNotification => _localizationService.GetLocalizedString("ResendingNotification");
        public string ResendPrefix => _localizationService.GetLocalizedString("ResendPrefix");
        public string NotificationResent => _localizationService.GetLocalizedString("NotificationResent");
        public string ResendNotificationFailed => _localizationService.GetLocalizedString("ResendNotificationFailed");
        public string AllTypes => _localizationService.GetLocalizedString("AllTypes");
        public string WorkReminder => _localizationService.GetLocalizedString("WorkReminder");
        public string BreakReminder => _localizationService.GetLocalizedString("BreakReminder");
        public string PaymentDue => _localizationService.GetLocalizedString("PaymentDue");
        public string ShiftConflict => _localizationService.GetLocalizedString("ShiftConflict");
        public string SystemUpdate => _localizationService.GetLocalizedString("SystemUpdate");
        public string UnknownType => _localizationService.GetLocalizedString("UnknownType");
        public string AllStatuses => _localizationService.GetLocalizedString("AllStatuses");
        public string Pending => _localizationService.GetLocalizedString("Pending");
        public string Sent => _localizationService.GetLocalizedString("Sent");
        public string Read => _localizationService.GetLocalizedString("Read");
        public string Dismissed => _localizationService.GetLocalizedString("Dismissed");
        public string UnknownStatus => _localizationService.GetLocalizedString("UnknownStatus");
        public string TestNotificationMessage => _localizationService.GetLocalizedString("TestNotificationMessage");

        // NotificationSettingsViewModel Messages
        public string LoadingSettings => _localizationService.GetLocalizedString("LoadingSettings");
        public string SettingsLoadedSuccessfully => _localizationService.GetLocalizedString("SettingsLoadedSuccessfully");
        public string LoadSettingsFailedFormat => _localizationService.GetLocalizedString("LoadSettingsFailedFormat");
        public string SavingSettings => _localizationService.GetLocalizedString("SavingSettings");
        public string SettingsSavedSuccessfully => _localizationService.GetLocalizedString("SettingsSavedSuccessfully");
        public string SaveSettingsFailedFormat => _localizationService.GetLocalizedString("SaveSettingsFailedFormat");
        public string SaveSettingsFailed => _localizationService.GetLocalizedString("SaveSettingsFailed");
        public string ResettingSettings => _localizationService.GetLocalizedString("ResettingSettings");
        public string SettingsResetSuccessfully => _localizationService.GetLocalizedString("SettingsResetSuccessfully");
        public string ResetSettingsFailedFormat => _localizationService.GetLocalizedString("ResetSettingsFailedFormat");
        public string ResetSettingsFailed => _localizationService.GetLocalizedString("ResetSettingsFailed");
        public string SendingTestNotificationSettings => _localizationService.GetLocalizedString("SendingTestNotificationSettings");
        public string NotificationPermissionDeniedCannotTest => _localizationService.GetLocalizedString("NotificationPermissionDeniedCannotTest");
        public string TestNotificationSettingsMessage => _localizationService.GetLocalizedString("TestNotificationSettingsMessage");
        public string TestNotificationSettingsSent => _localizationService.GetLocalizedString("TestNotificationSettingsSent");
        public string SendTestNotificationFailedFormat => _localizationService.GetLocalizedString("SendTestNotificationFailedFormat");
        public string RequestingNotificationPermission => _localizationService.GetLocalizedString("RequestingNotificationPermission");
        public string NotificationPermissionGranted => _localizationService.GetLocalizedString("NotificationPermissionGranted");
        public string NotificationPermissionDeniedManuallyEnable => _localizationService.GetLocalizedString("NotificationPermissionDeniedManuallyEnable");
        public string RequestNotificationPermissionFailedFormat => _localizationService.GetLocalizedString("RequestNotificationPermissionFailedFormat");
        public string FiveMinutesBefore => _localizationService.GetLocalizedString("FiveMinutesBefore");
        public string TenMinutesBefore => _localizationService.GetLocalizedString("TenMinutesBefore");
        public string FifteenMinutesBefore => _localizationService.GetLocalizedString("FifteenMinutesBefore");
        public string ThirtyMinutesBefore => _localizationService.GetLocalizedString("ThirtyMinutesBefore");
        public string OneHourBefore => _localizationService.GetLocalizedString("OneHourBefore");
        public string EveryOneHour => _localizationService.GetLocalizedString("EveryOneHour");
        public string EveryTwoHours => _localizationService.GetLocalizedString("EveryTwoHours");
        public string EveryThreeHours => _localizationService.GetLocalizedString("EveryThreeHours");
        public string EveryFourHours => _localizationService.GetLocalizedString("EveryFourHours");
        public string SameDay => _localizationService.GetLocalizedString("SameDay");
        public string OneDayBefore => _localizationService.GetLocalizedString("OneDayBefore");
        public string TwoDaysBefore => _localizationService.GetLocalizedString("TwoDaysBefore");
        public string ThreeDaysBefore => _localizationService.GetLocalizedString("ThreeDaysBefore");
        public string OneWeekBefore => _localizationService.GetLocalizedString("OneWeekBefore");

        // NotificationTestViewModel Messages
        public string TestNotificationDefaultMessage => _localizationService.GetLocalizedString("TestNotificationDefaultMessage");
        public string SendingInstantNotification => _localizationService.GetLocalizedString("SendingInstantNotification");
        public string NotificationPermissionDeniedCannotSendTest => _localizationService.GetLocalizedString("NotificationPermissionDeniedCannotSendTest");
        public string InstantNotificationSent => _localizationService.GetLocalizedString("InstantNotificationSent");
        public string SendInstantNotificationFailedFormat => _localizationService.GetLocalizedString("SendInstantNotificationFailedFormat");
        public string SchedulingDelayedNotificationFormat => _localizationService.GetLocalizedString("SchedulingDelayedNotificationFormat");
        public string DelayedNotificationScheduledFormat => _localizationService.GetLocalizedString("DelayedNotificationScheduledFormat");
        public string ScheduleDelayedNotificationFailedFormat => _localizationService.GetLocalizedString("ScheduleDelayedNotificationFailedFormat");
        public string TestingShiftReminder => _localizationService.GetLocalizedString("TestingShiftReminder");
        public string NoEmployerFoundCannotTestShiftReminder => _localizationService.GetLocalizedString("NoEmployerFoundCannotTestShiftReminder");
        public string TestLocation => _localizationService.GetLocalizedString("TestLocation");
        public string TestShiftLocation => _localizationService.GetLocalizedString("TestShiftLocation");
        public string ShiftReminderTestScheduledOneMinute => _localizationService.GetLocalizedString("ShiftReminderTestScheduledOneMinute");
        public string TestShiftReminderFailedFormat => _localizationService.GetLocalizedString("TestShiftReminderFailedFormat");
        public string TestingTodoReminder => _localizationService.GetLocalizedString("TestingTodoReminder");
        public string TestTodoTitle => _localizationService.GetLocalizedString("TestTodoTitle");
        public string TestTodoDescription => _localizationService.GetLocalizedString("TestTodoDescription");
        public string TodoReminderTestScheduledTwoMinutes => _localizationService.GetLocalizedString("TodoReminderTestScheduledTwoMinutes");
        public string TestTodoReminderFailedFormat => _localizationService.GetLocalizedString("TestTodoReminderFailedFormat");
        public string TestingPaymentReminder => _localizationService.GetLocalizedString("TestingPaymentReminder");
        public string NoEmployerFoundCannotTestPaymentReminder => _localizationService.GetLocalizedString("NoEmployerFoundCannotTestPaymentReminder");
        public string PaymentReminderTestSent => _localizationService.GetLocalizedString("PaymentReminderTestSent");
        public string TestPaymentReminderFailedFormat => _localizationService.GetLocalizedString("TestPaymentReminderFailedFormat");
        public string TestingBreakReminder => _localizationService.GetLocalizedString("TestingBreakReminder");
        public string TestBreakReminderMessage => _localizationService.GetLocalizedString("TestBreakReminderMessage");
        public string BreakReminderTestSent => _localizationService.GetLocalizedString("BreakReminderTestSent");
        public string TestBreakReminderFailedFormat => _localizationService.GetLocalizedString("TestBreakReminderFailedFormat");
        public string ReschedulingAllNotifications => _localizationService.GetLocalizedString("ReschedulingAllNotifications");
        public string AllNotificationsRescheduled => _localizationService.GetLocalizedString("AllNotificationsRescheduled");
        public string RescheduleNotificationsFailedFormat => _localizationService.GetLocalizedString("RescheduleNotificationsFailedFormat");
        public string CheckingNotificationPermission => _localizationService.GetLocalizedString("CheckingNotificationPermission");
        public string NotificationPermissionNotGranted => _localizationService.GetLocalizedString("NotificationPermissionNotGranted");
        public string CheckNotificationPermissionFailedFormat => _localizationService.GetLocalizedString("CheckNotificationPermissionFailedFormat");
        // RequestingNotificationPermission 已在第1844行定义
        public string NotificationPermissionDenied => _localizationService.GetLocalizedString("NotificationPermissionDenied");
        // RequestNotificationPermissionFailedFormat 已在第1847行定义
    }
}
