using System.Diagnostics;
using Scheduler.ViewModels;
using Scheduler.Views;

namespace Scheduler.Tests
{
    /// <summary>
    /// HomeView测试辅助类 - 用于诊断和验证Home页面功能
    /// HomeView Test Helper - for diagnosing and validating Home page functionality
    /// </summary>
    public static class HomeViewTestHelper
    {
        /// <summary>
        /// 验证HomeViewModel的基本状态
        /// Validate basic state of HomeViewModel
        /// </summary>
        /// <param name="viewModel">要验证的ViewModel实例</param>
        /// <returns>验证结果和详细信息</returns>
        public static (bool IsValid, string Details) ValidateHomeViewModel(HomeViewModel viewModel)
        {
            var details = new List<string>();
            bool isValid = true;

            try
            {
                // 检查ViewModel是否为null
                if (viewModel == null)
                {
                    return (false, "HomeViewModel 为 null");
                }

                // 检查基本属性
                details.Add($"Title: {viewModel.Title ?? "null"}");
                details.Add($"IsLoadingData: {viewModel.IsLoadingData}");
                details.Add($"IsRefreshing: {viewModel.IsRefreshing}");

                // 检查集合属性
                if (viewModel.TodayShifts == null)
                {
                    details.Add("警告: TodayShifts 为 null");
                    isValid = false;
                }
                else
                {
                    details.Add($"TodayShifts Count: {viewModel.TodayShifts.Count}");
                }

                if (viewModel.UpcomingShifts == null)
                {
                    details.Add("警告: UpcomingShifts 为 null");
                    isValid = false;
                }
                else
                {
                    details.Add($"UpcomingShifts Count: {viewModel.UpcomingShifts.Count}");
                }

                // 检查计算属性
                try
                {
                    details.Add($"HasTodayTasks: {viewModel.HasTodayTasks}");
                    details.Add($"HasUpcomingShifts: {viewModel.HasUpcomingShifts}");
                    details.Add($"TodayTasksCount: {viewModel.TodayTasksCount}");
                    details.Add($"UpcomingShiftsCount: {viewModel.UpcomingShiftsCount}");
                }
                catch (Exception ex)
                {
                    details.Add($"计算属性访问异常: {ex.Message}");
                    isValid = false;
                }

                // 检查命令
                var commands = new[]
                {
                    ("LoadDataCommand", viewModel.LoadDataCommand),
                    ("AddShiftCommand", viewModel.AddShiftCommand),
                    ("ManageEmployersCommand", viewModel.ManageEmployersCommand),
                    ("NavigateToScheduleCommand", viewModel.NavigateToScheduleCommand),
                    ("ViewTaskDetailsCommand", viewModel.ViewTaskDetailsCommand),
                    ("RefreshCommand", viewModel.RefreshCommand)
                };

                foreach (var (name, command) in commands)
                {
                    if (command == null)
                    {
                        details.Add($"警告: {name} 为 null");
                        isValid = false;
                    }
                    else
                    {
                        details.Add($"{name}: 已初始化");
                    }
                }

                details.Add($"验证结果: {(isValid ? "通过" : "失败")}");
                return (isValid, string.Join("\n", details));
            }
            catch (Exception ex)
            {
                return (false, $"验证过程中发生异常: {ex.Message}\n堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试数据加载功能
        /// Test data loading functionality
        /// </summary>
        /// <param name="viewModel">要测试的ViewModel实例</param>
        /// <returns>测试结果</returns>
        public static async Task<(bool Success, string Details)> TestDataLoading(HomeViewModel viewModel)
        {
            var details = new List<string>();
            bool success = true;

            try
            {
                details.Add("开始测试数据加载...");

                if (viewModel == null)
                {
                    return (false, "HomeViewModel 为 null，无法测试");
                }

                // 记录加载前状态
                var beforeCount = viewModel.TodayShifts?.Count ?? -1;
                details.Add($"加载前 TodayShifts 数量: {beforeCount}");

                // 执行数据加载
                await viewModel.LoadDataCommand.ExecuteAsync(null);

                // 记录加载后状态
                var afterCount = viewModel.TodayShifts?.Count ?? -1;
                details.Add($"加载后 TodayShifts 数量: {afterCount}");

                // 检查加载状态
                details.Add($"IsLoadingData: {viewModel.IsLoadingData}");
                details.Add($"HasTodayTasks: {viewModel.HasTodayTasks}");
                details.Add($"HasUpcomingShifts: {viewModel.HasUpcomingShifts}");

                details.Add("数据加载测试完成");
                return (success, string.Join("\n", details));
            }
            catch (Exception ex)
            {
                details.Add($"数据加载测试异常: {ex.Message}");
                details.Add($"异常堆栈: {ex.StackTrace}");
                return (false, string.Join("\n", details));
            }
        }

        /// <summary>
        /// 运行完整的HomeView诊断
        /// Run complete HomeView diagnostics
        /// </summary>
        /// <param name="viewModel">要诊断的ViewModel实例</param>
        /// <returns>诊断报告</returns>
        public static async Task<string> RunDiagnostics(HomeViewModel viewModel)
        {
            var report = new List<string>
            {
                "=== HomeView 诊断报告 ===",
                $"诊断时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                ""
            };

            // 基本状态验证
            var (isValid, validationDetails) = ValidateHomeViewModel(viewModel);
            report.Add("1. 基本状态验证:");
            report.Add(validationDetails);
            report.Add("");

            // 数据加载测试
            if (isValid)
            {
                var (loadSuccess, loadDetails) = await TestDataLoading(viewModel);
                report.Add("2. 数据加载测试:");
                report.Add(loadDetails);
                report.Add("");
            }
            else
            {
                report.Add("2. 数据加载测试: 跳过（基本验证失败）");
                report.Add("");
            }

            // 系统信息
            report.Add("3. 系统信息:");
            report.Add($"设备平台: {DeviceInfo.Platform}");
            report.Add($"设备型号: {DeviceInfo.Model}");
            report.Add($"操作系统版本: {DeviceInfo.VersionString}");
            report.Add($"应用版本: {AppInfo.VersionString}");
            report.Add("");

            report.Add("=== 诊断报告结束 ===");

            var fullReport = string.Join("\n", report);
            Debug.WriteLine(fullReport);
            return fullReport;
        }

        /// <summary>
        /// 创建测试用的HomeView实例
        /// Create test HomeView instance
        /// </summary>
        /// <param name="viewModel">ViewModel实例</param>
        /// <returns>HomeView实例和创建结果</returns>
        public static (HomeView? View, bool Success, string Error) CreateTestHomeView(HomeViewModel viewModel)
        {
            try
            {
                Debug.WriteLine("尝试创建测试用HomeView实例...");
                
                if (viewModel == null)
                {
                    return (null, false, "HomeViewModel 为 null");
                }

                var homeView = new HomeView(viewModel);
                Debug.WriteLine("HomeView 实例创建成功");
                
                return (homeView, true, "");
            }
            catch (Exception ex)
            {
                var error = $"创建HomeView失败: {ex.Message}\n堆栈: {ex.StackTrace}";
                Debug.WriteLine(error);
                return (null, false, error);
            }
        }
    }
}
