using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 主页视图 - 应用程序的主要仪表板页面
/// Home view - Main dashboard page of the application
/// </summary>
/// <remarks>
/// 该页面显示用户的工作概览，包括今日班次、收入统计和快捷操作。
/// 采用MVVM模式，所有业务逻辑都在HomeViewModel中处理。
/// </remarks>
public partial class HomeView : ContentPage
{
	private readonly HomeViewModel _viewModel;

	/// <summary>
	/// 初始化主页视图
	/// Initialize HomeView with ViewModel
	/// </summary>
	/// <param name="viewModel">主页视图模型实例</param>
	/// <remarks>
	/// 通过依赖注入获取ViewModel实例，设置数据绑定上下文
	/// </remarks>
	public HomeView(HomeViewModel viewModel)
	{
		try
		{
			System.Diagnostics.Debug.WriteLine("HomeView 构造函数开始执行");

			InitializeComponent();
			_viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
			BindingContext = _viewModel;

			System.Diagnostics.Debug.WriteLine("HomeView 构造函数执行完成");
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"HomeView 构造函数异常: {ex.Message}");
			System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
			System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
			throw; // 重新抛出异常，因为构造函数失败应该阻止对象创建
		}
	}

	/// <summary>
	/// 页面显示时的生命周期方法
	/// Page lifecycle method when appearing
	/// </summary>
	/// <remarks>
	/// 每次页面显示时都会调用ViewModel的OnAppearingAsync方法，
	/// 确保数据是最新的，包括刷新班次信息和统计数据
	/// </remarks>
	protected override async void OnAppearing()
	{
		var stopwatch = System.Diagnostics.Stopwatch.StartNew();
		try
		{
			System.Diagnostics.Debug.WriteLine("HomeView OnAppearing 开始执行");

			base.OnAppearing();

			if (_viewModel == null)
			{
				System.Diagnostics.Debug.WriteLine("错误: HomeView._viewModel 为 null");
				return;
			}

			// 添加超时保护，防止ANR
			using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(45)); // 45秒超时

			try
			{
				var onAppearingTask = _viewModel.OnAppearingAsync();
				await onAppearingTask.WaitAsync(cts.Token);
			}
			catch (OperationCanceledException)
			{
				System.Diagnostics.Debug.WriteLine("警告: HomeView OnAppearing 操作超时");
				await DisplayAlert("提示", "页面加载时间较长，请稍后再试", "确定");
				return;
			}

			stopwatch.Stop();
			System.Diagnostics.Debug.WriteLine($"HomeView OnAppearing 执行完成，耗时: {stopwatch.ElapsedMilliseconds}ms");

			// 如果加载时间过长，记录警告
			if (stopwatch.ElapsedMilliseconds > 10000) // 超过10秒
			{
				System.Diagnostics.Debug.WriteLine($"警告: HomeView 加载耗时过长 ({stopwatch.ElapsedMilliseconds}ms)");
			}
		}
		catch (Exception ex)
		{
			stopwatch.Stop();
			System.Diagnostics.Debug.WriteLine($"HomeView OnAppearing 异常: {ex.Message} (耗时: {stopwatch.ElapsedMilliseconds}ms)");
			System.Diagnostics.Debug.WriteLine($"异常类型: {ex.GetType().Name}");
			System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

			// 尝试显示错误信息给用户
			try
			{
				await DisplayAlert("错误", $"页面加载失败: {ex.Message}", "确定");
			}
			catch
			{
				System.Diagnostics.Debug.WriteLine("无法显示错误对话框");
			}
		}
	}
}