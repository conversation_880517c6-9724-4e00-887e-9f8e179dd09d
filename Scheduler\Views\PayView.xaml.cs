// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 工资视图 / Pay View
/// 显示收入统计、雇主信息和工资计算结果
/// Display income statistics, employer information and salary calculation results
/// </summary>
public partial class PayView : ContentPage
{
    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化工资视图并配置数据绑定 / Initialize pay view and configure data binding
    /// </summary>
    /// <param name="viewModel">工资视图模型 / Pay view model</param>
    public PayView(PayViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        BindingContext = viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载工资数据和统计信息 / Load pay data and statistics
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is PayViewModel viewModel)
        {
            // 执行数据加载命令 / Execute data loading command
            await viewModel.LoadDataCommand.ExecuteAsync(null);
        }
    }
}