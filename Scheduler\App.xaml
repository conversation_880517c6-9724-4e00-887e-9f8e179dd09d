﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Scheduler"
             xmlns:converters="clr-namespace:Scheduler.Converters"
             x:Class="Scheduler.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
            <converters:CountToBoolConverter x:Key="CountToBoolConverter" />
            <converters:BoolToColorConverter x:Key="BoolToColorConverter" />
            <converters:BoolToFontAttributesConverter x:Key="BoolToFontAttributesConverter" />
            <converters:BoolToTextColorConverter x:Key="BoolToTextColorConverter" />
            <converters:BoolToCurrentHourColorConverter x:Key="BoolToCurrentHourColorConverter" />
            <converters:BoolToColorSelectionConverter x:Key="BoolToColorSelectionConverter" />
            <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:BoolToTextConverter x:Key="BoolToTextConverter" />
            <converters:IsNotNullConverter x:Key="IsNotNullConverter" />
            <converters:IntToBoolConverter x:Key="IntToBoolConverter" />

            <!-- Notification Converters -->
            <converters:MinutesToIndexConverter x:Key="MinutesToIndexConverter" />
            <converters:HoursToIndexConverter x:Key="HoursToIndexConverter" />
            <converters:DaysToIndexConverter x:Key="DaysToIndexConverter" />
            <converters:TimeToStringConverter x:Key="TimeToStringConverter" />
            <converters:NotificationTypeToDescriptionConverter x:Key="NotificationTypeToDescriptionConverter" />
            <converters:NotificationStatusToColorConverter x:Key="NotificationStatusToColorConverter" />
            <converters:NotificationTypeToStringConverter x:Key="NotificationTypeToStringConverter" />
            <converters:NotificationStatusToStringConverter x:Key="NotificationStatusToStringConverter" />
            <converters:NotificationTypeToColorConverter x:Key="NotificationTypeToColorConverter" />
            <converters:NotificationStatusToUnreadVisibilityConverter x:Key="NotificationStatusToUnreadVisibilityConverter" />
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
