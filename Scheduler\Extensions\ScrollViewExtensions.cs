using Microsoft.Maui.Controls;

namespace Scheduler.Extensions
{
    /// <summary>
    /// ScrollView扩展方法，提供跨平台滚动优化
    /// ScrollView extension methods providing cross-platform scroll optimization
    /// </summary>
    public static class ScrollViewExtensions
    {
        /// <summary>
        /// 应用滚动优化配置
        /// Apply scroll optimization configuration
        /// </summary>
        /// <param name="scrollView">要优化的ScrollView</param>
        /// <returns>优化后的ScrollView</returns>
        public static ScrollView ApplyScrollOptimization(this ScrollView scrollView)
        {
            if (scrollView == null) return scrollView;

            try
            {
                // 通用滚动优化设置
                // General scroll optimization settings
                scrollView.HorizontalScrollBarVisibility = ScrollBarVisibility.Never;
                scrollView.VerticalScrollBarVisibility = ScrollBarVisibility.Default;

#if ANDROID
                // Android特定优化
                // Android-specific optimization
                scrollView.Loaded += (sender, e) =>
                {
                    System.Diagnostics.Debug.WriteLine("ScrollViewExtensions: Android滚动优化已应用");
                };
#endif

                System.Diagnostics.Debug.WriteLine("ScrollViewExtensions: 通用滚动优化已应用");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ScrollViewExtensions: 优化失败 - {ex.Message}");
            }

            return scrollView;
        }

        /// <summary>
        /// 应用CollectionView优化配置
        /// Apply CollectionView optimization configuration
        /// </summary>
        /// <param name="collectionView">要优化的CollectionView</param>
        /// <returns>优化后的CollectionView</returns>
        public static CollectionView ApplyCollectionOptimization(this CollectionView collectionView)
        {
            if (collectionView == null) return collectionView;

            try
            {
                // 通用CollectionView优化设置
                // General CollectionView optimization settings
                collectionView.RemainingItemsThreshold = 5;
                collectionView.RemainingItemsThresholdReachedCommand = null;

#if ANDROID
                // Android特定优化
                // Android-specific optimization
                collectionView.Loaded += (sender, e) =>
                {
                    System.Diagnostics.Debug.WriteLine("ScrollViewExtensions: Android CollectionView优化已应用");
                };
#endif

                System.Diagnostics.Debug.WriteLine("ScrollViewExtensions: 通用CollectionView优化已应用");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ScrollViewExtensions: CollectionView优化失败 - {ex.Message}");
            }

            return collectionView;
        }
    }
}
