namespace Scheduler.Models
{
    /// <summary>
    /// 提醒时间选项模型 / Reminder Time Option Model
    /// 用于班次通知的提醒时间配置
    /// Used for reminder time configuration in shift notifications
    /// </summary>
    public class ReminderTimeOption
    {
        /// <summary>
        /// 提醒提前时间 / Reminder Advance Time
        /// 提醒提前时间（分钟）
        /// Reminder advance time in minutes
        /// </summary>
        public int Minutes { get; set; }

        /// <summary>
        /// 显示名称 / Display Name
        /// 选项的显示名称
        /// Display name for the option
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 描述信息 / Description
        /// 提醒时间的详细描述
        /// Detailed description of the reminder time
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 获取预定义的提醒时间选项 / Get Predefined Reminder Time Options
        /// 返回系统预设的提醒时间选项列表
        /// Returns a list of system predefined reminder time options
        /// </summary>
        /// <returns>提醒时间选项列表 / List of reminder time options</returns>
        public static List<ReminderTimeOption> GetDefaultOptions()
        {
            return new List<ReminderTimeOption>
            {
                new ReminderTimeOption
                {
                    Minutes = 5,
                    DisplayName = "5 分钟前 (5 minutes before)",
                    Description = "班次开始前5分钟提醒 / Remind 5 minutes before shift starts"
                },
                new ReminderTimeOption
                {
                    Minutes = 10,
                    DisplayName = "10 分钟前 (10 minutes before)",
                    Description = "班次开始前10分钟提醒 / Remind 10 minutes before shift starts"
                },
                new ReminderTimeOption
                {
                    Minutes = 15,
                    DisplayName = "15 分钟前 (15 minutes before)",
                    Description = "班次开始前15分钟提醒（推荐）/ Remind 15 minutes before shift starts (recommended)"
                },
                new ReminderTimeOption
                {
                    Minutes = 30,
                    DisplayName = "30 分钟前 (30 minutes before)",
                    Description = "班次开始前30分钟提醒 / Remind 30 minutes before shift starts"
                },
                new ReminderTimeOption
                {
                    Minutes = 60,
                    DisplayName = "1 小时前 (1 hour before)",
                    Description = "班次开始前1小时提醒 / Remind 1 hour before shift starts"
                }
            };
        }

        /// <summary>
        /// 判断对象是否相等 / Determine if objects are equal
        /// 基于Minutes属性比较对象相等性
        /// Compare object equality based on Minutes property
        /// </summary>
        public override bool Equals(object? obj)
        {
            return obj is ReminderTimeOption option && Minutes == option.Minutes;
        }

        /// <summary>
        /// 获取哈希码 / Get hash code
        /// 基于Minutes属性生成哈希码
        /// Generate hash code based on Minutes property
        /// </summary>
        public override int GetHashCode()
        {
            return Minutes.GetHashCode();
        }

        /// <summary>
        /// 转换为字符串 / Convert to string
        /// 返回显示名称作为字符串表示
        /// Return display name as string representation
        /// </summary>
        public override string ToString()
        {
            return DisplayName;
        }
    }
}
