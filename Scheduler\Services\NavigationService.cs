// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Views;   // 视图页面 / View pages
using Scheduler.Models;  // 数据模型 / Data models

namespace Scheduler.Services
{
    /// <summary>
    /// 导航服务接口 / Navigation Service Interface
    /// 定义应用程序页面导航的标准契约
    /// Defines standard contract for application page navigation
    /// </summary>
    public interface INavigationService
    {
        /// <summary>导航到指定路由 / Navigate to specified route</summary>
        Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null);
        /// <summary>导航到班次详情页面 / Navigate to shift details page</summary>
        Task NavigateToShiftDetailsAsync(int shiftId);
        /// <summary>导航到支付详情页面 / Navigate to payment details page</summary>
        Task NavigateToPaymentDetailsAsync(int paymentId);
        /// <summary>导航到待办事项详情页面 / Navigate to todo details page</summary>
        Task NavigateToTodoDetailsAsync(int todoId);
        /// <summary>导航到通知设置页面 / Navigate to notification settings page</summary>
        Task NavigateToNotificationSettingsAsync();
        /// <summary>导航到通知历史页面 / Navigate to notification history page</summary>
        Task NavigateToNotificationHistoryAsync();
        /// <summary>导航到通知测试页面 / Navigate to notification test page</summary>
        Task NavigateToNotificationTestAsync();
        /// <summary>返回上一页 / Go back to previous page</summary>
        Task GoBackAsync();
    }

    /// <summary>
    /// 导航服务实现 / Navigation Service Implementation
    /// 基于Shell导航的具体实现
    /// Concrete implementation based on Shell navigation
    /// </summary>
    public class NavigationService : INavigationService
    {
        /// <summary>
        /// 导航到指定路由 / Navigate to specified route
        /// 支持带参数和不带参数的导航
        /// Supports navigation with and without parameters
        /// </summary>
        /// <param name="route">路由地址 / Route address</param>
        /// <param name="parameters">导航参数 / Navigation parameters</param>
        public async Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null)
        {
            try
            {
                if (parameters != null)
                {
                    await Shell.Current.GoToAsync(route, parameters);
                }
                else
                {
                    await Shell.Current.GoToAsync(route);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航失败 / Navigation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到班次详情页面
        /// </summary>
        public async Task NavigateToShiftDetailsAsync(int shiftId)
        {
            var parameters = new Dictionary<string, object>
            {
                ["ShiftId"] = shiftId
            };
            
            await NavigateToAsync("//DayView", parameters);
        }

        /// <summary>
        /// 导航到支付详情页面
        /// </summary>
        public async Task NavigateToPaymentDetailsAsync(int paymentId)
        {
            var parameters = new Dictionary<string, object>
            {
                ["PaymentId"] = paymentId
            };
            
            await NavigateToAsync("//PayView", parameters);
        }

        /// <summary>
        /// 导航到待办事项详情页面
        /// </summary>
        public async Task NavigateToTodoDetailsAsync(int todoId)
        {
            var parameters = new Dictionary<string, object>
            {
                ["TodoId"] = todoId
            };
            
            await NavigateToAsync("//TodoListView", parameters);
        }

        /// <summary>
        /// 导航到通知设置页面
        /// </summary>
        public async Task NavigateToNotificationSettingsAsync()
        {
            await NavigateToAsync("//NotificationSettingsView");
        }

        /// <summary>
        /// 导航到通知历史页面
        /// </summary>
        public async Task NavigateToNotificationHistoryAsync()
        {
            await NavigateToAsync("//NotificationHistoryView");
        }

        /// <summary>
        /// 导航到通知测试页面
        /// </summary>
        public async Task NavigateToNotificationTestAsync()
        {
            await NavigateToAsync("//NotificationTestView");
        }

        /// <summary>
        /// 返回上一页
        /// </summary>
        public async Task GoBackAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"返回导航失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 通知导航帮助类
    /// </summary>
    public static class NotificationNavigationHelper
    {
        /// <summary>
        /// 根据通知类型和相关实体ID导航到对应页面
        /// </summary>
        public static async Task NavigateFromNotificationAsync(
            INavigationService navigationService,
            NotificationType notificationType,
            int? relatedEntityId = null)
        {
            try
            {
                switch (notificationType)
                {
                    case NotificationType.WorkReminder:
                        if (relatedEntityId.HasValue)
                        {
                            await navigationService.NavigateToShiftDetailsAsync(relatedEntityId.Value);
                        }
                        else
                        {
                            await navigationService.NavigateToAsync("//DayView");
                        }
                        break;

                    case NotificationType.PaymentDue:
                        if (relatedEntityId.HasValue)
                        {
                            await navigationService.NavigateToPaymentDetailsAsync(relatedEntityId.Value);
                        }
                        else
                        {
                            await navigationService.NavigateToAsync("//PayView");
                        }
                        break;

                    case NotificationType.BreakReminder:
                        // 休息提醒通常导航到主页或当前工作页面
                        await navigationService.NavigateToAsync("//HomeView");
                        break;

                    case NotificationType.ShiftConflict:
                        // 班次冲突导航到日程页面
                        await navigationService.NavigateToAsync("//DayView");
                        break;

                    case NotificationType.SystemUpdate:
                        // 系统更新导航到设置页面
                        await navigationService.NavigateToNotificationSettingsAsync();
                        break;

                    default:
                        // 默认导航到主页
                        await navigationService.NavigateToAsync("//HomeView");
                        break;
                }

                System.Diagnostics.Debug.WriteLine($"通知导航完成: {notificationType} -> EntityId: {relatedEntityId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通知导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理通知点击事件
        /// </summary>
        public static async Task HandleNotificationClickAsync(
            INavigationService navigationService,
            int notificationId,
            NotificationType notificationType,
            int? relatedEntityId = null)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"处理通知点击: NotificationId={notificationId}, Type={notificationType}, EntityId={relatedEntityId}");

                // 导航到相关页面
                await NavigateFromNotificationAsync(navigationService, notificationType, relatedEntityId);

                // 这里可以添加标记通知为已读的逻辑
                // await notificationService.MarkNotificationAsReadAsync(notificationId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理通知点击失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取通知类型的默认导航路由
        /// </summary>
        public static string GetDefaultRouteForNotificationType(NotificationType notificationType)
        {
            return notificationType switch
            {
                NotificationType.WorkReminder => "//DayView",
                NotificationType.PaymentDue => "//PayView",
                NotificationType.BreakReminder => "//HomeView",
                NotificationType.ShiftConflict => "//DayView",
                NotificationType.SystemUpdate => "//NotificationSettingsView",
                _ => "//HomeView"
            };
        }

        /// <summary>
        /// 检查通知是否需要特定的实体ID
        /// </summary>
        public static bool RequiresEntityId(NotificationType notificationType)
        {
            return notificationType switch
            {
                NotificationType.WorkReminder => true,
                NotificationType.PaymentDue => true,
                NotificationType.BreakReminder => false,
                NotificationType.ShiftConflict => true,
                NotificationType.SystemUpdate => false,
                _ => false
            };
        }
    }
}
