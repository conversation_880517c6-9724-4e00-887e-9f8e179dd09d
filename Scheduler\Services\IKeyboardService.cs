namespace Scheduler.Services
{
    /// <summary>
    /// 键盘服务接口 / Keyboard Service Interface
    /// 用于强制显示/隐藏软键盘的平台特定功能
    /// Used for platform-specific functionality to force show/hide soft keyboard
    /// </summary>
    public interface IKeyboardService
    {
        /// <summary>
        /// 强制显示软键盘 / Force show soft keyboard
        /// 在某些情况下强制弹出虚拟键盘
        /// Force popup virtual keyboard in certain situations
        /// </summary>
        void ForceShowKeyboard();

        /// <summary>
        /// 强制隐藏软键盘 / Force hide soft keyboard
        /// 强制收起虚拟键盘
        /// Force dismiss virtual keyboard
        /// </summary>
        void ForceHideKeyboard();

        /// <summary>
        /// 检查软键盘是否可见 / Check if soft keyboard is visible
        /// 检测当前虚拟键盘的显示状态
        /// Detect current virtual keyboard display state
        /// </summary>
        /// <returns>如果键盘可见返回true，否则返回false / Returns true if keyboard is visible, false otherwise</returns>
        bool IsKeyboardVisible();
    }
}
