<?xml version="1.0" encoding="utf-8" ?>
<!--
    编辑雇主视图 / Edit Employer View
    用于编辑现有雇主信息的表单页面
    Form page for editing existing employer information
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Scheduler.ViewModels"
             xmlns:models="clr-namespace:Scheduler.Models"
             x:Class="Scheduler.Views.EditEmployerView"
             x:DataType="viewmodels:EditEmployerViewModel"
             Title="{Binding PageTitle}"
             BackgroundColor="#F8F9FA"
             Shell.FlyoutBehavior="Disabled"
             Shell.TabBarIsVisible="False">

    <!-- 返回按钮配置 / Back button configuration -->
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="True" IsVisible="True" />
    </Shell.BackButtonBehavior>

    <ScrollView>
        <VerticalStackLayout Spacing="20" Margin="20">

            <!-- 页面标题区域 / Page title area -->
            <Frame BackgroundColor="#007ACC"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding LocalizedTexts.EditEmployer}"
                           FontSize="24"
                           FontAttributes="Bold"
                           TextColor="White"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding LocalizedTexts.EditEmployerDescription}"
                           FontSize="14"
                           TextColor="White"
                           Opacity="0.8"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <!-- 基本信息区域 / Basic information area -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.BasicInformation}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>

                    <!-- 雇主姓名 -->
                    <VerticalStackLayout Spacing="5">
                        <Label FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span Text="{Binding LocalizedTexts.EmployerName}" />
                                    <Span Text=" " />
                                    <Span Text="*" TextColor="Red" FontAttributes="Bold" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Entry Text="{Binding Name}"
                               Placeholder="{Binding LocalizedTexts.EmployerNamePlaceholder}"
                               BackgroundColor="{Binding NameEntryBackgroundColor}"
                               TextColor="#333"
                               ReturnType="Next"
                               x:Name="NameEntry"
                               AutomationId="EmployerNameEntry" />
                    </VerticalStackLayout>

                    <!-- 联系方式 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="{Binding LocalizedTexts.ContactInfo}"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Entry Text="{Binding ContactInfo}"
                               Placeholder="{Binding LocalizedTexts.ContactInfoPlaceholder}"
                               BackgroundColor="#F8F9FA"
                               TextColor="#333"
                               Keyboard="Email"
                               ReturnType="Next"
                               x:Name="ContactInfoEntry" />
                    </VerticalStackLayout>

                    <!-- 工作地点 -->
                    <VerticalStackLayout Spacing="5">
                        <Label Text="{Binding LocalizedTexts.WorkLocation}"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>
                        <Entry Text="{Binding WorkLocation}"
                               Placeholder="{Binding LocalizedTexts.WorkLocationPlaceholder}"
                               BackgroundColor="#F8F9FA"
                               TextColor="#333"
                               ReturnType="Next"
                               x:Name="WorkLocationEntry" />
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </Frame>

            <!-- 薪资信息区域 -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.SalaryInformation}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>

                    <!-- 时薪 -->
                    <VerticalStackLayout Spacing="5">
                        <Label FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span Text="{Binding LocalizedTexts.HourlyRate}" />
                                    <Span Text=" " />
                                    <Span Text="*" TextColor="Red" FontAttributes="Bold" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Entry Text="{Binding HourlyRate}"
                               Placeholder="0.00"
                               Keyboard="Numeric"
                               BackgroundColor="{Binding HourlyRateEntryBackgroundColor}"
                               TextColor="#333"
                               ReturnType="Next"
                               x:Name="HourlyRateEntry" />
                    </VerticalStackLayout>

                    <!-- 支付周期 -->
                    <VerticalStackLayout Spacing="5">
                        <Label FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span Text="{Binding LocalizedTexts.PaymentCycleDays}" />
                                    <Span Text=" " />
                                    <Span Text="*" TextColor="Red" FontAttributes="Bold" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Entry Text="{Binding PaymentCycleDays}"
                               Placeholder="30"
                               Keyboard="Numeric"
                               BackgroundColor="{Binding PaymentCycleDaysEntryBackgroundColor}"
                               TextColor="#333"
                               ReturnType="Done"
                               x:Name="PaymentCycleDaysEntry" />
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </Frame>

            <!-- 显示设置区域 -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="{Binding LocalizedTexts.DisplaySettings}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>

                    <!-- 标识颜色 -->
                    <VerticalStackLayout Spacing="15">
                        <Label Text="{Binding LocalizedTexts.IdentificationColor}"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"/>

                        <!-- 颜色选择网格 -->
                        <CollectionView ItemsSource="{Binding ColorOptions}"
                                        SelectionMode="None"
                                        HeightRequest="155">
                            <CollectionView.ItemsLayout>
                                <GridItemsLayout Orientation="Vertical"
                                                 Span="5"
                                                 HorizontalItemSpacing="12"
                                                 VerticalItemSpacing="12"/>
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:ColorOption">
                                    <VerticalStackLayout Spacing="5">
                                        <!-- 颜色圆形按钮 -->
                                        <Border BackgroundColor="{Binding HexValue}"
                                                WidthRequest="50"
                                                HeightRequest="50"
                                                StrokeThickness="{Binding IsSelected, Converter={StaticResource BoolToColorSelectionConverter}, ConverterParameter=BorderWidth}"
                                                Stroke="{Binding IsSelected, Converter={StaticResource BoolToColorSelectionConverter}, ConverterParameter=BorderColor}">
                                            <Border.StrokeShape>
                                                <RoundRectangle CornerRadius="25"/>
                                            </Border.StrokeShape>

                                            <!-- 选中标记 -->
                                            <Label Text="✓"
                                                   FontSize="18"
                                                   FontAttributes="Bold"
                                                   TextColor="White"
                                                   HorizontalOptions="Center"
                                                   VerticalOptions="Center"
                                                   IsVisible="{Binding IsSelected, Converter={StaticResource BoolToColorSelectionConverter}, ConverterParameter=CheckmarkVisibility}"/>

                                            <Border.GestureRecognizers>
                                                <TapGestureRecognizer
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:EditEmployerViewModel}}, Path=SelectColorCommand}"
                                                    CommandParameter="{Binding .}"/>
                                            </Border.GestureRecognizers>
                                        </Border>

                                        <!-- 颜色名称 -->
                                        <Label Text=""
                                               FontSize="10"
                                               TextColor="#666"
                                               HorizontalOptions="Center"
                                               HorizontalTextAlignment="Center"/>
                                    </VerticalStackLayout>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <!-- 当前选中颜色显示 -->
                        <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                            <Label Text="{Binding LocalizedTexts.CurrentColor}"
                                   FontSize="14"
                                   TextColor="#666"
                                   VerticalOptions="Center"/>
                            <Border BackgroundColor="{Binding Color}"
                                    WidthRequest="24"
                                    HeightRequest="24">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="12"/>
                                </Border.StrokeShape>
                            </Border>
                            <Label Text="{Binding Color}"
                                   FontSize="12"
                                   FontFamily="Consolas"
                                   TextColor="#666"
                                   VerticalOptions="Center"/>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>

                    <!-- 在职状态 -->
                    <HorizontalStackLayout Spacing="10">
                        <Switch IsToggled="{Binding IsActive}"
                                OnColor="#007ACC"/>
                        <Label Text="{Binding LocalizedTexts.EmploymentStatus}"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="#333"
                               VerticalOptions="Center"/>
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>

            <!-- 备注区域 -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="{Binding LocalizedTexts.NotesInformation}"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#007ACC"/>
                    <Editor Text="{Binding Notes}"
                            Placeholder="{Binding LocalizedTexts.NotesPlaceholder}"
                            HeightRequest="100"
                            BackgroundColor="#F8F9FA"
                            TextColor="#333"
                            x:Name="NotesEditor" />
                </VerticalStackLayout>
            </Frame>

            <!-- 操作按钮区域 -->
            <VerticalStackLayout Spacing="15">
                <!-- 保存修改按钮 -->
                <Button Text="{Binding LocalizedTexts.SaveChanges}"
                        Command="{Binding SaveCommand}"
                        BackgroundColor="#28A745"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        CornerRadius="25"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}"/>

                <!-- 取消编辑按钮 -->
                <Button Text="{Binding LocalizedTexts.CancelEdit}"
                        Command="{Binding CancelCommand}"
                        BackgroundColor="#6C757D"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50"
                        CornerRadius="25"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InverseBoolConverter}}"/>
            </VerticalStackLayout>

            <!-- 加载指示器 -->
            <ActivityIndicator IsVisible="{Binding IsBusy}"
                               IsRunning="{Binding IsBusy}"
                               Color="#007ACC"
                               HeightRequest="50"/>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
