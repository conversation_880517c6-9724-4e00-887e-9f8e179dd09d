// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 所有付款记录视图 / All Payment Records View
/// 显示所有付款记录的历史和统计信息，支持筛选和管理
/// Display history and statistics of all payment records with filtering and management support
/// </summary>
public partial class AllPaymentRecordsView : ContentPage
{
    // 所有付款记录视图模型实例 / All payment records view model instance
    private AllPaymentRecordsViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化所有付款记录视图并配置数据绑定 / Initialize all payment records view and configure data binding
    /// </summary>
    /// <param name="viewModel">所有付款记录视图模型 / All payment records view model</param>
    public AllPaymentRecordsView(AllPaymentRecordsViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 自动刷新数据确保显示最新信息 / Auto refresh data to ensure latest information is displayed
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        System.Diagnostics.Debug.WriteLine("AllPaymentRecordsView: 页面出现，开始自动刷新数据 / Page appearing, starting auto refresh");

        // 自动刷新数据，确保显示最新信息 / Auto refresh data to ensure latest information is displayed
        if (_viewModel != null)
        {
            await _viewModel.RefreshCommand.ExecuteAsync(null);
        }
    }
}
