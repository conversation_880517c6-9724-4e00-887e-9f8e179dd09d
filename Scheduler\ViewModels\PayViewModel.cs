using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using System.Collections.ObjectModel;
using Scheduler.Models;
using Scheduler.Services;
using Scheduler.ViewModels;

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 工资页面视图模型 - 管理薪资统计和支付确认功能
    /// Pay page ViewModel - manages salary statistics and payment confirmation functionality
    /// </summary>
    /// <remarks>
    /// 该ViewModel负责处理薪资相关的所有业务逻辑，包括：
    /// - 雇主信息展示和时薪管理
    /// - 周/月收入统计切换
    /// - 5年总收入历史显示
    /// - 收入确认待办事项管理
    /// - 多语言支持和实时数据同步
    /// </remarks>
    public partial class PayViewModel : BaseViewModel, IRecipient<EmployerUpdatedMessage>, IRecipient<LanguageChangedMessage>, IRecipient<DataResetMessage>, IRecipient<PaymentStatusChangedMessage>, IRecipient<PaymentRecordUpdatedMessage>
    {
        private readonly DatabaseService _databaseService;
        private readonly LocalizationService _localizationService;
        private readonly TodoService _todoService;
        private readonly PayrollService _payrollService;

        /// <summary>
        /// 初始化工资页面视图模型
        /// Initialize PayViewModel with required services
        /// </summary>
        /// <param name="databaseService">数据库服务实例</param>
        /// <remarks>
        /// 构造函数中会初始化所有必要的集合，设置默认视图状态，
        /// 注册消息监听器以实现实时数据同步
        /// </remarks>
        public PayViewModel(DatabaseService databaseService, TodoService todoService, PayrollService payrollService)
        {
            _databaseService = databaseService;
            _todoService = todoService;
            _payrollService = payrollService;
            _localizationService = LocalizationService.Instance;
            Title = "Pay";

            // 初始化集合 - 使用属性而不是字段
            Employers = new ObservableCollection<Employer>();
            PaymentTodoItems = new ObservableCollection<PaymentTodoItem>();
            RecentConfirmedItems = new ObservableCollection<PaymentTodoItem>();
            YearlyIncomes = new ObservableCollection<YearlyIncomeItem>();

            // 设置默认值
            IsWeeklyView = true;
            CurrentPeriodText = "This Week";

            // 初始化时间范围文本
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);
            CurrentPeriodRangeText = $"{startOfWeek:yyyy年M月d日} - {endOfWeek:yyyy年M月d日}";

            // 注册消息接收
            WeakReferenceMessenger.Default.Register<EmployerUpdatedMessage>(this);
            WeakReferenceMessenger.Default.Register<LanguageChangedMessage>(this);
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
            WeakReferenceMessenger.Default.Register<PaymentStatusChangedMessage>(this);
            WeakReferenceMessenger.Default.Register<PaymentRecordUpdatedMessage>(this);

            // 添加调试日志
            System.Diagnostics.Debug.WriteLine("PayViewModel: 构造函数完成，Employers集合已初始化");
        }

        #region Observable Properties

        /// <summary>
        /// 雇主列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<Employer> employers;

        /// <summary>
        /// 待确认支付记录（Todo List样式）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PaymentTodoItem> paymentTodoItems;

        /// <summary>
        /// 最近已确认的支付记录（最新5条）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PaymentTodoItem> recentConfirmedItems;

        /// <summary>
        /// 是否没有支付记录
        /// </summary>
        public bool HasNoPaymentRecords => PaymentTodoItems.Count == 0 && RecentConfirmedItems.Count == 0;

        /// <summary>
        /// 是否为周视图（否则为月视图）
        /// </summary>
        [ObservableProperty]
        private bool isWeeklyView;

        /// <summary>
        /// 当前时间段文本
        /// </summary>
        [ObservableProperty]
        private string currentPeriodText;

        /// <summary>
        /// 当前时间段范围文本（用于显示具体的日期范围）
        /// </summary>
        [ObservableProperty]
        private string currentPeriodRangeText;

        /// <summary>
        /// 当前时间段收入
        /// </summary>
        [ObservableProperty]
        private decimal currentPeriodIncome;

        /// <summary>
        /// 当前时间段工作时长
        /// </summary>
        [ObservableProperty]
        private double currentPeriodHours;

        /// <summary>
        /// 总确认收入
        /// </summary>
        [ObservableProperty]
        private decimal totalConfirmedIncome;

        // 本地化文本属性
        public string EmployerDirectoryText => _localizationService.GetLocalizedString("EmployerDirectory");
        public string NoEmployersFoundText => _localizationService.GetLocalizedString("NoEmployersFoundInPay");
        public string FiveYearIncomeHistoryText => _localizationService.GetLocalizedString("FiveYearIncomeHistory");
        public string PaymentTodoListText => _localizationService.GetLocalizedString("PaymentTodoList");
        public string PendingText => _localizationService.GetLocalizedString("Pending");
        public string ConfirmedText => _localizationService.GetLocalizedString("Confirmed");
        public string TotalIncomeText => _localizationService.GetLocalizedString("TotalIncome");
        public string NoPaymentRecordsText => _localizationService.GetLocalizedString("NoPaymentRecords");
        public string ToggleText => _localizationService.GetLocalizedString("Toggle");
        public string ManageEmployersText => _localizationService.GetLocalizedString("ManageEmployers");
        public string PendingPaymentsText => _localizationService.GetLocalizedString("PendingPayments");
        public string RecentlyConfirmedText => _localizationService.GetLocalizedString("RecentlyConfirmed");

        /// <summary>
        /// 近5年收入数据
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<YearlyIncomeItem> yearlyIncomes;

        /// <summary>
        /// 已确认支付记录数量
        /// </summary>
        [ObservableProperty]
        private int confirmedCount;

        /// <summary>
        /// 待确认记录数量
        /// </summary>
        [ObservableProperty]
        private int pendingCount;

        #endregion

        #region Commands

        /// <summary>
        /// 页面加载命令
        /// </summary>
        [RelayCommand]
        private async Task LoadDataAsync()
        {
            System.Diagnostics.Debug.WriteLine("PayViewModel: 开始加载所有数据");

            await SafeExecuteAsync(async () =>
            {
                // 加载雇主列表
                System.Diagnostics.Debug.WriteLine("PayViewModel: 准备加载雇主列表");
                await LoadEmployersAsync();

                // 加载当前时间段收入
                await LoadCurrentPeriodIncomeAsync();

                // 加载年度收入统计
                await LoadYearlyIncomeAsync();

                // 自动生成支付记录（从shift数据）
                await AutoGeneratePaymentRecordsAsync();

                // 加载待确认支付记录
                await LoadPaymentTodoItemsAsync();

                // 计算总确认收入
                await LoadTotalConfirmedIncomeAsync();

                System.Diagnostics.Debug.WriteLine("PayViewModel: 所有数据加载完成");
            });
        }

        /// <summary>
        /// 切换周/月视图命令
        /// </summary>
        [RelayCommand]
        private async Task TogglePeriodViewAsync()
        {
            IsWeeklyView = !IsWeeklyView;
            await LoadCurrentPeriodIncomeAsync();
        }

        /// <summary>
        /// 导航到所有雇主页面
        /// </summary>
        [RelayCommand]
        private async Task NavigateToAllEmployersAsync()
        {
            await Shell.Current.GoToAsync("AllEmployer");
        }

        /// <summary>
        /// 导航到所有支付记录页面
        /// </summary>
        [RelayCommand]
        private async Task ViewAllPaymentRecordsAsync()
        {
            await Shell.Current.GoToAsync("AllPaymentRecords");
        }

        /// <summary>
        /// 切换支付确认状态命令
        /// </summary>
        [RelayCommand]
        private async Task TogglePaymentConfirmationAsync(PaymentTodoItem todoItem)
        {
            if (todoItem == null) return;

            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 切换支付确认状态 - {todoItem.EmployerName}, 当前状态: {todoItem.IsConfirmed}");

                if (!todoItem.IsConfirmed)
                {
                    // 暂时跳过工作时间检查
                    // TODO: 重新实现工作时间检测逻辑

                    // 确认支付
                    await _databaseService.ConfirmPaymentAsync(todoItem.PaymentRecord);
                    todoItem.IsConfirmed = true;
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 支付已确认 - {todoItem.EmployerName}");

                    // 发送支付记录更新消息
                    WeakReferenceMessenger.Default.Send(new PaymentRecordUpdatedMessage
                    {
                        PaymentRecordId = todoItem.PaymentRecord.Id,
                        Action = "Confirmed",
                        EmployerId = todoItem.PaymentRecord.EmployerId,
                        Amount = todoItem.PaymentRecord.TotalAmount,
                        UpdateTime = DateTime.Now
                    });
                }
                else
                {
                    // 取消确认
                    todoItem.PaymentRecord.Status = PaymentStatus.Pending;
                    todoItem.PaymentRecord.ActualPaymentDate = null;
                    await _databaseService.UpdatePaymentRecordAsync(todoItem.PaymentRecord);
                    todoItem.IsConfirmed = false;
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 支付确认已取消 - {todoItem.EmployerName}");

                    // 发送支付记录更新消息
                    WeakReferenceMessenger.Default.Send(new PaymentRecordUpdatedMessage
                    {
                        PaymentRecordId = todoItem.PaymentRecord.Id,
                        Action = "Cancelled",
                        EmployerId = todoItem.PaymentRecord.EmployerId,
                        Amount = todoItem.PaymentRecord.TotalAmount,
                        UpdateTime = DateTime.Now
                    });
                }

                // 重新加载数据以更新列表显示
                await LoadPaymentTodoItemsAsync();

                // 更新所有统计数据（只计算已确认的收入）
                await LoadTotalConfirmedIncomeAsync();
                await LoadCurrentPeriodIncomeAsync();
                await LoadYearlyIncomeAsync(); // 年度收入统计也需要实时更新

                // 自动创建或更新相关的待办事项
                await AutoCreatePaymentTodoItemsAsync();

                System.Diagnostics.Debug.WriteLine($"PayViewModel: 所有统计数据已更新（包括年度收入）");
            });
        }

        /// <summary>
        /// 刷新数据命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            IsRefreshing = true;
            await LoadDataAsync();
            IsRefreshing = false;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 加载雇主列表
        /// </summary>
        private async Task LoadEmployersAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 开始加载雇主列表");

                var employers = await _databaseService.GetEmployersAsync();
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 从数据库获取到 {employers.Count} 个雇主");

                // 确保在UI线程上更新集合
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Employers.Clear();
                    foreach (var employer in employers)
                    {
                        Employers.Add(employer);
                        System.Diagnostics.Debug.WriteLine($"PayViewModel: 添加雇主 - {employer.Name}, 时薪: ${employer.HourlyRate:F2}");
                    }

                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 雇主列表更新完成，当前集合数量: {Employers.Count}");

                    // 通知UI更新
                    OnPropertyChanged(nameof(Employers));
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 加载雇主列表失败 - {ex.Message}");
                var loadFailedText = _localizationService.GetLocalizedString("LoadFailed");
                var unableToLoadEmployerInfoText = _localizationService.GetLocalizedString("UnableToLoadEmployerInfo");
                await ShowErrorAsync(loadFailedText, $"{unableToLoadEmployerInfoText}: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载当前时间段收入（仅计算已确认的支付记录）
        /// Load current period income (only confirmed payment records)
        /// </summary>
        private async Task LoadCurrentPeriodIncomeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 开始加载{(IsWeeklyView ? "周" : "月")}收入数据（仅已确认记录）");

                // 直接获取已确认的支付记录，避免在ViewModel层过滤
                var confirmedRecords = IsWeeklyView
                    ? await _databaseService.GetWeeklyConfirmedPaymentRecordsAsync()
                    : await _databaseService.GetMonthlyConfirmedPaymentRecordsAsync();

                System.Diagnostics.Debug.WriteLine($"PayViewModel: 获取到 {confirmedRecords.Count} 条已确认支付记录");

                // 计算已确认收入和工作小时
                CurrentPeriodIncome = confirmedRecords.Sum(r => r.TotalAmount);
                CurrentPeriodHours = confirmedRecords.Sum(r => r.TotalHours);

                System.Diagnostics.Debug.WriteLine($"PayViewModel: 已确认收入: ${CurrentPeriodIncome:F2}, 工作小时: {CurrentPeriodHours:F1}");

                // 更新时间段文本和范围文本
                if (IsWeeklyView)
                {
                    CurrentPeriodText = "This Week";
                    var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                    var endOfWeek = startOfWeek.AddDays(6);
                    CurrentPeriodRangeText = $"{startOfWeek:yyyy年M月d日} - {endOfWeek:yyyy年M月d日}";
                }
                else
                {
                    CurrentPeriodText = "This Month";
                    var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                    var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                    CurrentPeriodRangeText = $"{startOfMonth:yyyy年M月d日} - {endOfMonth:yyyy年M月d日}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 加载当前时间段收入失败 - {ex.Message}");
                CurrentPeriodIncome = 0;
                CurrentPeriodHours = 0;
                CurrentPeriodRangeText = "";
            }
        }

        /// <summary>
        /// 加载年度收入统计
        /// </summary>
        private async Task LoadYearlyIncomeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 开始加载年度收入统计");

                var yearlyData = await _databaseService.GetYearlyIncomeAsync();

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    YearlyIncomes.Clear();

                    foreach (var item in yearlyData.OrderByDescending(x => x.Key))
                    {
                        YearlyIncomes.Add(new YearlyIncomeItem
                        {
                            Year = item.Key,
                            Income = item.Value
                        });
                        System.Diagnostics.Debug.WriteLine($"PayViewModel: 年度收入 {item.Key}: ${item.Value:F2}");
                    }

                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 年度收入统计加载完成，共 {YearlyIncomes.Count} 年数据");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 加载年度收入统计失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载支付Todo项目
        /// </summary>
        private async Task LoadPaymentTodoItemsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 开始加载支付Todo项目");

                var allPayments = await _databaseService.GetPaymentRecordsAsync();
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 获取到 {allPayments.Count} 条支付记录");

                // 分离待确认和已确认的记录
                var pendingPayments = allPayments.Where(p => p.Status != PaymentStatus.Paid).OrderByDescending(p => p.PeriodEnd).ToList();
                var confirmedPayments = allPayments.Where(p => p.Status == PaymentStatus.Paid).OrderByDescending(p => p.ActualPaymentDate ?? p.PeriodEnd).Take(5).ToList();

                // 预先获取班次信息（在主线程外执行）
                var pendingTodoItems = new List<PaymentTodoItem>();
                foreach (var payment in pendingPayments)
                {
                    try
                    {
                        var latestShiftEndTime = await GetLatestShiftEndTimeAsync(payment);
                        var canConfirm = !latestShiftEndTime.HasValue || DateTime.Now >= latestShiftEndTime.Value;

                        var todoItem = new PaymentTodoItem
                        {
                            PaymentRecord = payment,
                            IsConfirmed = false,
                            EmployerName = GetEmployerName(payment.EmployerId),
                            LatestShiftEndTime = latestShiftEndTime,
                            CanConfirm = canConfirm
                        };
                        pendingTodoItems.Add(todoItem);
                        System.Diagnostics.Debug.WriteLine($"PayViewModel: 添加待确认项目 - {todoItem.EmployerName}, ${payment.TotalAmount:F2}, 可确认: {todoItem.CanConfirm}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"PayViewModel: 创建待确认项目失败 - {ex.Message}");
                        // 创建一个简化的项目，不包含班次信息
                        var fallbackItem = new PaymentTodoItem
                        {
                            PaymentRecord = payment,
                            IsConfirmed = false,
                            EmployerName = GetEmployerName(payment.EmployerId),
                            LatestShiftEndTime = null,
                            CanConfirm = true // 允许确认
                        };
                        pendingTodoItems.Add(fallbackItem);
                    }
                }

                var confirmedTodoItems = new List<PaymentTodoItem>();
                foreach (var payment in confirmedPayments)
                {
                    var todoItem = new PaymentTodoItem
                    {
                        PaymentRecord = payment,
                        IsConfirmed = true,
                        EmployerName = GetEmployerName(payment.EmployerId)
                    };
                    confirmedTodoItems.Add(todoItem);
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 添加已确认项目 - {todoItem.EmployerName}, ${payment.TotalAmount:F2}");
                }

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // 清空现有数据
                    PaymentTodoItems.Clear();
                    RecentConfirmedItems.Clear();

                    // 添加待确认的支付记录
                    foreach (var todoItem in pendingTodoItems)
                    {
                        PaymentTodoItems.Add(todoItem);
                    }

                    // 添加最近已确认的支付记录（最新5条）
                    foreach (var todoItem in confirmedTodoItems)
                    {
                        RecentConfirmedItems.Add(todoItem);
                    }

                    UpdateConfirmedCount();
                    OnPropertyChanged(nameof(HasNoPaymentRecords));
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 支付Todo项目加载完成 - 待确认: {PaymentTodoItems.Count}, 已确认: {RecentConfirmedItems.Count}");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 加载支付Todo项目失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 更新确认数量统计
        /// </summary>
        private void UpdateConfirmedCount()
        {
            PendingCount = PaymentTodoItems.Count(x => !x.IsConfirmed);
            ConfirmedCount = PaymentTodoItems.Count(x => x.IsConfirmed) + RecentConfirmedItems.Count;
        }

        /// <summary>
        /// 获取支付记录对应的最晚班次结束时间
        /// </summary>
        private async Task<DateTime?> GetLatestShiftEndTimeAsync(PaymentRecord payment)
        {
            try
            {
                // 暂时简化逻辑，避免复杂的数据库查询
                await Task.Delay(1); // 保持异步方法签名
                return null; // 暂时返回null，表示总是允许确认
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 获取班次结束时间失败 - {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 自动创建支付相关的待办事项
        /// </summary>
        private async Task AutoCreatePaymentTodoItemsAsync()
        {
            try
            {
                // 获取TodoService实例
                var todoService = _todoService;
                if (todoService != null)
                {
                    // 自动从未确认的支付记录创建待办事项
                    var createdCount = await todoService.AutoCreatePaymentTodoItemsAsync();
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 自动创建了 {createdCount} 个支付相关待办事项");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 自动创建支付待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 同步待办事项状态
        /// </summary>
        private async Task SyncTodoItemStatusAsync()
        {
            try
            {
                // 获取TodoService实例
                var todoService = _todoService;
                if (todoService != null)
                {
                    // 同步待办事项状态
                    var syncedCount = await todoService.SyncTodoItemStatusAsync();
                    System.Diagnostics.Debug.WriteLine($"PayViewModel: 同步了 {syncedCount} 个待办事项状态");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 同步待办事项状态失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 自动从shift数据生成PaymentRecord
        /// </summary>
        private async Task AutoGeneratePaymentRecordsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 开始自动生成支付记录");

                // 获取PayrollService实例
                var payrollService = _payrollService;
                if (payrollService != null)
                {
                    // 自动生成支付记录
                    await payrollService.GeneratePaymentRecordsAsync();
                    System.Diagnostics.Debug.WriteLine("PayViewModel: 支付记录自动生成完成");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 自动生成支付记录失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载总确认收入
        /// </summary>
        private async Task LoadTotalConfirmedIncomeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 开始加载总确认收入");

                TotalConfirmedIncome = await _databaseService.GetTotalConfirmedIncomeAsync();

                System.Diagnostics.Debug.WriteLine($"PayViewModel: 总确认收入: ${TotalConfirmedIncome:F2}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 加载总确认收入失败 - {ex.Message}");
                TotalConfirmedIncome = 0;
            }
        }

        /// <summary>
        /// 根据雇主ID获取雇主名称
        /// </summary>
        private string GetEmployerName(int employerId)
        {
            var employer = Employers.FirstOrDefault(e => e.Id == employerId);
            return employer?.Name ?? _localizationService.GetLocalizedString("UnknownEmployer");
        }

        /// <summary>
        /// 接收雇主更新消息
        /// </summary>
        public void Receive(EmployerUpdatedMessage message)
        {
            System.Diagnostics.Debug.WriteLine("PayViewModel: 收到雇主更新消息，重新加载雇主列表");
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await LoadEmployersAsync();
            });
        }

        /// <summary>
        /// 接收语言更改消息
        /// </summary>
        public void Receive(LanguageChangedMessage message)
        {
            // 语言更改时，重新加载数据以更新本地化文本
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // 通知所有本地化文本属性更改
                OnPropertyChanged(nameof(EmployerDirectoryText));
                OnPropertyChanged(nameof(NoEmployersFoundText));
                OnPropertyChanged(nameof(FiveYearIncomeHistoryText));
                OnPropertyChanged(nameof(PaymentTodoListText));
                OnPropertyChanged(nameof(PendingText));
                OnPropertyChanged(nameof(ConfirmedText));
                OnPropertyChanged(nameof(TotalIncomeText));
                OnPropertyChanged(nameof(NoPaymentRecordsText));
                OnPropertyChanged(nameof(ToggleText));

                await LoadDataAsync();
            });
        }

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            // 当数据被重置时，刷新薪资数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: Received data reset message at {message.ResetTime}");

                // 清空当前数据
                Employers.Clear();
                PaymentTodoItems.Clear();
                RecentConfirmedItems.Clear();
                YearlyIncomes.Clear();

                // 重置统计数据
                CurrentPeriodIncome = 0;
                TotalConfirmedIncome = 0;
                CurrentPeriodHours = 0;

                // 强制通知UI更新
                OnPropertyChanged(nameof(CurrentPeriodIncome));
                OnPropertyChanged(nameof(TotalConfirmedIncome));
                OnPropertyChanged(nameof(CurrentPeriodHours));

                // 重新加载数据（应该为空）
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine($"PayViewModel: Data refreshed after reset - Employers: {Employers.Count}, PaymentTodoItems: {PaymentTodoItems.Count}, RecentConfirmedItems: {RecentConfirmedItems.Count}");
            });
        }

        /// <summary>
        /// 接收支付状态变更消息
        /// </summary>
        public void Receive(PaymentStatusChangedMessage message)
        {
            // 当支付状态变更时，刷新支付相关数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine("PayViewModel: 收到支付状态变更消息，重新加载支付数据");

                // 重新加载支付待办事项
                await LoadPaymentTodoItemsAsync();

                // 重新加载收入统计
                await LoadCurrentPeriodIncomeAsync();
                await LoadTotalConfirmedIncomeAsync();

                System.Diagnostics.Debug.WriteLine("PayViewModel: 支付数据刷新完成");
            });
        }

        /// <summary>
        /// 接收支付记录更新消息
        /// </summary>
        public void Receive(PaymentRecordUpdatedMessage message)
        {
            // 当支付记录更新时，刷新支付相关数据
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"PayViewModel: 收到支付记录更新消息 - {message.PaymentRecordId}, 操作: {message.Action}");

                // 重新加载支付待办事项
                await LoadPaymentTodoItemsAsync();

                // 重新加载所有收入统计（确保数据一致性）
                await LoadCurrentPeriodIncomeAsync();
                await LoadTotalConfirmedIncomeAsync();
                await LoadYearlyIncomeAsync(); // 年度收入统计也需要实时更新

                // 同步相关的待办事项状态
                await SyncTodoItemStatusAsync();

                System.Diagnostics.Debug.WriteLine("PayViewModel: 支付记录更新处理完成（包括年度收入统计）");
            });
        }

        #endregion
    }

    /// <summary>
    /// 年度收入项目
    /// </summary>
    public class YearlyIncomeItem
    {
        public int Year { get; set; }
        public decimal Income { get; set; }
        public string DisplayText => $"{Year}: ${Income:F2}";
    }

    /// <summary>
    /// 支付Todo项目（用于Todo List样式显示）
    /// </summary>
    public partial class PaymentTodoItem : ObservableObject
    {
        /// <summary>
        /// 支付记录
        /// </summary>
        public PaymentRecord PaymentRecord { get; set; } = new();

        /// <summary>
        /// 是否已确认
        /// </summary>
        [ObservableProperty]
        private bool isConfirmed;

        /// <summary>
        /// 雇主名称
        /// </summary>
        public string EmployerName { get; set; } = string.Empty;

        /// <summary>
        /// 工作班次的最晚结束时间（用于检测工作是否结束）
        /// </summary>
        public DateTime? LatestShiftEndTime { get; set; }

        /// <summary>
        /// 是否可以确认（工作时间是否已结束）
        /// </summary>
        public bool CanConfirm { get; set; } = true;

        /// <summary>
        /// 是否禁用（工作时间未结束）
        /// </summary>
        public bool IsDisabled => !CanConfirm;

        /// <summary>
        /// 显示文本
        /// </summary>
        public string DisplayText
        {
            get
            {
                try
                {
                    return $"${PaymentRecord?.TotalAmount:F2} - {EmployerName ?? "Unknown"}";
                }
                catch
                {
                    return "Payment Record";
                }
            }
        }

        /// <summary>
        /// 时间段文本
        /// </summary>
        public string PeriodText
        {
            get
            {
                try
                {
                    return $"{PaymentRecord?.PeriodStart:MMM dd} - {PaymentRecord?.PeriodEnd:MMM dd}";
                }
                catch
                {
                    return "Period";
                }
            }
        }

        /// <summary>
        /// 工作时长文本
        /// </summary>
        public string HoursText
        {
            get
            {
                try
                {
                    return $"{PaymentRecord?.TotalHours:F1} hours";
                }
                catch
                {
                    return "0.0 hours";
                }
            }
        }

        /// <summary>
        /// 项目样式（已确认的项目显示为划线效果）
        /// </summary>
        public TextDecorations TextDecoration => IsConfirmed ? TextDecorations.Strikethrough : TextDecorations.None;

        /// <summary>
        /// 文本颜色（已确认的项目显示为灰色，禁用的项目显示为浅灰色）
        /// </summary>
        public Color TextColor
        {
            get
            {
                try
                {
                    if (IsConfirmed) return Colors.Gray;
                    if (IsDisabled) return Color.FromArgb("#BDBDBD");
                    return Colors.Black;
                }
                catch
                {
                    return Colors.Black;
                }
            }
        }

        /// <summary>
        /// 背景颜色
        /// </summary>
        public Color BackgroundColor
        {
            get
            {
                try
                {
                    if (IsConfirmed) return Color.FromArgb("#F5F5F5");
                    if (IsDisabled) return Color.FromArgb("#FAFAFA");
                    return Colors.White;
                }
                catch
                {
                    return Colors.White;
                }
            }
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get
            {
                try
                {
                    if (IsConfirmed) return "✓ Paid";
                    if (IsDisabled) return "⏰ Working";
                    return "⏳ Pending";
                }
                catch
                {
                    return "⏳ Pending";
                }
            }
        }

        /// <summary>
        /// 不可确认时的提示文本
        /// </summary>
        public string DisabledTooltip => IsDisabled ? "The working hours have not yet ended, so the salary cannot be confirmed." : string.Empty;
    }
}
