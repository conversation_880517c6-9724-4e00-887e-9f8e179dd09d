// 引入视图模型命名空间 / Import view model namespace
using Scheduler.ViewModels;

namespace Scheduler.Views;

/// <summary>
/// 待办事项列表视图 / Todo List View
/// 显示和管理用户的待办事项，包括统计信息和操作功能
/// Display and manage user's todo items, including statistics and operation functions
/// </summary>
public partial class TodoListView : ContentPage
{
    // 待办事项列表视图模型实例 / Todo list view model instance
    private readonly TodoListViewModel _viewModel;

    /// <summary>
    /// 构造函数 / Constructor
    /// 初始化待办事项列表视图并配置数据绑定 / Initialize todo list view and configure data binding
    /// </summary>
    /// <param name="viewModel">待办事项列表视图模型 / Todo list view model</param>
    public TodoListView(TodoListViewModel viewModel)
    {
        // 初始化XAML组件 / Initialize XAML components
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    /// <summary>
    /// 页面出现时的处理 / Handle page appearing
    /// 加载待办事项数据和统计信息 / Load todo items data and statistics
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();

        try
        {
            System.Diagnostics.Debug.WriteLine("TodoListView: OnAppearing 开始 / Starting");

            // 页面出现时加载数据 / Load data when page appears
            await _viewModel.LoadDataCommand.ExecuteAsync(null);

            System.Diagnostics.Debug.WriteLine("TodoListView: OnAppearing 完成 / Completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"TodoListView: OnAppearing 异常 / Exception - {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"TodoListView: 异常堆栈 / Exception stack - {ex.StackTrace}");

            // 显示错误信息给用户 / Show error message to user
            await DisplayAlert("错误 / Error", $"加载待办事项失败 / Failed to load todo items: {ex.Message}", "确定 / OK");
        }
    }
}
