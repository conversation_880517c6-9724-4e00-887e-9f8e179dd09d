<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Settings Page -->
  <data name="Settings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="WorkSettings" xml:space="preserve">
    <value>工作设置</value>
  </data>
  <data name="LogConfirmation" xml:space="preserve">
    <value>工作记录确认</value>
  </data>
  <data name="LogConfirmationDesc" xml:space="preserve">
    <value>启用工作记录确认功能</value>
  </data>
  <data name="PayConfirmation" xml:space="preserve">
    <value>工资确认</value>
  </data>
  <data name="PayConfirmationDesc" xml:space="preserve">
    <value>启用工资确认功能</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>通知提醒</value>
  </data>
  <data name="WorkReminders" xml:space="preserve">
    <value>工作提醒</value>
  </data>
  <data name="WorkRemindersDesc" xml:space="preserve">
    <value>在班次开始前接收通知</value>
  </data>
  <data name="BreakReminders" xml:space="preserve">
    <value>休息提醒</value>
  </data>
  <data name="BreakRemindersDesc" xml:space="preserve">
    <value>在工作期间提醒休息</value>
  </data>
  <data name="PaymentNotifications" xml:space="preserve">
    <value>工资通知</value>
  </data>
  <data name="PaymentNotificationsDesc" xml:space="preserve">
    <value>接收工资确认相关通知</value>
  </data>
  <data name="DisplayTime" xml:space="preserve">
    <value>显示与时间</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>时区</value>
  </data>
  <data name="TimeFormat" xml:space="preserve">
    <value>时间格式</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="SystemIntegration" xml:space="preserve">
    <value>系统集成</value>
  </data>
  <data name="AlarmSettings" xml:space="preserve">
    <value>闹钟设置</value>
  </data>
  <data name="AlarmSettingsDesc" xml:space="preserve">
    <value>链接到手机闹钟应用</value>
  </data>
  <data name="ExportToCalendar" xml:space="preserve">
    <value>导出到日历</value>
  </data>
  <data name="ExportToCalendarDesc" xml:space="preserve">
    <value>与系统日历同步</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>数据管理</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>导出数据</value>
  </data>
  <data name="ExportDataDesc" xml:space="preserve">
    <value>将工作数据导出到文件</value>
  </data>
  <data name="BackupSync" xml:space="preserve">
    <value>备份与同步</value>
  </data>
  <data name="BackupSyncDesc" xml:space="preserve">
    <value>云备份设置</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>关于应用</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>版本 {0}</value>
  </data>
  
  <!-- Messages -->
  <data name="SettingsUpdated" xml:space="preserve">
    <value>设置已更新</value>
  </data>
  <data name="NotificationSettings" xml:space="preserve">
    <value>通知设置</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>已启用</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>已禁用</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="SelectTimeZone" xml:space="preserve">
    <value>选择时区</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>选择语言</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="Hour24" xml:space="preserve">
    <value>24小时制</value>
  </data>
  <data name="Hour12" xml:space="preserve">
    <value>12小时制</value>
  </data>

  <!-- Employer Management -->
  <data name="EmployerManagement" xml:space="preserve">
    <value>雇主管理</value>
  </data>
  <data name="AddEmployer" xml:space="preserve">
    <value>新增雇主</value>
  </data>
  <data name="EditEmployer" xml:space="preserve">
    <value>编辑雇主</value>
  </data>
  <data name="ViewAllEmployers" xml:space="preserve">
    <value>查看所有雇主</value>
  </data>
  <data name="EmployerName" xml:space="preserve">
    <value>雇主姓名</value>
  </data>
  <data name="EmployerNameRequired" xml:space="preserve">
    <value>雇主姓名 *</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>联系方式</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>工作地点</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>时薪 ($/小时)</value>
  </data>
  <data name="HourlyRateRequired" xml:space="preserve">
    <value>时薪 ($/小时) *</value>
  </data>
  <data name="PaymentCycle" xml:space="preserve">
    <value>支付周期 (天)</value>
  </data>
  <data name="IdentificationColor" xml:space="preserve">
    <value>标识颜色</value>
  </data>
  <data name="EmploymentStatus" xml:space="preserve">
    <value>在职状态</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>备注信息</value>
  </data>
  <data name="BasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="SalaryInfo" xml:space="preserve">
    <value>薪资信息</value>
  </data>
  <data name="DisplaySettings" xml:space="preserve">
    <value>显示设置</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="ReturnHome" xml:space="preserve">
    <value>返回Home</value>
  </data>
  <data name="NoEmployersFound" xml:space="preserve">
    <value>暂无雇主信息</value>
  </data>
  <data name="AddFirstEmployer" xml:space="preserve">
    <value>点击"新增雇主"按钮添加第一个雇主</value>
  </data>
  <data name="SearchEmployers" xml:space="preserve">
    <value>搜索雇主姓名、联系方式或工作地点...</value>
  </data>
  <data name="FillEmployerInfo" xml:space="preserve">
    <value>填写雇主基本信息</value>
  </data>
  <data name="EnterEmployerName" xml:space="preserve">
    <value>请输入雇主姓名</value>
  </data>
  <data name="EnterContactInfo" xml:space="preserve">
    <value>电话、邮箱或其他联系方式</value>
  </data>
  <data name="EnterWorkLocation" xml:space="preserve">
    <value>请输入工作地点</value>
  </data>
  <data name="EnterNotes" xml:space="preserve">
    <value>请输入备注信息...</value>
  </data>

  <!-- Validation Messages -->
  <data name="ValidationFailed" xml:space="preserve">
    <value>验证失败</value>
  </data>
  <data name="PleaseEnterEmployerName" xml:space="preserve">
    <value>请输入雇主姓名</value>
  </data>
  <data name="PleaseEnterValidHourlyRate" xml:space="preserve">
    <value>请输入有效的时薪</value>
  </data>
  <data name="PleaseEnterValidPaymentCycle" xml:space="preserve">
    <value>请输入有效的支付周期天数</value>
  </data>

  <!-- Success Messages -->
  <data name="SaveSuccess" xml:space="preserve">
    <value>保存成功</value>
  </data>
  <data name="EmployerInfoUpdated" xml:space="preserve">
    <value>雇主信息已更新</value>
  </data>
  <data name="NewEmployerAdded" xml:space="preserve">
    <value>新雇主已添加</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>删除成功</value>
  </data>
  <data name="EmployerDeleted" xml:space="preserve">
    <value>雇主 "{0}" 已删除</value>
  </data>

  <!-- Error Messages -->
  <data name="SaveFailed" xml:space="preserve">
    <value>保存失败</value>
  </data>
  <data name="LoadFailed" xml:space="preserve">
    <value>加载失败</value>
  </data>
  <data name="DeleteFailed" xml:space="preserve">
    <value>删除失败</value>
  </data>
  <data name="InitializationFailed" xml:space="preserve">
    <value>初始化失败</value>
  </data>

  <!-- Confirmation Dialogs -->
  <data name="ConfirmDelete" xml:space="preserve">
    <value>确认删除</value>
  </data>
  <data name="ConfirmDeleteEmployer" xml:space="preserve">
    <value>确定要删除雇主 "{0}" 吗？

注意：这将会软删除该雇主，相关的班次和工作记录将保留。</value>
  </data>

  <!-- Pay Page Employer Directory -->
  <data name="EmployerDirectory" xml:space="preserve">
    <value>雇主目录</value>
  </data>
  <data name="NoEmployersFoundInPay" xml:space="preserve">
    <value>未找到雇主</value>
  </data>
  <data name="UnknownEmployer" xml:space="preserve">
    <value>未知雇主</value>
  </data>

  <!-- Pay Page Additional Texts -->
  <data name="FiveYearIncomeHistory" xml:space="preserve">
    <value>5年收入历史</value>
  </data>
  <data name="PaymentTodoList" xml:space="preserve">
    <value>工资确认清单</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>待确认</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="TotalIncome" xml:space="preserve">
    <value>总收入</value>
  </data>
  <data name="NoPaymentRecords" xml:space="preserve">
    <value>暂无支付记录</value>
  </data>
  <data name="Toggle" xml:space="preserve">
    <value>切换</value>
  </data>

  <!-- Login Page -->
  <data name="Username" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>记住我</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>忘记密码？</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="WorkManagementSystem" xml:space="preserve">
    <value>工作管理系统</value>
  </data>

  <!-- TodoList Page -->
  <data name="SyncStatus" xml:space="preserve">
    <value>同步状态</value>
  </data>
  <data name="ToggleView" xml:space="preserve">
    <value>切换显示</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="NoPendingTodoItems" xml:space="preserve">
    <value>暂无待办事项</value>
  </data>
  <data name="RecentlyCompletedTasks" xml:space="preserve">
    <value>最近完成的任务</value>
  </data>

  <!-- AllEmployer Page -->
  <data name="EmployerManagement" xml:space="preserve">
    <value>雇主管理</value>
  </data>
  <data name="AddEmployer" xml:space="preserve">
    <value>新增雇主</value>
  </data>
  <data name="NoEmployerInfo" xml:space="preserve">
    <value>暂无雇主信息</value>
  </data>
  <data name="ClickAddEmployerToStart" xml:space="preserve">
    <value>点击'新增雇主'按钮添加第一个雇主</value>
  </data>
  <data name="EditSelected" xml:space="preserve">
    <value>编辑选中</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>删除选中</value>
  </data>
  <data name="BackToHome" xml:space="preserve">
    <value>返回Home</value>
  </data>
  <data name="EditSelected" xml:space="preserve">
    <value>编辑选中</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>删除选中</value>
  </data>
  <data name="RecentlyConfirmed" xml:space="preserve">
    <value>最近已确认 (最新5条)</value>
  </data>
  <data name="HourlyRateFormat" xml:space="preserve">
    <value>${0:F2}/小时</value>
  </data>

  <!-- PayView Page -->
  <data name="ManageEmployers" xml:space="preserve">
    <value>管理雇主</value>
  </data>
  <data name="PendingPayments" xml:space="preserve">
    <value>待确认支付</value>
  </data>
  <data name="RecentlyConfirmed" xml:space="preserve">
    <value>最近已确认 (最新5条)</value>
  </data>

  <!-- Common Actions -->
  <data name="Add" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>否</value>
  </data>

  <!-- CalendarView -->
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>

  <!-- SetView -->
  <data name="WorkSettings" xml:space="preserve">
    <value>工作设置</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>通知提醒设置</value>
  </data>
  <data name="ShiftReminderTime" xml:space="preserve">
    <value>班次提醒时间</value>
  </data>
  <data name="ShiftReminderDescription" xml:space="preserve">
    <value>班次开始前多少分钟提醒</value>
  </data>
  <data name="MinutesFormat" xml:space="preserve">
    <value>{0} 分钟</value>
  </data>
  <data name="DailySchedule" xml:space="preserve">
    <value>每日工作安排</value>
  </data>
  <data name="DailyScheduleDescription" xml:space="preserve">
    <value>每天早晨发送当天的工作安排</value>
  </data>
  <data name="DailyNotificationTime" xml:space="preserve">
    <value>每日通知时间</value>
  </data>
  <data name="DailyNotificationDescription" xml:space="preserve">
    <value>每天发送工作安排的时间</value>
  </data>
  <data name="BreakReminderInterval" xml:space="preserve">
    <value>休息提醒间隔</value>
  </data>
  <data name="BreakReminderDescription" xml:space="preserve">
    <value>每隔多长时间提醒休息</value>
  </data>
  <data name="HoursFormat" xml:space="preserve">
    <value>{0} 小时</value>
  </data>
  <data name="NotificationBehavior" xml:space="preserve">
    <value>通知行为</value>
  </data>
  <data name="NotificationSound" xml:space="preserve">
    <value>通知声音</value>
  </data>
  <data name="NotificationSoundDescription" xml:space="preserve">
    <value>播放通知提示音</value>
  </data>
  <data name="VibrationReminder" xml:space="preserve">
    <value>振动提醒</value>
  </data>
  <data name="VibrationDescription" xml:space="preserve">
    <value>通知时振动设备</value>
  </data>
  <data name="QuietHours" xml:space="preserve">
    <value>免打扰时间</value>
  </data>
  <data name="QuietHoursDescription" xml:space="preserve">
    <value>在指定时间段内静音通知</value>
  </data>
  <data name="QuietHoursPeriod" xml:space="preserve">
    <value>免打扰时间段</value>
  </data>
  <data name="QuietHoursPeriodDescription" xml:space="preserve">
    <value>设置静音时间范围</value>
  </data>
  <data name="PermissionManagement" xml:space="preserve">
    <value>权限管理</value>
  </data>
  <data name="NotificationPermission" xml:space="preserve">
    <value>通知权限</value>
  </data>
  <data name="NotificationPermissionDescription" xml:space="preserve">
    <value>允许应用发送通知提醒</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>检查</value>
  </data>
  <data name="LocationPermission" xml:space="preserve">
    <value>位置权限</value>
  </data>
  <data name="LocationPermissionDescription" xml:space="preserve">
    <value>用于基于位置的工作提醒</value>
  </data>
  <data name="OpenAppSettings" xml:space="preserve">
    <value>打开应用设置</value>
  </data>
  <data name="TestNotification" xml:space="preserve">
    <value>测试通知功能</value>
  </data>
  <data name="DisplayTime" xml:space="preserve">
    <value>显示与时间设置</value>
  </data>
  <data name="SystemIntegration" xml:space="preserve">
    <value>系统集成</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>数据管理</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>关于应用</value>
  </data>

  <!-- ShiftManagementView -->
  <data name="SmartTimeSelection" xml:space="preserve">
    <value>智能时间选择</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="WorkHours" xml:space="preserve">
    <value>工作时长</value>
  </data>
  <data name="EndTimeAutoCalculated" xml:space="preserve">
    <value>自动计算的结束时间</value>
  </data>
  <data name="SmartWorkPreview" xml:space="preserve">
    <value>智能工作预览</value>
  </data>
  <data name="JobPreview" xml:space="preserve">
    <value>工作预览</value>
  </data>
  <data name="WorkHoursLabel" xml:space="preserve">
    <value>工作时长</value>
  </data>
  <data name="HourlyRateLabel" xml:space="preserve">
    <value>时薪</value>
  </data>
  <data name="EstimatedRevenue" xml:space="preserve">
    <value>预估收入</value>
  </data>
  <data name="TimeRangeDisplay" xml:space="preserve">
    <value>时间范围显示</value>
  </data>
  <data name="RecurringShiftSettings" xml:space="preserve">
    <value>循环排班设置</value>
  </data>
  <data name="RecurringShiftTitleAndSwitch" xml:space="preserve">
    <value>循环排班标题和开关</value>
  </data>
  <data name="RecurringSettingsDetails" xml:space="preserve">
    <value>循环设置详细选项</value>
  </data>
  <data name="CyclePeriod" xml:space="preserve">
    <value>循环周期</value>
  </data>
  <data name="RecurringEndSettings" xml:space="preserve">
    <value>循环结束设置</value>
  </data>
  <data name="RecurringPreviewInfo" xml:space="preserve">
    <value>循环预览信息</value>
  </data>
  <data name="NotificationSettings" xml:space="preserve">
    <value>通知设置</value>
  </data>
  <data name="RecurringShiftNotificationRestriction" xml:space="preserve">
    <value>⚠️ 重复排班通知限制</value>
  </data>
  <data name="RecurringShiftNotificationMessage" xml:space="preserve">
    <value>重复排班功能不支持通知设置。建议为单个班次单独设置通知。</value>
  </data>
  <data name="NotificationSettingsTitleAndSwitch" xml:space="preserve">
    <value>通知设置标题和开关</value>
  </data>
  <data name="NotificationSettingsDetails" xml:space="preserve">
    <value>通知设置详细选项</value>
  </data>
  <data name="ReminderTimeSelection" xml:space="preserve">
    <value>提醒时间选择</value>
  </data>
  <data name="ReminderTime" xml:space="preserve">
    <value>提醒时间</value>
  </data>
  <data name="NotificationPreviewInfo" xml:space="preserve">
    <value>通知预览信息</value>
  </data>

  <!-- NotificationHistoryView -->
  <data name="NotificationHistory" xml:space="preserve">
    <value>通知历史</value>
  </data>
  <data name="ShiftNotificationManagement" xml:space="preserve">
    <value>🔔 班次通知管理</value>
  </data>
  <data name="TestNotification" xml:space="preserve">
    <value>📱 测试通知</value>
  </data>
  <data name="NoShiftsWithNotifications" xml:space="preserve">
    <value>📅 暂无已启用通知的班次</value>
  </data>
  <data name="ReminderMinutesFormat" xml:space="preserve">
    <value>🔔 提前 {0} 分钟提醒</value>
  </data>
  <data name="CancelNotification" xml:space="preserve">
    <value>❌ 取消通知</value>
  </data>
  <data name="RefreshData" xml:space="preserve">
    <value>🔄 刷新数据</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>搜索通知标题或内容...</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="NotificationType" xml:space="preserve">
    <value>通知类型</value>
  </data>
  <data name="NotificationStatus" xml:space="preserve">
    <value>通知状态</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>开始日期</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>结束日期</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>清除筛选</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>清除全部</value>
  </data>
  <data name="NoNotificationRecords" xml:space="preserve">
    <value>暂无通知记录</value>
  </data>
  <data name="TryAdjustingFilters" xml:space="preserve">
    <value>尝试调整筛选条件或创建新的通知</value>
  </data>
  <data name="ScheduledTime" xml:space="preserve">
    <value>安排时间: </value>
  </data>
  <data name="ReadTime" xml:space="preserve">
    <value>已读时间: </value>
  </data>
  <data name="MarkAsRead" xml:space="preserve">
    <value>标记已读</value>
  </data>
  <data name="Resend" xml:space="preserve">
    <value>重新发送</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>

  <!-- NotificationSettingsView -->
  <data name="NotificationSettings" xml:space="preserve">
    <value>通知设置</value>
  </data>
  <data name="MainNotificationToggle" xml:space="preserve">
    <value>通知总开关</value>
  </data>
  <data name="EnableNotifications" xml:space="preserve">
    <value>启用通知</value>
  </data>
  <data name="EnableSound" xml:space="preserve">
    <value>启用声音</value>
  </data>
  <data name="EnableVibration" xml:space="preserve">
    <value>启用振动</value>
  </data>
  <data name="ShiftReminderSettings" xml:space="preserve">
    <value>班次提醒设置</value>
  </data>
  <data name="EnableShiftReminders" xml:space="preserve">
    <value>启用班次提醒</value>
  </data>
  <data name="AdvanceReminderTime" xml:space="preserve">
    <value>提前提醒时间</value>
  </data>
  <data name="EnableDailyScheduleNotifications" xml:space="preserve">
    <value>启用每日工作安排通知</value>
  </data>
  <data name="BreakReminderSettings" xml:space="preserve">
    <value>休息提醒设置</value>
  </data>
  <data name="EnableBreakReminders" xml:space="preserve">
    <value>启用休息提醒</value>
  </data>
  <data name="ReminderInterval" xml:space="preserve">
    <value>提醒间隔</value>
  </data>
  <data name="OnlyDuringWorkHours" xml:space="preserve">
    <value>仅在工作时间提醒</value>
  </data>
  <data name="PaymentReminderSettings" xml:space="preserve">
    <value>薪资提醒设置</value>
  </data>
  <data name="EnablePaymentReminders" xml:space="preserve">
    <value>启用薪资提醒</value>
  </data>
  <data name="AdvanceReminderDays" xml:space="preserve">
    <value>提前提醒天数</value>
  </data>
  <data name="EnablePaymentConfirmationReminders" xml:space="preserve">
    <value>启用薪资确认提醒</value>
  </data>
  <data name="QuietHoursSettings" xml:space="preserve">
    <value>免打扰时间设置</value>
  </data>
  <data name="EnableQuietHours" xml:space="preserve">
    <value>启用免打扰时间</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>开始时间</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>结束时间</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>小时</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>分钟</value>
  </data>
  <data name="SaveSettings" xml:space="preserve">
    <value>保存设置</value>
  </data>
  <data name="RequestNotificationPermission" xml:space="preserve">
    <value>请求通知权限</value>
  </data>
  <data name="ResetToDefaultSettings" xml:space="preserve">
    <value>重置为默认设置</value>
  </data>

  <!-- NotificationTestView -->
  <data name="NotificationFunctionTest" xml:space="preserve">
    <value>通知功能测试</value>
  </data>
  <data name="NotificationPermissionManagement" xml:space="preserve">
    <value>通知权限管理</value>
  </data>
  <data name="CheckPermission" xml:space="preserve">
    <value>检查权限</value>
  </data>
  <data name="RequestPermission" xml:space="preserve">
    <value>请求权限</value>
  </data>
  <data name="CustomNotificationTest" xml:space="preserve">
    <value>自定义通知测试</value>
  </data>
  <data name="NotificationTitle" xml:space="preserve">
    <value>通知标题</value>
  </data>
  <data name="EnterNotificationTitle" xml:space="preserve">
    <value>输入通知标题</value>
  </data>
  <data name="NotificationContent" xml:space="preserve">
    <value>通知内容</value>
  </data>
  <data name="EnterNotificationContent" xml:space="preserve">
    <value>输入通知内容</value>
  </data>
  <data name="DelayTimeFormat" xml:space="preserve">
    <value>延迟时间: {0} 秒</value>
  </data>
  <data name="InstantNotification" xml:space="preserve">
    <value>即时通知</value>
  </data>
  <data name="DelayedNotification" xml:space="preserve">
    <value>延迟通知</value>
  </data>
  <data name="FeatureNotificationTest" xml:space="preserve">
    <value>功能通知测试</value>
  </data>
  <data name="TestShiftReminder" xml:space="preserve">
    <value>测试班次提醒</value>
  </data>
  <data name="TestTodoReminder" xml:space="preserve">
    <value>测试待办事项提醒</value>
  </data>
  <data name="TestPaymentReminder" xml:space="preserve">
    <value>测试支付提醒</value>
  </data>
  <data name="TestBreakReminder" xml:space="preserve">
    <value>测试休息提醒</value>
  </data>
  <data name="SystemManagement" xml:space="preserve">
    <value>系统管理</value>
  </data>
  <data name="RescheduleAllNotifications" xml:space="preserve">
    <value>重新安排所有通知</value>
  </data>
  <data name="TestInstructions" xml:space="preserve">
    <value>测试说明</value>
  </data>
  <data name="InstantNotificationDesc" xml:space="preserve">
    <value>• 即时通知：立即发送测试通知</value>
  </data>
  <data name="DelayedNotificationDesc" xml:space="preserve">
    <value>• 延迟通知：按设定时间延迟发送</value>
  </data>
  <data name="ShiftReminderDesc" xml:space="preserve">
    <value>• 班次提醒：模拟1分钟后开始的班次</value>
  </data>
  <data name="TodoReminderDesc" xml:space="preserve">
    <value>• 待办事项提醒：模拟2分钟后到期的任务</value>
  </data>
  <data name="PaymentReminderDesc" xml:space="preserve">
    <value>• 支付提醒：模拟待支付的工资记录</value>
  </data>
  <data name="BreakReminderDesc" xml:space="preserve">
    <value>• 休息提醒：模拟工作休息提醒</value>
  </data>
  <data name="RescheduleDesc" xml:space="preserve">
    <value>• 重新安排：重新安排所有待发送的通知</value>
  </data>
  <!-- AllPaymentRecordsView -->
  <data name="AllPaymentRecords" xml:space="preserve">
    <value>所有支付记录</value>
  </data>
  <data name="TotalRecords" xml:space="preserve">
    <value>总记录数</value>
  </data>
  <data name="PendingConfirmation" xml:space="preserve">
    <value>待确认</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>已确认</value>
  </data>
  <data name="SelectAllToggle" xml:space="preserve">
    <value>全选/取消全选</value>
  </data>
  <data name="SelectedFormat" xml:space="preserve">
    <value>已选择: {0}</value>
  </data>
  <data name="NoPaymentRecords" xml:space="preserve">
    <value>暂无支付记录</value>
  </data>
  <data name="WorkPeriodFormat" xml:space="preserve">
    <value>工作期间: {0:yyyy-MM-dd}</value>
  </data>
  <data name="ToFormat" xml:space="preserve">
    <value>至: {0:yyyy-MM-dd}</value>
  </data>
  <data name="HoursFormat" xml:space="preserve">
    <value>{0:F1}小时</value>
  </data>
  <data name="CancelConfirmation" xml:space="preserve">
    <value>取消确认</value>
  </data>
  <!-- PaymentTodoView -->
  <data name="PaymentTodoStatistics" xml:space="preserve">
    <value>支付待办事项统计</value>
  </data>
  <data name="PendingConfirmationCount" xml:space="preserve">
    <value>待确认</value>
  </data>
  <data name="OverdueCount" xml:space="preserve">
    <value>逾期</value>
  </data>
  <data name="PendingAmount" xml:space="preserve">
    <value>待确认金额</value>
  </data>
  <data name="ConfirmedAmount" xml:space="preserve">
    <value>已确认金额</value>
  </data>
  <data name="HideCompleted" xml:space="preserve">
    <value>隐藏已完成</value>
  </data>
  <data name="ShowCompleted" xml:space="preserve">
    <value>显示已完成</value>
  </data>
  <data name="OverduePayments" xml:space="preserve">
    <value>逾期支付 ⚠️</value>
  </data>
  <data name="NoOverduePayments" xml:space="preserve">
    <value>暂无逾期支付</value>
  </data>
  <data name="UrgentMark" xml:space="preserve">
    <value>⚠️</value>
  </data>
  <data name="PaymentInfo" xml:space="preserve">
    <value>支付信息</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>详情</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="PendingPayments" xml:space="preserve">
    <value>待确认支付</value>
  </data>
  <data name="NoPendingPayments" xml:space="preserve">
    <value>暂无待确认支付</value>
  </data>
  <data name="PaymentIcon" xml:space="preserve">
    <value>💰</value>
  </data>
  <data name="ConfirmedPayments" xml:space="preserve">
    <value>已确认支付</value>
  </data>
  <data name="NoConfirmedPayments" xml:space="preserve">
    <value>暂无已确认支付</value>
  </data>
  <data name="CompletedMark" xml:space="preserve">
    <value>✅</value>
  </data>
  <data name="CompletedTimeFormat" xml:space="preserve">
    <value>完成时间: {0:MM/dd HH:mm}</value>
  </data>
  <data name="LoadingIndicator" xml:space="preserve">
    <value>加载指示器</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <!-- EditEmployerView -->
  <data name="EditEmployer" xml:space="preserve">
    <value>编辑雇主</value>
  </data>
  <data name="EditEmployerDescription" xml:space="preserve">
    <value>修改雇主信息并保存更改</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="EmployerName" xml:space="preserve">
    <value>雇主姓名</value>
  </data>
  <data name="EmployerNamePlaceholder" xml:space="preserve">
    <value>请输入雇主姓名</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>联系方式</value>
  </data>
  <data name="ContactInfoPlaceholder" xml:space="preserve">
    <value>请输入联系方式</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>工作地点</value>
  </data>
  <data name="WorkLocationPlaceholder" xml:space="preserve">
    <value>请输入工作地点</value>
  </data>
  <data name="SalaryInformation" xml:space="preserve">
    <value>薪资信息</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>时薪</value>
  </data>
  <data name="PaymentCycleDays" xml:space="preserve">
    <value>支付周期（天）</value>
  </data>
  <data name="DisplaySettings" xml:space="preserve">
    <value>显示设置</value>
  </data>
  <data name="IdentificationColor" xml:space="preserve">
    <value>标识颜色</value>
  </data>
  <data name="ColorSelectionGrid" xml:space="preserve">
    <value>颜色选择网格</value>
  </data>
  <data name="ColorCircleButton" xml:space="preserve">
    <value>颜色圆形按钮</value>
  </data>
  <data name="SelectedMark" xml:space="preserve">
    <value>选中标记</value>
  </data>
  <data name="ColorName" xml:space="preserve">
    <value>颜色名称</value>
  </data>
  <data name="CurrentColorDisplay" xml:space="preserve">
    <value>当前选中颜色显示</value>
  </data>
  <data name="CurrentColor" xml:space="preserve">
    <value>当前颜色：</value>
  </data>
  <data name="EmploymentStatus" xml:space="preserve">
    <value>在职状态</value>
  </data>
  <data name="NotesInformation" xml:space="preserve">
    <value>备注信息</value>
  </data>
  <data name="NotesPlaceholder" xml:space="preserve">
    <value>请输入备注信息...</value>
  </data>
  <data name="ActionButtons" xml:space="preserve">
    <value>操作按钮区域</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>保存修改</value>
  </data>
  <data name="CancelEdit" xml:space="preserve">
    <value>取消编辑</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>筛选</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="NoShiftRecords" xml:space="preserve">
    <value>暂无班次记录</value>
  </data>
  <data name="ClickAddToCreateFirstShift" xml:space="preserve">
    <value>点击右上角添加按钮创建第一个班次</value>
  </data>
  <data name="AddNow" xml:space="preserve">
    <value>立即添加</value>
  </data>
  <data name="MinimalShiftManagementPage" xml:space="preserve">
    <value>最小化班次管理页面</value>
  </data>
  <data name="SelectEmployer" xml:space="preserve">
    <value>选择雇主:</value>
  </data>
  <data name="HourlyRateLabel" xml:space="preserve">
    <value>时薪:</value>
  </data>
  <data name="Return" xml:space="preserve">
    <value>返回</value>
  </data>
  <data name="ShiftManagementSimplified" xml:space="preserve">
    <value>班次管理 (简化版)</value>
  </data>
  <data name="PageLoadedSuccessfully" xml:space="preserve">
    <value>页面加载成功！</value>
  </data>
  <data name="SaveShift" xml:space="preserve">
    <value>保存班次</value>
  </data>
  <data name="NavigationWorksMessage" xml:space="preserve">
    <value>如果您能看到这个页面，说明导航工作正常！</value>
  </data>
  <data name="TryNavigateToFullPage" xml:space="preserve">
    <value>现在可以尝试导航到完整的 SimpleShiftView 页面。</value>
  </data>
  <data name="OpenFullSchedulePage" xml:space="preserve">
    <value>打开完整的 Schedule 页面</value>
  </data>
  <data name="ReturnToHome" xml:space="preserve">
    <value>返回主页</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>快速操作</value>
  </data>
  <data name="ClockIn" xml:space="preserve">
    <value>上班打卡</value>
  </data>
  <data name="ClockOut" xml:space="preserve">
    <value>下班打卡</value>
  </data>
  <data name="AddNote" xml:space="preserve">
    <value>添加笔记</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>详情</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="LoadingIndicator" xml:space="preserve">
    <value>加载指示器</value>
  </data>
  <data name="TestShiftManagementPage" xml:space="preserve">
    <value>✅ 测试班次管理页面</value>
  </data>
  <data name="AutoCloseTestTitle" xml:space="preserve">
    <value>自动关闭测试</value>
  </data>
  <data name="AutoCloseTestHeader" xml:space="preserve">
    <value>自动关闭提示测试</value>
  </data>
  <data name="AutoCloseTestInstructions" xml:space="preserve">
    <value>点击下面的按钮测试不同类型的自动关闭提示：</value>
  </data>
  <data name="TestSuccessToast" xml:space="preserve">
    <value>测试成功提示 (Toast 5秒)</value>
  </data>
  <data name="TestErrorSnackbar" xml:space="preserve">
    <value>测试错误提示 (Snackbar 5秒)</value>
  </data>
  <data name="TestInfoToast" xml:space="preserve">
    <value>测试信息提示 (Toast 3秒)</value>
  </data>
  <data name="TestWarningSnackbar" xml:space="preserve">
    <value>测试警告提示 (Snackbar 7秒)</value>
  </data>
  <data name="AutoCloseNote" xml:space="preserve">
    <value>注意：所有提示都会自动关闭，但您也可以手动点击关闭。</value>
  </data>
  <data name="EmployerCountFormat" xml:space="preserve">
    <value>雇主数量: {0}</value>
  </data>
  <data name="RecordCountFormat" xml:space="preserve">
    <value>共 {0} 条</value>
  </data>
  <data name="WorkHoursFormat" xml:space="preserve">
    <value>工作时长: {0:F1} 小时</value>
  </data>
  <data name="DatabaseTestTitle" xml:space="preserve">
    <value>数据库连接测试</value>
  </data>
  <data name="DatabaseTestHeader" xml:space="preserve">
    <value>🔍 数据库连接诊断工具</value>
  </data>
  <data name="DatabaseTestDescription" xml:space="preserve">
    <value>验证SQLite数据库连接状态和重置功能</value>
  </data>
  <data name="TestOptions" xml:space="preserve">
    <value>测试选项</value>
  </data>
  <data name="TestRunning" xml:space="preserve">
    <value>测试运行中...</value>
  </data>
  <data name="RunConnectionTest" xml:space="preserve">
    <value>运行连接测试</value>
  </data>
  <data name="TestResetFunction" xml:space="preserve">
    <value>测试重置功能</value>
  </data>
  <data name="ReenableDemoData" xml:space="preserve">
    <value>重新启用演示数据</value>
  </data>
  <data name="ClearResults" xml:space="preserve">
    <value>清空结果</value>
  </data>
  <data name="AllTestsPassed" xml:space="preserve">
    <value>所有测试通过</value>
  </data>
  <data name="TestsFoundIssues" xml:space="preserve">
    <value>测试发现问题</value>
  </data>
  <data name="RunningTests" xml:space="preserve">
    <value>正在运行测试，请稍候...</value>
  </data>
  <data name="TestResults" xml:space="preserve">
    <value>测试结果</value>
  </data>
  <data name="BackToSettings" xml:space="preserve">
    <value>返回设置</value>
  </data>
  <data name="KeyboardTestTitle" xml:space="preserve">
    <value>键盘测试页面</value>
  </data>
  <data name="KeyboardTestHeader" xml:space="preserve">
    <value>键盘弹出测试</value>
  </data>
  <data name="SimplifiedEntryTest" xml:space="preserve">
    <value>简化Entry测试</value>
  </data>
  <data name="BasicTextInput" xml:space="preserve">
    <value>基本文本输入</value>
  </data>
  <data name="NumericInput" xml:space="preserve">
    <value>数字输入</value>
  </data>
  <data name="EmailInput" xml:space="preserve">
    <value>邮箱输入</value>
  </data>
  <data name="MultilineTextInput" xml:space="preserve">
    <value>多行文本输入</value>
  </data>
  <data name="TestInstructions" xml:space="preserve">
    <value>测试说明</value>
  </data>
  <data name="TestInstruction1" xml:space="preserve">
    <value>• 点击每个输入框应该能正常弹出键盘</value>
  </data>
  <data name="TestInstruction2" xml:space="preserve">
    <value>• 键盘类型应该与输入内容匹配</value>
  </data>
  <data name="TestInstruction3" xml:space="preserve">
    <value>• 不应该有延迟或焦点冲突</value>
  </data>

  <!-- BaseViewModel Messages -->
  <data name="Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>操作失败</value>
  </data>

  <!-- SimpleShiftViewModel Messages -->
  <data name="FailedToLoadShiftData" xml:space="preserve">
    <value>加载班次数据失败</value>
  </data>
  <data name="FailedToLoadEmployers" xml:space="preserve">
    <value>加载雇主失败</value>
  </data>
  <data name="FailedToNavigateToEmployerPage" xml:space="preserve">
    <value>导航到雇主页面失败</value>
  </data>
  <data name="FailedToNavigateToShiftsPage" xml:space="preserve">
    <value>导航到班次页面失败</value>
  </data>
  <data name="PleaseSelectEmployer" xml:space="preserve">
    <value>请选择雇主</value>
  </data>
  <data name="SaveFailed" xml:space="preserve">
    <value>保存失败</value>
  </data>

  <!-- EditEmployerViewModel Messages -->
  <data name="SaveSuccessful" xml:space="preserve">
    <value>保存成功</value>
  </data>
  <data name="EmployerInfoUpdatedSuccessfully" xml:space="preserve">
    <value>雇主信息已成功更新</value>
  </data>

  <!-- Additional SimpleShiftViewModel Messages -->
  <data name="PleaseEnterValidHourlyRate" xml:space="preserve">
    <value>请输入有效的时薪</value>
  </data>
  <data name="PleaseSetValidWorkHours" xml:space="preserve">
    <value>请设置有效的工作时长</value>
  </data>
  <data name="SavingShift" xml:space="preserve">
    <value>正在保存班次...</value>
  </data>

  <!-- HomeViewModel Messages -->
  <data name="RefreshDataFailed" xml:space="preserve">
    <value>刷新数据失败</value>
  </data>

  <!-- ShiftListViewModel Messages -->
  <data name="ShiftDetails" xml:space="preserve">
    <value>班次详情</value>
  </data>
  <data name="Employer" xml:space="preserve">
    <value>雇主</value>
  </data>
  <data name="WorkHours" xml:space="preserve">
    <value>工作时长</value>
  </data>
  <data name="EstimatedEarnings" xml:space="preserve">
    <value>预期收入</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>备注</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>小时</value>
  </data>

  <!-- AutoCloseTestViewModel Messages -->
  <data name="ErrorTestMessage" xml:space="preserve">
    <value>这是一个错误提示，将在5秒后自动关闭！</value>
  </data>
  <data name="OperationFailedTitle" xml:space="preserve">
    <value>操作失败</value>
  </data>
  <data name="InfoTestMessage" xml:space="preserve">
    <value>这是一个信息提示，将在3秒后自动关闭！</value>
  </data>
  <data name="InfoTitle" xml:space="preserve">
    <value>提示信息</value>
  </data>
  <data name="WarningTestMessage" xml:space="preserve">
    <value>这是一个警告提示，将在7秒后自动关闭！</value>
  </data>
  <data name="WarningTitle" xml:space="preserve">
    <value>警告</value>
  </data>

  <!-- PayViewModel Messages -->
  <data name="LoadFailed" xml:space="preserve">
    <value>加载失败</value>
  </data>
  <data name="UnableToLoadEmployerInfo" xml:space="preserve">
    <value>无法加载雇主信息</value>
  </data>

  <!-- TodoListViewModel Messages -->
  <data name="FailedToUpdateTodoItemStatus" xml:space="preserve">
    <value>更新待办事项状态失败</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>确认删除</value>
  </data>
  <data name="AreYouSureDeleteTodoItem" xml:space="preserve">
    <value>确定要删除待办事项"{0}"吗？</value>
  </data>
  <data name="SyncedTodoItemStatus" xml:space="preserve">
    <value>已同步 {0} 个待办事项状态</value>
  </data>
  <data name="AllTodoItemStatusesUpToDate" xml:space="preserve">
    <value>所有待办事项状态都是最新的</value>
  </data>

  <!-- AllEmployerViewModel Messages -->
  <data name="SelectAll" xml:space="preserve">
    <value>全选</value>
  </data>
  <data name="UnselectAll" xml:space="preserve">
    <value>取消全选</value>
  </data>
  <data name="DeleteSelected" xml:space="preserve">
    <value>删除选中</value>
  </data>
  <data name="DeleteSelectedCount" xml:space="preserve">
    <value>删除选中({0})</value>
  </data>
  <data name="ConfirmDeleteEmployers" xml:space="preserve">
    <value>确定要删除以下雇主吗？\n\n{0}\n\n删除后将同时清理相关的班次和工作记录数据。</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>删除成功</value>
  </data>
  <data name="SuccessfullyDeletedEmployers" xml:space="preserve">
    <value>成功删除 {0} 个雇主</value>
  </data>
  <data name="DeleteFailed" xml:space="preserve">
    <value>删除失败</value>
  </data>
  <data name="ConfirmDeleteEmployer" xml:space="preserve">
    <value>确定要删除雇主"{0}"吗？这将同时删除所有相关的班次和支付记录。</value>
  </data>
  <data name="EmployerDeleted" xml:space="preserve">
    <value>雇主"{0}"已被删除</value>
  </data>

  <!-- NotificationHistoryViewModel Messages -->
  <data name="LoadingNotificationHistory" xml:space="preserve">
    <value>正在加载通知历史...</value>
  </data>
  <data name="LoadedNotificationCount" xml:space="preserve">
    <value>已加载 {0} 条通知记录</value>
  </data>
  <data name="LoadNotificationHistoryFailed" xml:space="preserve">
    <value>加载通知历史失败: {0}</value>
  </data>
  <data name="LoadingShiftsWithNotifications" xml:space="preserve">
    <value>正在加载已启用通知的班次...</value>
  </data>
  <data name="FoundShiftsWithNotifications" xml:space="preserve">
    <value>找到 {0} 个已启用通知的班次</value>
  </data>
  <data name="LoadShiftNotificationsFailed" xml:space="preserve">
    <value>加载班次通知失败: {0}</value>
  </data>
  <data name="ShiftNotificationCancelled" xml:space="preserve">
    <value>已取消班次通知: {0} - {1:MM/dd HH:mm}</value>
  </data>
  <data name="CancelShiftNotificationFailed" xml:space="preserve">
    <value>取消班次通知失败: {0}</value>
  </data>
  <data name="SendingTestNotification" xml:space="preserve">
    <value>正在发送测试通知...</value>
  </data>
  <data name="TestNotificationFailedNoPermission" xml:space="preserve">
    <value>测试通知失败: 没有通知权限</value>
  </data>
  <data name="TestNotificationTitle" xml:space="preserve">
    <value>📱 通知功能测试</value>
  </data>
  <data name="TestNotificationDescription" xml:space="preserve">
    <value>这是一个测试通知，用于验证通知功能是否正常工作。如果您看到这条通知，说明通知系统运行正常！</value>
  </data>
  <data name="TestNotificationSent" xml:space="preserve">
    <value>测试通知已发送，请查看通知栏</value>
  </data>
  <data name="SendTestNotificationFailed" xml:space="preserve">
    <value>发送测试通知失败: {0}</value>
  </data>
  <data name="NotificationMarkedAsRead" xml:space="preserve">
    <value>通知已标记为已读</value>
  </data>
  <data name="MarkNotificationFailed" xml:space="preserve">
    <value>标记通知失败: {0}</value>
  </data>
  <data name="NotificationRecordDeleted" xml:space="preserve">
    <value>通知记录已删除</value>
  </data>
  <data name="DeleteNotificationRecordFailed" xml:space="preserve">
    <value>删除通知记录失败</value>
  </data>
  <data name="ClearingAllNotificationRecords" xml:space="preserve">
    <value>正在清除所有通知记录...</value>
  </data>
  <data name="AllNotificationRecordsCleared" xml:space="preserve">
    <value>所有通知记录已清除</value>
  </data>
  <data name="ClearNotificationRecordsFailed" xml:space="preserve">
    <value>清除通知记录失败: {0}</value>
  </data>
  <data name="ResendingNotification" xml:space="preserve">
    <value>正在重新发送通知...</value>
  </data>
  <data name="ResendPrefix" xml:space="preserve">
    <value>重新发送: {0}</value>
  </data>
  <data name="NotificationResent" xml:space="preserve">
    <value>通知已重新发送</value>
  </data>
  <data name="ResendNotificationFailed" xml:space="preserve">
    <value>重新发送通知失败: {0}</value>
  </data>
  <data name="AllTypes" xml:space="preserve">
    <value>全部类型</value>
  </data>
  <data name="WorkReminder" xml:space="preserve">
    <value>工作提醒</value>
  </data>
  <data name="BreakReminder" xml:space="preserve">
    <value>休息提醒</value>
  </data>
  <data name="PaymentDue" xml:space="preserve">
    <value>支付提醒</value>
  </data>
  <data name="ShiftConflict" xml:space="preserve">
    <value>班次冲突</value>
  </data>
  <data name="SystemUpdate" xml:space="preserve">
    <value>系统更新</value>
  </data>
  <data name="UnknownType" xml:space="preserve">
    <value>未知类型</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>全部状态</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>待发送</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>已发送</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>已读</value>
  </data>
  <data name="Dismissed" xml:space="preserve">
    <value>已忽略</value>
  </data>
  <data name="UnknownStatus" xml:space="preserve">
    <value>未知状态</value>
  </data>
  <data name="TestNotificationMessage" xml:space="preserve">
    <value>这是一个测试通知，用于验证通知功能是否正常工作。</value>
  </data>

  <!-- NotificationSettingsViewModel Messages -->
  <data name="LoadingSettings" xml:space="preserve">
    <value>正在加载设置...</value>
  </data>
  <data name="SettingsLoadComplete" xml:space="preserve">
    <value>设置加载完成</value>
  </data>
  <data name="LoadSettingsFailed" xml:space="preserve">
    <value>加载设置失败: {0}</value>
  </data>
  <data name="SavingSettings" xml:space="preserve">
    <value>正在保存设置...</value>
  </data>
  <data name="SettingsSaveSuccess" xml:space="preserve">
    <value>设置保存成功</value>
  </data>
  <data name="SettingsSaveFailed" xml:space="preserve">
    <value>设置保存失败</value>
  </data>
  <data name="SaveSettingsFailed" xml:space="preserve">
    <value>保存设置失败: {0}</value>
  </data>
  <data name="ResettingSettings" xml:space="preserve">
    <value>正在重置设置...</value>
  </data>
  <data name="SettingsResetToDefault" xml:space="preserve">
    <value>设置已重置为默认值</value>
  </data>
  <data name="ResetSettingsFailed" xml:space="preserve">
    <value>重置设置失败</value>
  </data>
  <data name="ResetSettingsFailedWithError" xml:space="preserve">
    <value>重置设置失败: {0}</value>
  </data>
  <data name="SendingTestNotificationSettings" xml:space="preserve">
    <value>正在发送测试通知...</value>
  </data>
  <data name="NotificationPermissionDeniedCannotTest" xml:space="preserve">
    <value>通知权限被拒绝，无法发送测试通知</value>
  </data>
  <data name="TestNotificationSettingsMessage" xml:space="preserve">
    <value>这是一条测试通知，用于验证通知功能是否正常工作。</value>
  </data>
  <data name="TestNotificationSettingsSent" xml:space="preserve">
    <value>测试通知已发送</value>
  </data>
  <data name="SendTestNotificationSettingsFailed" xml:space="preserve">
    <value>发送测试通知失败: {0}</value>
  </data>
  <data name="RequestingNotificationPermission" xml:space="preserve">
    <value>正在请求通知权限...</value>
  </data>
  <data name="NotificationPermissionGranted" xml:space="preserve">
    <value>通知权限已获得</value>
  </data>
  <data name="NotificationPermissionDeniedManualEnable" xml:space="preserve">
    <value>通知权限被拒绝，请在系统设置中手动开启</value>
  </data>
  <data name="RequestNotificationPermissionFailed" xml:space="preserve">
    <value>请求通知权限失败: {0}</value>
  </data>
  <data name="FiveMinutesBefore" xml:space="preserve">
    <value>5分钟前</value>
  </data>
  <data name="TenMinutesBefore" xml:space="preserve">
    <value>10分钟前</value>
  </data>
  <data name="FifteenMinutesBefore" xml:space="preserve">
    <value>15分钟前</value>
  </data>
  <data name="ThirtyMinutesBefore" xml:space="preserve">
    <value>30分钟前</value>
  </data>
  <data name="OneHourBefore" xml:space="preserve">
    <value>1小时前</value>
  </data>
  <data name="EveryOneHour" xml:space="preserve">
    <value>每1小时</value>
  </data>
  <data name="EveryTwoHours" xml:space="preserve">
    <value>每2小时</value>
  </data>
  <data name="EveryThreeHours" xml:space="preserve">
    <value>每3小时</value>
  </data>
  <data name="EveryFourHours" xml:space="preserve">
    <value>每4小时</value>
  </data>
  <data name="SameDay" xml:space="preserve">
    <value>当天</value>
  </data>
  <data name="OneDayBefore" xml:space="preserve">
    <value>1天前</value>
  </data>
  <data name="TwoDaysBefore" xml:space="preserve">
    <value>2天前</value>
  </data>
  <data name="ThreeDaysBefore" xml:space="preserve">
    <value>3天前</value>
  </data>
  <data name="OneWeekBefore" xml:space="preserve">
    <value>1周前</value>
  </data>

  <!-- NotificationTestViewModel Messages -->
  <data name="TestNotificationTitle" xml:space="preserve">
    <value>测试通知</value>
  </data>
  <data name="TestNotificationDefaultMessage" xml:space="preserve">
    <value>这是一条测试通知消息</value>
  </data>
  <data name="SendingInstantNotification" xml:space="preserve">
    <value>正在发送即时通知...</value>
  </data>
  <data name="NotificationPermissionDeniedCannotSendTest" xml:space="preserve">
    <value>通知权限被拒绝，无法发送测试通知</value>
  </data>
  <data name="InstantNotificationSent" xml:space="preserve">
    <value>即时通知已发送</value>
  </data>
  <data name="SendInstantNotificationFailed" xml:space="preserve">
    <value>发送即时通知失败: {0}</value>
  </data>
  <data name="SchedulingDelayedNotification" xml:space="preserve">
    <value>正在安排 {0} 秒后的延迟通知...</value>
  </data>
  <data name="DelayedNotificationScheduled" xml:space="preserve">
    <value>延迟通知已安排，将在 {0} 秒后发送</value>
  </data>
  <data name="ScheduleDelayedNotificationFailed" xml:space="preserve">
    <value>安排延迟通知失败: {0}</value>
  </data>
  <data name="TestingShiftReminder" xml:space="preserve">
    <value>正在测试班次提醒...</value>
  </data>
  <data name="NoEmployersFoundCannotTestShiftReminder" xml:space="preserve">
    <value>没有找到雇主，无法测试班次提醒</value>
  </data>
  <data name="TestLocation" xml:space="preserve">
    <value>测试地点</value>
  </data>
  <data name="TestShiftLocation" xml:space="preserve">
    <value>测试班次地点</value>
  </data>
  <data name="ShiftReminderTestScheduled" xml:space="preserve">
    <value>班次提醒测试已安排，将在1分钟后发送</value>
  </data>
  <data name="TestShiftReminderFailed" xml:space="preserve">
    <value>测试班次提醒失败: {0}</value>
  </data>
  <data name="TestingTodoReminder" xml:space="preserve">
    <value>正在测试待办事项提醒...</value>
  </data>
  <data name="TestTodoItem" xml:space="preserve">
    <value>测试待办事项</value>
  </data>
  <data name="TestTodoDescription" xml:space="preserve">
    <value>这是一个用于测试通知功能的待办事项</value>
  </data>
  <data name="TodoReminderTestScheduled" xml:space="preserve">
    <value>待办事项提醒测试已安排，将在2分钟后发送</value>
  </data>
  <data name="TestTodoReminderFailed" xml:space="preserve">
    <value>测试待办事项提醒失败: {0}</value>
  </data>
  <data name="TestingPaymentReminder" xml:space="preserve">
    <value>正在测试支付提醒...</value>
  </data>
  <data name="NoEmployersFoundCannotTestPaymentReminder" xml:space="preserve">
    <value>没有找到雇主，无法测试支付提醒</value>
  </data>
  <data name="PaymentReminderTestSent" xml:space="preserve">
    <value>支付提醒测试已发送</value>
  </data>
  <data name="TestPaymentReminderFailed" xml:space="preserve">
    <value>测试支付提醒失败: {0}</value>
  </data>
  <data name="TestingBreakReminder" xml:space="preserve">
    <value>正在测试休息提醒...</value>
  </data>
  <data name="TestBreakReminderMessage" xml:space="preserve">
    <value>测试休息提醒：您已连续工作2小时，建议休息10-15分钟</value>
  </data>
  <data name="BreakReminderTestSent" xml:space="preserve">
    <value>休息提醒测试已发送</value>
  </data>
  <data name="TestBreakReminderFailed" xml:space="preserve">
    <value>测试休息提醒失败: {0}</value>
  </data>
  <data name="ReschedulingAllNotifications" xml:space="preserve">
    <value>正在重新安排所有通知...</value>
  </data>
  <data name="AllNotificationsRescheduled" xml:space="preserve">
    <value>所有通知已重新安排</value>
  </data>
  <data name="RescheduleNotificationsFailed" xml:space="preserve">
    <value>重新安排通知失败: {0}</value>
  </data>
  <data name="CheckingNotificationPermission" xml:space="preserve">
    <value>正在检查通知权限...</value>
  </data>
  <data name="NotificationPermissionObtained" xml:space="preserve">
    <value>通知权限已获得</value>
  </data>
  <data name="NotificationPermissionNotObtained" xml:space="preserve">
    <value>通知权限未获得</value>
  </data>
  <data name="CheckNotificationPermissionFailed" xml:space="preserve">
    <value>检查通知权限失败: {0}</value>
  </data>
  <data name="RequestingNotificationPermissionTest" xml:space="preserve">
    <value>正在请求通知权限...</value>
  </data>
  <data name="NotificationPermissionObtainedTest" xml:space="preserve">
    <value>通知权限已获得</value>
  </data>
  <data name="NotificationPermissionDeniedTest" xml:space="preserve">
    <value>通知权限被拒绝</value>
  </data>
  <data name="RequestNotificationPermissionTestFailed" xml:space="preserve">
    <value>请求通知权限失败: {0}</value>
  </data>
</root>
