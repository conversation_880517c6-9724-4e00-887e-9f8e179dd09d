// 引入必要的命名空间 / Import necessary namespaces
using Scheduler.Models;                // 数据模型 / Data models
using System.Collections.ObjectModel; // 可观察集合 / Observable collections

namespace Scheduler.Services
{
    /// <summary>
    /// 演示数据服务 / Demo Data Service
    /// 提供真实可信的示例数据用于演示和测试
    /// Provides realistic and credible sample data for demonstration and testing
    /// </summary>
    public class DemoDataService
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化演示数据服务 / Initialize demo data service
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        public DemoDataService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// 检查是否需要创建演示数据 / Check if demo data needs to be created
        /// 根据数据库状态和用户设置决定是否创建演示数据
        /// Decide whether to create demo data based on database state and user settings
        /// </summary>
        /// <returns>是否需要创建演示数据 / Whether demo data needs to be created</returns>
        public async Task<bool> ShouldCreateDemoDataAsync()
        {
            try
            {
                // 检查是否用户手动重置了数据库（如果是，则不自动创建演示数据） / Check if user manually reset database
                var userResetFlag = Preferences.Get("UserManuallyResetDatabase", false);
                if (userResetFlag)
                {
                    System.Diagnostics.Debug.WriteLine("检测到用户手动重置数据库，跳过演示数据创建 / Detected user manual database reset, skipping demo data creation");
                    return false;
                }

                // 检查数据库是否为空（没有雇主和班次） / Check if database is empty (no employers and shifts)
                var employers = await _databaseService.GetEmployersAsync();
                var today = DateTime.Today;
                var shifts = await _databaseService.GetShiftsAsync(today.AddDays(-30), today.AddDays(30));

                var shouldCreate = employers.Count == 0 && shifts.Count == 0;
                System.Diagnostics.Debug.WriteLine($"演示数据创建检查 / Demo data creation check - 雇主数量 / Employers count: {employers.Count}, 班次数量 / Shifts count: {shifts.Count}, 是否需要创建 / Should create: {shouldCreate}");

                return shouldCreate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查演示数据需求失败 / Failed to check demo data requirements: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建完整的演示数据集
        /// </summary>
        public async Task<bool> CreateDemoDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始创建演示数据...");

                // 创建演示雇主
                var employers = await CreateDemoEmployersAsync();
                if (employers.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("创建演示雇主失败");
                    return false;
                }

                // 创建演示班次
                var shifts = await CreateDemoShiftsAsync(employers);
                System.Diagnostics.Debug.WriteLine($"创建了 {shifts.Count} 个演示班次");

                // 创建演示工作记录
                var workRecords = await CreateDemoWorkRecordsAsync(shifts);
                System.Diagnostics.Debug.WriteLine($"创建了 {workRecords.Count} 个演示工作记录");

                // 创建演示薪资记录
                var paymentRecords = await CreateDemoPaymentRecordsAsync(employers, workRecords);
                System.Diagnostics.Debug.WriteLine($"创建了 {paymentRecords.Count} 个演示薪资记录");

                System.Diagnostics.Debug.WriteLine("演示数据创建完成！");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建演示数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建演示雇主数据
        /// </summary>
        private async Task<List<Employer>> CreateDemoEmployersAsync()
        {
            var employers = new List<Employer>
            {
                new Employer
                {
                    Name = "Sunrise Cafe",
                    ContactInfo = "Sarah Johnson - <EMAIL> - +61 2 9876 5432",
                    HourlyRate = 28.50m,
                    IsActive = true,
                    Notes = "Friendly cafe in CBD. Flexible hours, great team environment.",
                    CreatedAt = DateTime.Now.AddDays(-30),
                    UpdatedAt = DateTime.Now.AddDays(-5)
                },
                new Employer
                {
                    Name = "TechFlow Solutions",
                    ContactInfo = "Michael Chen - <EMAIL> - +61 3 8765 4321",
                    HourlyRate = 35.00m,
                    IsActive = true,
                    Notes = "IT consulting company. Remote work available. Professional environment.",
                    CreatedAt = DateTime.Now.AddDays(-25),
                    UpdatedAt = DateTime.Now.AddDays(-3)
                },
                new Employer
                {
                    Name = "Green Garden Restaurant",
                    ContactInfo = "Emma Wilson - <EMAIL> - +61 7 7654 3210",
                    HourlyRate = 26.75m,
                    IsActive = true,
                    Notes = "Busy restaurant with excellent tips. Weekend shifts available.",
                    CreatedAt = DateTime.Now.AddDays(-20),
                    UpdatedAt = DateTime.Now.AddDays(-2)
                }
            };

            var createdEmployers = new List<Employer>();
            foreach (var employer in employers)
            {
                var created = await _databaseService.SaveEmployerAsync(employer);
                if (created > 0)
                {
                    employer.Id = created; // 设置生成的ID
                    createdEmployers.Add(employer);
                    System.Diagnostics.Debug.WriteLine($"创建演示雇主: {employer.Name}");
                }
            }

            return createdEmployers;
        }

        /// <summary>
        /// 创建演示班次数据
        /// </summary>
        private async Task<List<Shift>> CreateDemoShiftsAsync(List<Employer> employers)
        {
            var shifts = new List<Shift>();
            var random = new Random();
            var today = DateTime.Today;

            foreach (var employer in employers)
            {
                // 为每个雇主创建过去、今天和未来的班次
                for (int i = -7; i <= 14; i++) // 过去7天到未来14天
                {
                    var shiftDate = today.AddDays(i);
                    
                    // 跳过一些日期，让数据更真实
                    if (random.Next(0, 3) == 0) continue;

                    var shift = new Shift
                    {
                        EmployerId = employer.Id,
                        StartTime = shiftDate.AddHours(9 + random.Next(0, 4)), // 9AM-1PM 开始
                        EndTime = shiftDate.AddHours(17 + random.Next(0, 3)), // 5PM-8PM 结束
                        Location = "Work Location", // 简化的位置信息
                        Notes = GetRandomShiftNote(),
                        Status = GetShiftStatus(i),
                        CreatedAt = DateTime.Now.AddDays(i - 1),
                        UpdatedAt = DateTime.Now.AddDays(i - 1)
                    };

                    shifts.Add(shift);
                }
            }

            var createdShifts = new List<Shift>();
            foreach (var shift in shifts)
            {
                var created = await _databaseService.SaveShiftAsync(shift);
                if (created > 0)
                {
                    shift.Id = created; // 设置生成的ID
                    createdShifts.Add(shift);
                }
            }

            return createdShifts;
        }

        /// <summary>
        /// 创建演示工作记录
        /// </summary>
        private async Task<List<WorkRecord>> CreateDemoWorkRecordsAsync(List<Shift> shifts)
        {
            var workRecords = new List<WorkRecord>();
            var random = new Random();

            // 只为过去的班次创建工作记录
            var pastShifts = shifts.Where(s => s.StartTime.Date < DateTime.Today && s.Status == ShiftStatus.Completed).ToList();

            foreach (var shift in pastShifts)
            {
                var workRecord = new WorkRecord
                {
                    ShiftId = shift.Id,
                    ClockInTime = shift.StartTime.AddMinutes(random.Next(-5, 10)), // 稍微早到或晚到
                    ClockOutTime = shift.EndTime.AddMinutes(random.Next(-10, 15)), // 稍微早走或晚走
                    ActualLocation = shift.Location,
                    Notes = GetRandomWorkNote(),
                    CreatedAt = shift.StartTime.Date.AddHours(23), // 当天晚上创建记录
                    UpdatedAt = shift.StartTime.Date.AddHours(23)
                };

                // 计算实际工作时间
                if (workRecord.ClockOutTime.HasValue && workRecord.ClockInTime.HasValue)
                {
                    var duration = workRecord.ClockOutTime.Value - workRecord.ClockInTime.Value;
                    workRecord.ActualHours = duration.TotalHours;
                }

                workRecords.Add(workRecord);
            }

            var createdRecords = new List<WorkRecord>();
            foreach (var record in workRecords)
            {
                var created = await _databaseService.SaveWorkRecordAsync(record);
                if (created > 0)
                {
                    record.Id = created; // 设置生成的ID
                    createdRecords.Add(record);
                }
            }

            return createdRecords;
        }

        /// <summary>
        /// 创建演示薪资记录
        /// </summary>
        private async Task<List<PaymentRecord>> CreateDemoPaymentRecordsAsync(List<Employer> employers, List<WorkRecord> workRecords)
        {
            var paymentRecords = new List<PaymentRecord>();
            var random = new Random();
            var today = DateTime.Today;

            foreach (var employer in employers)
            {
                // 获取该雇主的工作记录
                var employerWorkRecords = workRecords.Where(wr =>
                {
                    // 通过班次ID找到对应的雇主
                    var shift = _databaseService.GetShiftAsync(wr.ShiftId).Result;
                    return shift?.EmployerId == employer.Id;
                }).ToList();

                if (employerWorkRecords.Count == 0) continue;

                // 按周分组创建薪资记录
                var weeklyGroups = employerWorkRecords
                    .Where(wr => wr.ClockInTime.HasValue)
                    .GroupBy(wr => GetWeekStart(wr.ClockInTime!.Value))
                    .OrderBy(g => g.Key);

                foreach (var weekGroup in weeklyGroups)
                {
                    var weekStart = weekGroup.Key;
                    var weekEnd = weekStart.AddDays(6);
                    var weekRecords = weekGroup.ToList();

                    // 计算总工作时间
                    var totalHours = weekRecords.Sum(wr => wr.ActualHours ?? 0);
                    if (totalHours <= 0) continue;

                    // 计算加班时间（超过38小时的部分）
                    var regularHours = Math.Min(totalHours, 38);
                    var overtimeHours = Math.Max(0, totalHours - 38);

                    // 计算薪资
                    var baseAmount = (decimal)regularHours * employer.HourlyRate;
                    var overtimeAmount = (decimal)overtimeHours * employer.HourlyRate * 1.5m; // 加班费1.5倍
                    var bonusAmount = random.Next(0, 3) == 0 ? random.Next(20, 100) : 0; // 随机奖金
                    var totalAmount = baseAmount + overtimeAmount + bonusAmount;

                    // 确定支付状态（过去的记录大部分已支付，最近的可能待支付）
                    var daysSinceWeekEnd = (today - weekEnd).Days;
                    var status = daysSinceWeekEnd > 14 ? PaymentStatus.Paid :
                                (daysSinceWeekEnd > 7 ? (random.Next(0, 2) == 0 ? PaymentStatus.Paid : PaymentStatus.Pending) :
                                PaymentStatus.Pending);

                    var paymentRecord = new PaymentRecord
                    {
                        EmployerId = employer.Id,
                        PeriodStart = weekStart,
                        PeriodEnd = weekEnd,
                        TotalHours = totalHours,
                        RegularHours = regularHours,
                        OvertimeHours = overtimeHours,
                        BaseAmount = baseAmount,
                        OvertimeAmount = overtimeAmount,
                        BonusAmount = bonusAmount,
                        DeductionAmount = 0,
                        TotalAmount = totalAmount,
                        Status = status,
                        ExpectedPaymentDate = weekEnd.AddDays(7), // 一周后支付
                        ActualPaymentDate = status == PaymentStatus.Paid ? weekEnd.AddDays(random.Next(7, 14)) : null,
                        PaymentMethod = status == PaymentStatus.Paid ? GetRandomPaymentMethod() : null,
                        Notes = GetRandomPaymentNote(),
                        CreatedAt = weekEnd.AddDays(1),
                        UpdatedAt = status == PaymentStatus.Paid ? weekEnd.AddDays(random.Next(7, 14)) : weekEnd.AddDays(1)
                    };

                    paymentRecords.Add(paymentRecord);
                }
            }

            // 保存到数据库
            var createdRecords = new List<PaymentRecord>();
            foreach (var record in paymentRecords)
            {
                var created = await _databaseService.SavePaymentRecordAsync(record);
                if (created > 0)
                {
                    record.Id = created;
                    createdRecords.Add(record);
                    System.Diagnostics.Debug.WriteLine($"创建薪资记录: {record.PeriodStart:yyyy-MM-dd} - {record.PeriodEnd:yyyy-MM-dd}, ${record.TotalAmount:F2}, 状态: {record.Status}");
                }
            }

            return createdRecords;
        }

        /// <summary>
        /// 获取一周的开始日期（周一）
        /// </summary>
        private DateTime GetWeekStart(DateTime date)
        {
            var dayOfWeek = (int)date.DayOfWeek;
            var daysToSubtract = dayOfWeek == 0 ? 6 : dayOfWeek - 1; // 周日为0，需要减6天到周一
            return date.Date.AddDays(-daysToSubtract);
        }

        /// <summary>
        /// 获取随机支付方式
        /// </summary>
        private string GetRandomPaymentMethod()
        {
            var methods = new[] { "Bank Transfer", "Cash", "Cheque", "PayPal", "Direct Deposit" };
            return methods[new Random().Next(methods.Length)];
        }

        /// <summary>
        /// 获取随机薪资记录备注
        /// </summary>
        private string GetRandomPaymentNote()
        {
            var notes = new[]
            {
                "Regular weekly payment",
                "Includes overtime bonus",
                "Performance bonus included",
                "Holiday pay adjustment",
                "Excellent work this week",
                "On-time payment",
                ""
            };
            return notes[new Random().Next(notes.Length)];
        }

        /// <summary>
        /// 根据日期偏移获取班次状态
        /// </summary>
        private ShiftStatus GetShiftStatus(int dayOffset)
        {
            if (dayOffset < 0) return ShiftStatus.Completed; // 过去的班次
            if (dayOffset == 0) return ShiftStatus.Scheduled; // 今天的班次
            return ShiftStatus.Scheduled; // 未来的班次
        }

        /// <summary>
        /// 获取随机班次备注
        /// </summary>
        private string GetRandomShiftNote()
        {
            var notes = new[]
            {
                "Regular shift",
                "Busy period expected",
                "Training new staff",
                "Special event coverage",
                "Weekend premium rate",
                "Flexible end time",
                "Cover for colleague",
                ""
            };
            return notes[new Random().Next(notes.Length)];
        }

        /// <summary>
        /// 获取随机工作记录备注
        /// </summary>
        private string GetRandomWorkNote()
        {
            var notes = new[]
            {
                "Smooth shift, good teamwork",
                "Very busy day, excellent tips",
                "Helped train new employee",
                "Handled customer complaints well",
                "Overtime approved by manager",
                "Quiet day, finished early",
                "Special project completed",
                ""
            };
            return notes[new Random().Next(notes.Length)];
        }

        /// <summary>
        /// 检查是否存在演示数据
        /// </summary>
        public async Task<bool> HasDemoDataAsync()
        {
            try
            {
                var employers = await _databaseService.GetEmployersAsync();
                var demoEmployerNames = new[] { "Sunrise Cafe", "TechFlow Solutions", "Green Garden Restaurant" };
                
                return employers.Any(e => demoEmployerNames.Contains(e.Name));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查演示数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取演示数据统计信息
        /// </summary>
        public async Task<string> GetDemoDataSummaryAsync()
        {
            try
            {
                var employers = await _databaseService.GetEmployersAsync();
                var today = DateTime.Today;
                var shifts = await _databaseService.GetShiftsAsync(today.AddDays(-30), today.AddDays(30));
                var workRecords = await _databaseService.GetWorkRecordsAsync();
                var paymentRecords = await _databaseService.GetPaymentRecordsAsync();

                var summary = $"演示数据统计:\n";
                summary += $"- 雇主: {employers.Count} 个\n";
                summary += $"- 班次: {shifts.Count} 个\n";
                summary += $"- 工作记录: {workRecords.Count} 个\n";
                summary += $"- 薪资记录: {paymentRecords.Count} 个\n";
                summary += $"- 今日班次: {shifts.Count(s => s.StartTime.Date == DateTime.Today)} 个\n";
                summary += $"- 未来班次: {shifts.Count(s => s.StartTime.Date > DateTime.Today)} 个\n";
                summary += $"- 已支付薪资: {paymentRecords.Count(p => p.Status == PaymentStatus.Paid)} 个\n";
                summary += $"- 待支付薪资: {paymentRecords.Count(p => p.Status == PaymentStatus.Pending)} 个";

                return summary;
            }
            catch (Exception ex)
            {
                return $"获取演示数据统计失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 设置用户手动重置数据库标记
        /// </summary>
        public void SetUserManualResetFlag()
        {
            Preferences.Set("UserManuallyResetDatabase", true);
            System.Diagnostics.Debug.WriteLine("已设置用户手动重置数据库标记，将阻止自动创建演示数据");
        }

        /// <summary>
        /// 清除用户手动重置数据库标记（允许重新创建演示数据）
        /// </summary>
        public void ClearUserManualResetFlag()
        {
            Preferences.Remove("UserManuallyResetDatabase");
            System.Diagnostics.Debug.WriteLine("已清除用户手动重置数据库标记，允许自动创建演示数据");
        }

        /// <summary>
        /// 检查是否设置了用户手动重置标记
        /// </summary>
        public bool HasUserManualResetFlag()
        {
            return Preferences.Get("UserManuallyResetDatabase", false);
        }
    }
}
