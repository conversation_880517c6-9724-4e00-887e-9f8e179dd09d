// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM组件模型 / MVVM component model
using CommunityToolkit.Mvvm.Input;           // MVVM输入命令 / MVVM input commands
using CommunityToolkit.Mvvm.Messaging;       // MVVM消息传递 / MVVM messaging
using Scheduler.Models;                       // 数据模型 / Data models
using Scheduler.Services;                     // 应用服务 / Application services
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 待办事项列表视图模型 / Todo List ViewModel
    /// 管理一般待办事项的创建、编辑和完成
    /// Manages creation, editing and completion of general todo items
    /// </summary>
    public partial class TodoListViewModel : BaseViewModel, IRecipient<DataResetMessage>
    {
        // 数据库服务实例 / Database service instance
        private readonly DatabaseService _databaseService;
        // 待办事项服务实例 / Todo service instance
        private readonly TodoService _todoService;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化待办事项列表视图模型 / Initialize todo list view model
        /// </summary>
        /// <param name="databaseService">数据库服务 / Database service</param>
        /// <param name="todoService">待办事项服务 / Todo service</param>
        public TodoListViewModel(DatabaseService databaseService, TodoService todoService)
        {
            _databaseService = databaseService;
            _todoService = todoService;
            Title = "Todo List";

            // 初始化集合 / Initialize collections
            PendingTodoItems = new ObservableCollection<TodoItem>();
            RecentCompletedTodoItems = new ObservableCollection<TodoItem>();

            // 注册消息监听 / Register message listeners
            WeakReferenceMessenger.Default.Register<DataResetMessage>(this);
        }

        #region Observable Properties

        /// <summary>
        /// 未完成的待办事项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TodoItem> pendingTodoItems;

        /// <summary>
        /// 最近完成的待办事项（最近5条）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TodoItem> recentCompletedTodoItems;

        /// <summary>
        /// 待办事项统计 - 总数
        /// </summary>
        [ObservableProperty]
        private int totalCount;

        /// <summary>
        /// 待办事项统计 - 未完成数量
        /// </summary>
        [ObservableProperty]
        private int pendingCount;

        /// <summary>
        /// 待办事项统计 - 已完成数量
        /// </summary>
        [ObservableProperty]
        private int completedCount;

        /// <summary>
        /// 待办事项统计 - 逾期数量
        /// </summary>
        [ObservableProperty]
        private int overdueCount;

        /// <summary>
        /// 是否显示已完成的项目
        /// </summary>
        [ObservableProperty]
        private bool showCompletedItems = true;

        /// <summary>
        /// 选中的待办事项类型过滤器
        /// </summary>
        [ObservableProperty]
        private TodoItemType? selectedTypeFilter;

        #endregion

        #region Commands

        /// <summary>
        /// 页面加载命令
        /// </summary>
        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 开始加载待办事项数据");

                // 加载未完成的待办事项
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 加载未完成的待办事项");
                await LoadPendingTodoItemsAsync();

                // 加载最近完成的待办事项
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 加载最近完成的待办事项");
                await LoadRecentCompletedTodoItemsAsync();

                // 加载统计信息
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 加载统计信息");
                await LoadStatisticsAsync();

                // 自动创建待办事项
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 自动创建待办事项");
                await AutoCreateTodoItemsAsync();

                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 待办事项数据加载完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: LoadDataAsync 异常 - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 异常堆栈 - {ex.StackTrace}");
                throw; // 重新抛出异常以便上层处理
            }
        }

        /// <summary>
        /// 切换待办事项完成状态
        /// </summary>
        [RelayCommand]
        private async Task ToggleTodoCompletionAsync(TodoItem todoItem)
        {
            if (todoItem == null) return;

            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 切换待办事项完成状态 - {todoItem.Title}");

                var success = await _todoService.ToggleTodoItemCompletionAsync(todoItem);
                if (success)
                {
                    // 重新加载数据以更新UI
                    await LoadPendingTodoItemsAsync();
                    await LoadRecentCompletedTodoItemsAsync();
                    await LoadStatisticsAsync();

                    System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 待办事项状态已更新 - {todoItem.Title}");
                }
                else
                {
                    var failedToUpdateText = _localizationService.GetLocalizedString("FailedToUpdateTodoItemStatus");
                    await ShowErrorAsync(failedToUpdateText);
                }
            });
        }

        /// <summary>
        /// 删除待办事项
        /// </summary>
        [RelayCommand]
        private async Task DeleteTodoItemAsync(TodoItem todoItem)
        {
            if (todoItem == null) return;

            var confirmDeleteTitle = _localizationService.GetLocalizedString("ConfirmDelete");
            var confirmDeleteMessage = _localizationService.GetString("AreYouSureDeleteTodoItem", todoItem.Title);
            var deleteText = _localizationService.GetLocalizedString("Delete");
            var cancelText = _localizationService.GetLocalizedString("Cancel");

            var confirmed = await Application.Current!.MainPage!.DisplayAlert(
                confirmDeleteTitle,
                confirmDeleteMessage,
                deleteText,
                cancelText);

            if (!confirmed) return;

            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 删除待办事项 - {todoItem.Title}");

                await _todoService.DeleteTodoItemAsync(todoItem.Id);

                // 重新加载数据
                await LoadPendingTodoItemsAsync();
                await LoadRecentCompletedTodoItemsAsync();
                await LoadStatisticsAsync();

                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 待办事项已删除 - {todoItem.Title}");
            });
        }

        /// <summary>
        /// 切换显示已完成项目
        /// </summary>
        [RelayCommand]
        private async Task ToggleShowCompletedAsync()
        {
            ShowCompletedItems = !ShowCompletedItems;
            if (ShowCompletedItems)
            {
                await LoadRecentCompletedTodoItemsAsync();
            }
            else
            {
                RecentCompletedTodoItems.Clear();
            }
        }

        /// <summary>
        /// 应用类型过滤器
        /// </summary>
        [RelayCommand]
        private async Task ApplyTypeFilterAsync(TodoItemType? filterType)
        {
            SelectedTypeFilter = filterType;
            await LoadPendingTodoItemsAsync();
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        /// <summary>
        /// 同步待办事项状态
        /// </summary>
        [RelayCommand]
        private async Task SyncTodoStatusAsync()
        {
            await SafeExecuteAsync(async () =>
            {
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 开始同步待办事项状态");

                var updatedCount = await _todoService.SyncTodoItemStatusAsync();

                if (updatedCount > 0)
                {
                    await LoadDataAsync();
                    var syncedStatusText = _localizationService.GetString("SyncedTodoItemStatus", updatedCount);
                    await ShowSuccessAsync(syncedStatusText);
                }
                else
                {
                    var allUpToDateText = _localizationService.GetLocalizedString("AllTodoItemStatusesUpToDate");
                    await ShowSuccessAsync(allUpToDateText);
                }

                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 同步完成，更新了 {updatedCount} 个待办事项");
            });
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 加载未完成的待办事项
        /// </summary>
        private async Task LoadPendingTodoItemsAsync()
        {
            try
            {
                List<TodoItem> todoItems;

                if (SelectedTypeFilter.HasValue)
                {
                    var allTodos = await _todoService.GetTodoItemsByTypeAsync(SelectedTypeFilter.Value);
                    todoItems = allTodos.Where(t => !t.IsCompleted).ToList();
                }
                else
                {
                    todoItems = await _todoService.GetPendingTodoItemsAsync();
                }

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    PendingTodoItems.Clear();
                    foreach (var todo in todoItems)
                    {
                        PendingTodoItems.Add(todo);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 加载了 {todoItems.Count} 个未完成待办事项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 加载未完成待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载最近完成的待办事项
        /// </summary>
        private async Task LoadRecentCompletedTodoItemsAsync()
        {
            try
            {
                if (!ShowCompletedItems)
                {
                    MainThread.BeginInvokeOnMainThread(() => RecentCompletedTodoItems.Clear());
                    return;
                }

                var completedTodos = await _todoService.GetRecentCompletedTodoItemsAsync(5);

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    RecentCompletedTodoItems.Clear();
                    foreach (var todo in completedTodos)
                    {
                        RecentCompletedTodoItems.Add(todo);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 加载了 {completedTodos.Count} 个最近完成的待办事项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 加载最近完成待办事项失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 加载统计信息
        /// </summary>
        private async Task LoadStatisticsAsync()
        {
            try
            {
                var (total, pending, completed, overdue) = await _databaseService.GetTodoItemStatisticsAsync();

                TotalCount = total;
                PendingCount = pending;
                CompletedCount = completed;
                OverdueCount = overdue;

                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 统计信息 - 总数:{total}, 未完成:{pending}, 已完成:{completed}, 逾期:{overdue}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 加载统计信息失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 自动创建待办事项
        /// </summary>
        private async Task AutoCreateTodoItemsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 开始自动创建待办事项");

                var shiftTodosCreated = await _todoService.AutoCreateShiftTodoItemsAsync();
                var paymentTodosCreated = await _todoService.AutoCreatePaymentTodoItemsAsync();

                var totalCreated = shiftTodosCreated + paymentTodosCreated;

                if (totalCreated > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 自动创建了 {totalCreated} 个待办事项 (班次:{shiftTodosCreated}, 支付:{paymentTodosCreated})");
                    
                    // 重新加载数据以显示新创建的待办事项
                    await LoadPendingTodoItemsAsync();
                    await LoadStatisticsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 自动创建待办事项失败 - {ex.Message}");
            }
        }

        #endregion

        #region Message Handlers

        /// <summary>
        /// 接收数据重置消息
        /// </summary>
        public void Receive(DataResetMessage message)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                System.Diagnostics.Debug.WriteLine($"TodoListViewModel: 收到数据重置消息 - {message.ResetTime}");

                // 清空所有数据
                PendingTodoItems.Clear();
                RecentCompletedTodoItems.Clear();

                // 重置统计数据
                TotalCount = 0;
                PendingCount = 0;
                CompletedCount = 0;
                OverdueCount = 0;

                // 重新加载数据（应该为空）
                await LoadDataAsync();

                System.Diagnostics.Debug.WriteLine("TodoListViewModel: 数据重置完成");
            });
        }

        #endregion
    }
}
