// 引入必要的命名空间 / Import necessary namespaces
using CommunityToolkit.Mvvm.ComponentModel;  // MVVM工具包组件模型 / MVVM toolkit component model
using CommunityToolkit.Mvvm.Input;           // MVVM工具包输入命令 / MVVM toolkit input commands
using System.Collections.ObjectModel;        // 可观察集合 / Observable collections
using System;
using System.Threading.Tasks;
using Scheduler.Services;                     // 应用服务 / Application services

namespace Scheduler.ViewModels
{
    /// <summary>
    /// 基础视图模型 / Base ViewModel
    /// 提供所有视图模型的通用功能和属性
    /// Provides common functionality and properties for all view models
    /// </summary>
    public abstract partial class BaseViewModel : ObservableObject
    {
        // 自动关闭提示服务（可选） / Auto-close alert service (optional)
        protected IAutoCloseAlertService? _autoCloseAlertService;

        // 本地化服务（用于获取本地化文本） / Localization service (for getting localized texts)
        protected LocalizationService _localizationService;

        // 忙碌状态属性 / Busy state property
        [ObservableProperty]
        private bool isBusy;

        // 标题属性 / Title property
        [ObservableProperty]
        private string title = string.Empty;

        // 刷新状态属性 / Refresh state property
        [ObservableProperty]
        private bool isRefreshing;

        /// <summary>
        /// 构造函数 / Constructor
        /// 初始化基础视图模型和服务 / Initialize base view model and services
        /// </summary>
        /// <param name="autoCloseAlertService">自动关闭提示服务（可选） / Auto-close alert service (optional)</param>
        protected BaseViewModel(IAutoCloseAlertService? autoCloseAlertService = null)
        {
            _autoCloseAlertService = autoCloseAlertService;
            _localizationService = LocalizationService.Instance;
        }

        /// <summary>
        /// 显示错误消息 / Display error message
        /// 5秒后自动关闭 / Auto-close after 5 seconds
        /// </summary>
        /// <param name="message">错误消息 / Error message</param>
        /// <param name="title">标题（可选） / Title (optional)</param>
        protected async Task ShowErrorAsync(string message, string? title = null)
        {
            var errorTitle = title ?? _localizationService.GetLocalizedString("Error");

            if (_autoCloseAlertService != null)
            {
                // 使用自动关闭服务 / Use auto-close service
                await _autoCloseAlertService.ShowErrorAsync(message, errorTitle);
            }
            else
            {
                // 降级到传统DisplayAlert / Fallback to traditional DisplayAlert
                var okText = _localizationService.GetLocalizedString("OK");
                await Application.Current!.MainPage!.DisplayAlert(errorTitle, message, okText);
            }
        }

        /// <summary>
        /// 显示成功消息 / Display success message
        /// 5秒后自动关闭 / Auto-close after 5 seconds
        /// </summary>
        /// <param name="message">成功消息 / Success message</param>
        /// <param name="title">标题（可选） / Title (optional)</param>
        protected async Task ShowSuccessAsync(string message, string? title = null)
        {
            var successTitle = title ?? _localizationService.GetLocalizedString("Success");

            if (_autoCloseAlertService != null)
            {
                // 使用自动关闭服务 / Use auto-close service
                await _autoCloseAlertService.ShowSuccessAsync(message, successTitle);
            }
            else
            {
                // 降级到传统DisplayAlert / Fallback to traditional DisplayAlert
                var okText = _localizationService.GetLocalizedString("OK");
                await Application.Current!.MainPage!.DisplayAlert(successTitle, message, okText);
            }
        }

        /// <summary>
        /// 显示确认对话框 / Display confirmation dialog
        /// 不自动关闭 / Does not auto-close
        /// </summary>
        /// <param name="message">确认消息 / Confirmation message</param>
        /// <param name="title">标题（可选） / Title (optional)</param>
        /// <param name="accept">确认按钮文本（可选） / Accept button text (optional)</param>
        /// <param name="cancel">取消按钮文本（可选） / Cancel button text (optional)</param>
        /// <returns>用户选择结果 / User selection result</returns>
        protected async Task<bool> ShowConfirmAsync(string message, string? title = null, string? accept = null, string? cancel = null)
        {
            var confirmTitle = title ?? _localizationService.GetLocalizedString("Confirm");
            var acceptText = accept ?? _localizationService.GetLocalizedString("OK");
            var cancelText = cancel ?? _localizationService.GetLocalizedString("Cancel");

            if (_autoCloseAlertService != null)
            {
                // 使用自动关闭服务 / Use auto-close service
                return await _autoCloseAlertService.ShowConfirmAsync(message, confirmTitle, acceptText, cancelText);
            }
            else
            {
                // 降级到传统DisplayAlert / Fallback to traditional DisplayAlert
                return await Application.Current!.MainPage!.DisplayAlert(confirmTitle, message, acceptText, cancelText);
            }
        }

        /// <summary>
        /// Safely execute async operation
        /// </summary>
        protected async Task SafeExecuteAsync(Func<Task> operation, bool showLoading = true)
        {
            try
            {
                if (showLoading)
                    IsBusy = true;

                await operation();
            }
            catch (Exception ex)
            {
                var operationFailedText = _localizationService.GetLocalizedString("OperationFailed");
                await ShowErrorAsync($"{operationFailedText}: {ex.Message}");
            }
            finally
            {
                if (showLoading)
                    IsBusy = false;
            }
        }

        /// <summary>
        /// Safely execute async operation and return result
        /// </summary>
        protected async Task<T?> SafeExecuteAsync<T>(Func<Task<T>> operation, bool showLoading = true)
        {
            try
            {
                if (showLoading)
                    IsBusy = true;

                return await operation();
            }
            catch (Exception ex)
            {
                var operationFailedText = _localizationService.GetLocalizedString("OperationFailed");
                await ShowErrorAsync($"{operationFailedText}: {ex.Message}");
                return default;
            }
            finally
            {
                if (showLoading)
                    IsBusy = false;
            }
        }
    }
}
