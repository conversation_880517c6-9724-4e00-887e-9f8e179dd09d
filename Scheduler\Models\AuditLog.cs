// 引入必要的命名空间 / Import necessary namespaces
using SQLite;                                                    // SQLite数据库 / SQLite database
using DataAnnotations = System.ComponentModel.DataAnnotations;   // 数据注解 / Data annotations

namespace Scheduler.Models
{
    /// <summary>
    /// 操作类型枚举 / Audit Action Enumeration
    /// 定义系统中可审计的操作类型
    /// Defines auditable operation types in the system
    /// </summary>
    public enum AuditAction
    {
        Create = 0,     // 创建 / Create
        Update = 1,     // 更新 / Update
        Delete = 2,     // 删除 / Delete
        Login = 3,      // 登录 / Login
        Logout = 4,     // 登出 / Logout
        ClockIn = 5,    // 打卡上班 / Clock In
        ClockOut = 6,   // 打卡下班 / Clock Out
        Payment = 7     // 支付确认 / Payment Confirmation
    }

    /// <summary>
    /// 审计日志模型 / Audit Log Model
    /// 用于记录系统中的重要操作和变更
    /// Used to record important operations and changes in the system
    /// </summary>
    [Table("AuditLogs")]
    public class AuditLog
    {
        /// <summary>
        /// 主键ID / Primary Key ID
        /// 自动递增的唯一标识符
        /// Auto-incrementing unique identifier
        /// </summary>
        [PrimaryKey, AutoIncrement]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID / User ID
        /// 执行操作的用户标识
        /// Identifier of the user who performed the operation
        /// </summary>
        [Indexed]
        public int? UserId { get; set; }

        /// <summary>
        /// 操作类型 / Action Type
        /// 执行的具体操作类型
        /// Specific type of operation performed
        /// </summary>
        [Indexed]
        public AuditAction Action { get; set; }

        /// <summary>
        /// 实体类型 / Entity Type
        /// 被操作的实体类型名称
        /// Name of the entity type being operated on
        /// </summary>
        [SQLite.MaxLength(50)]
        [Indexed]
        public string? EntityType { get; set; }

        /// <summary>
        /// 实体ID
        /// </summary>
        [Indexed]
        public int? EntityId { get; set; }

        /// <summary>
        /// 操作描述
        /// </summary>
        [SQLite.MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 变更前数据（JSON格式）
        /// </summary>
        [SQLite.MaxLength(5000)]
        public string? OldValues { get; set; }

        /// <summary>
        /// 变更后数据（JSON格式）
        /// </summary>
        [SQLite.MaxLength(5000)]
        public string? NewValues { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [SQLite.MaxLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [SQLite.MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Indexed]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
