<?xml version="1.0" encoding="utf-8" ?>
<!--
    班次管理视图 / Shift Management View
    用于创建、编辑和管理工作班次的综合管理页面
    Comprehensive management page for creating, editing and managing work shifts
-->
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Scheduler.Views.ShiftManagementView"
             Title="Shift Management"
             xmlns:xkt="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             Shell.FlyoutBehavior="Disabled"
             Shell.TabBarIsVisible="False">

    <!-- 返回按钮配置 / Back button configuration -->
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="True" IsVisible="True" />
    </Shell.BackButtonBehavior>

    <ScrollView>
        <VerticalStackLayout Padding="16" Spacing="20">

            <!-- 页面标题和操作按钮 / Page title and action buttons -->
            <Grid ColumnDefinitions="*,Auto,Auto" ColumnSpacing="10">
                <Label Grid.Column="0"
                       Text="Shift Management"
                       FontSize="24"
                       FontAttributes="Bold"
                       VerticalOptions="Center"
                       TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                <!-- 查看班次按钮 / View shifts button -->
                <Button Grid.Column="1"
                        Text="View Shifts"
                        BackgroundColor="#8B5CF6"
                        TextColor="White"
                        FontSize="14"
                        Command="{Binding ViewShiftsCommand}"/>

                <!-- 模板管理按钮 / Template management button -->
                <Button Grid.Column="2"
                        Text="Templates"
                        BackgroundColor="#10B981"
                        TextColor="White"
                        FontSize="14"
                        Command="{Binding ManageTemplatesCommand}"/>
            </Grid>

            <!-- 快速班次创建表单 / Quick shift creation form -->
            <Frame BackgroundColor="{AppThemeBinding Light=#F8F9FA, Dark=#2D3748}"
                   HasShadow="True"
                   CornerRadius="12"
                   Padding="16">

                <VerticalStackLayout Spacing="16">
                    <Label Text="Create New Shift"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>

                    <!-- Employer selection -->
                    <VerticalStackLayout Spacing="8">
                        <Label Text="Select Employer *"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                        <Picker ItemsSource="{Binding Employers}"
                                ItemDisplayBinding="{Binding Name}"
                                SelectedItem="{Binding SelectedEmployer}"
                                Title="Please select an employer"
                                BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                    </VerticalStackLayout>

                    <!-- Hourly rate display and editing -->
                    <VerticalStackLayout Spacing="8" IsVisible="{Binding IsEmployerSelected}">
                        <Label Text="Hourly Rate (Editable)"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                        <Entry Text="{Binding HourlyRate, StringFormat='{0:F2}'}"
                               Placeholder="Enter hourly rate"
                               Keyboard="Numeric"
                               BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                        <Label Text="{Binding SelectedEmployer.Name, StringFormat='Default rate: ${0:F2}/hour'}"
                               FontSize="12"
                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"
                               IsVisible="{Binding IsEmployerSelected}"/>
                    </VerticalStackLayout>

                    <!-- Smart Time Selection -->
                    <VerticalStackLayout Spacing="16">

                        <!-- Start Time -->
                        <VerticalStackLayout Spacing="8">
                            <Label Text="⏰ Start time *"
                                   FontAttributes="Bold"
                                   FontSize="16"
                                   TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                            <Grid ColumnDefinitions="*,*" ColumnSpacing="12">
                                <DatePicker Grid.Column="0"
                                           Date="{Binding StartDate}"
                                           BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                           TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                                <TimePicker Grid.Column="1"
                                           Time="{Binding StartTime}"
                                           BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                           TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                            </Grid>
                        </VerticalStackLayout>

                        <!-- Work Hours Input -->
                        <VerticalStackLayout Spacing="8">
                            <Label Text="⏱️ Work hours *"
                                   FontAttributes="Bold"
                                   FontSize="16"
                                   TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                            <Grid ColumnDefinitions="*,Auto" ColumnSpacing="12">
                                <Entry Grid.Column="0"
                                       Text="{Binding WorkDurationText}"
                                       Placeholder="Entry work hours"
                                       Keyboard="Numeric"
                                       BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                       TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"
                                       FontSize="16"/>
                                <Label Grid.Column="1"
                                       Text="Hours"
                                       VerticalOptions="Center"
                                       FontSize="14"
                                       TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            </Grid>
                            <Label Text="💡 Tip: Decimal input is supported, such as 8.5 means 8 hours and 30 minutes"
                                   FontSize="12"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        </VerticalStackLayout>

                        <!-- Auto-calculated End Time -->
                        <VerticalStackLayout Spacing="8">
                            <Label Text="🏁 End time (automatically calculated)"
                                   FontAttributes="Bold"
                                   FontSize="16"
                                   TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                            <Grid ColumnDefinitions="*,*" ColumnSpacing="12">
                                <DatePicker Grid.Column="0"
                                           Date="{Binding EndDate}"
                                           BackgroundColor="{AppThemeBinding Light=#F3F4F6, Dark=#4B5563}"
                                           TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                                <TimePicker Grid.Column="1"
                                           Time="{Binding EndTime}"
                                           BackgroundColor="{AppThemeBinding Light=#F3F4F6, Dark=#4B5563}"
                                           TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                            </Grid>
                            <Label Text="📝 The end time can be adjusted manually, and the system will automatically recalculate the working hours"
                                   FontSize="12"
                                   TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                        </VerticalStackLayout>

                    </VerticalStackLayout>

                    <!-- Smart Work Preview -->
                    <Frame BackgroundColor="{AppThemeBinding Light=#EBF8FF, Dark=#1E3A8A}"
                           HasShadow="True"
                           CornerRadius="12"
                           Padding="16">
                        <VerticalStackLayout Spacing="12">

                            <!-- Title -->
                            <Label Text="📊 Job preview"
                                   FontSize="16"
                                   FontAttributes="Bold"
                                   TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>

                            <!-- Work Information Grid -->
                            <Grid ColumnDefinitions="*,*,*" ColumnSpacing="16">

                                <!-- Work Hours -->
                                <VerticalStackLayout Grid.Column="0">
                                    <Label Text="Work hours"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                    <Label Text="{Binding WorkDuration, StringFormat='{0:F1} hours'}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                </VerticalStackLayout>

                                <!-- Hourly Rate -->
                                <VerticalStackLayout Grid.Column="1">
                                    <Label Text="Hourly rate"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                    <Label Text="{Binding HourlyRate, StringFormat='${0:F2}/hour'}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                </VerticalStackLayout>

                                <!-- Estimated Revenue -->
                                <VerticalStackLayout Grid.Column="2">
                                    <Label Text="Estimated revenue"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                    <Label Text="{Binding EstimatedEarnings, StringFormat='${0:F2}'}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                </VerticalStackLayout>

                            </Grid>

                            <!-- Time Range Display -->
                            <Frame BackgroundColor="{AppThemeBinding Light=#DBEAFE, Dark=#1E3A8A}"
                                   HasShadow="False"
                                   CornerRadius="8"
                                   Padding="8">
                                <Label Text="{Binding TimeRangeDisplay}"
                                       FontSize="12"
                                       HorizontalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                            </Frame>

                        </VerticalStackLayout>
                    </Frame>

                    <!-- Recurring Shift Settings -->
                    <Frame BackgroundColor="{AppThemeBinding Light=#F0F9FF, Dark=#1E3A8A}"
                           HasShadow="False"
                           CornerRadius="12"
                           Padding="16">
                        <VerticalStackLayout Spacing="16">
                            <!-- Recurring Shift Title and Switch -->
                            <Grid ColumnDefinitions="*,Auto" ColumnSpacing="16">
                                <Label Grid.Column="0"
                                       Text="🔄 Cycle Schedule Settings"
                                       FontSize="18"
                                       FontAttributes="Bold"
                                       VerticalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsRecurrenceEnabled}"
                                        OnColor="#3B82F6"
                                        ThumbColor="White"/>
                            </Grid>

                            <!-- Recurring Settings Details -->
                            <VerticalStackLayout IsVisible="{Binding IsRecurrenceEnabled}" Spacing="16">

                                <!-- Cycle Period Selection -->
                                <VerticalStackLayout Spacing="8">
                                    <Label Text="Cycle period *"
                                           FontAttributes="Bold"
                                           FontSize="14"
                                           TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                    <Picker ItemsSource="{Binding RecurrenceOptions}"
                                            ItemDisplayBinding="{Binding DisplayName}"
                                            SelectedItem="{Binding SelectedRecurrence}"
                                            Title="Select cycle time"
                                            BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                            TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                                    <Label Text="{Binding RecurrenceDescription}"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                                </VerticalStackLayout>

                                <!-- Recurring End Settings -->
                                <Grid ColumnDefinitions="*,*" ColumnSpacing="16">
                                    <VerticalStackLayout Grid.Column="0" Spacing="8">
                                        <Label Text="End date"
                                               FontAttributes="Bold"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                        <DatePicker Date="{Binding RecurrenceEndDate}"
                                                   BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                                   TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                                    </VerticalStackLayout>

                                    <VerticalStackLayout Grid.Column="1" Spacing="8">
                                        <Label Text="Maximum times (optional)"
                                               FontAttributes="Bold"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                        <Entry Text="{Binding MaxOccurrences}"
                                               Placeholder="No limit"
                                               Keyboard="Numeric"
                                               BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                               TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                                    </VerticalStackLayout>
                                </Grid>

                                <!-- Recurring Preview Info -->
                                <Frame BackgroundColor="{AppThemeBinding Light=#DBEAFE, Dark=#1E3A8A}"
                                       HasShadow="False"
                                       CornerRadius="8"
                                       Padding="12">
                                    <VerticalStackLayout Spacing="4">
                                        <Label Text="📅 Loop preview"
                                               FontAttributes="Bold"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                        <Label Text="{Binding RecurrencePreview}"
                                               FontSize="12"
                                               TextColor="{AppThemeBinding Light=#1E40AF, Dark=#93C5FD}"/>
                                        <Label Text="{Binding EstimatedShiftsCount, StringFormat='Estimated {0} shifts to be created'}"
                                               FontSize="12"
                                               FontAttributes="Bold"
                                               TextColor="{AppThemeBinding Light=#1E40AF, Dark=#DBEAFE}"/>
                                    </VerticalStackLayout>
                                </Frame>

                            </VerticalStackLayout>
                        </VerticalStackLayout>
                    </Frame>

                    <!-- Notification Settings (通知设置) -->
                    <Frame BackgroundColor="{AppThemeBinding Light=#F0FDF4, Dark=#14532D}"
                           HasShadow="False"
                           CornerRadius="12"
                           Padding="16">
                        <VerticalStackLayout Spacing="16">

                            <!-- Recurring shift notification restriction warning -->
                            <Frame BackgroundColor="#FEF3C7"
                                   HasShadow="False"
                                   CornerRadius="8"
                                   Padding="12"
                                   IsVisible="{Binding IsNotificationDisabledByRecurrence}">
                                <VerticalStackLayout Spacing="8">
                                    <Label Text="{Binding LocalizedTexts.RecurringShiftNotificationRestriction}"
                                           FontSize="14"
                                           FontAttributes="Bold"
                                           TextColor="#D97706" />
                                    <Label Text="{Binding LocalizedTexts.RecurringShiftNotificationMessage}"
                                           FontSize="12"
                                           TextColor="#92400E"/>
                                </VerticalStackLayout>
                            </Frame>

                            <!-- Notification Settings Title and Switch -->
                            <Grid ColumnDefinitions="*,Auto" ColumnSpacing="16">
                                <Label Grid.Column="0"
                                       Text="🔔 Shift Reminder Notification"
                                       FontSize="18"
                                       FontAttributes="Bold"
                                       VerticalOptions="Center"
                                       TextColor="{AppThemeBinding Light=#15803D, Dark=#BBF7D0}"/>
                                <Switch Grid.Column="1"
                                        IsToggled="{Binding IsReminderEnabled}"
                                        IsEnabled="{Binding IsNotificationSettingsEnabled}"
                                        OnColor="#10B981"
                                        ThumbColor="White"/>
                            </Grid>

                            <!-- Notification Settings Details -->
                            <VerticalStackLayout IsVisible="{Binding IsReminderEnabled}"
                                                 IsEnabled="{Binding IsNotificationSettingsEnabled}"
                                                 Spacing="16">

                                <!-- Reminder Time Selection -->
                                <VerticalStackLayout Spacing="8">
                                    <Label Text="Reminder Time *"
                                           FontAttributes="Bold"
                                           FontSize="14"
                                           TextColor="{AppThemeBinding Light=#15803D, Dark=#86EFAC}"/>
                                    <Picker ItemsSource="{Binding ReminderTimeOptions}"
                                            ItemDisplayBinding="{Binding DisplayName}"
                                            SelectedItem="{Binding SelectedReminderTimeOption}"
                                            Title="Select reminder time"
                                            BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                            TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                                    <Label Text="{Binding SelectedReminderTimeOption.Description}"
                                           FontSize="12"
                                           TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                                </VerticalStackLayout>

                                <!-- Notification Preview Info -->
                                <Frame BackgroundColor="{AppThemeBinding Light=#DCFCE7, Dark=#14532D}"
                                       HasShadow="False"
                                       CornerRadius="8"
                                       Padding="12">
                                    <VerticalStackLayout Spacing="4">
                                        <Label Text="📱 Notification Preview"
                                               FontAttributes="Bold"
                                               FontSize="14"
                                               TextColor="{AppThemeBinding Light=#15803D, Dark=#BBF7D0}"/>
                                        <Label Text="{Binding SelectedReminderMinutes, StringFormat='You will receive a reminder {0} minutes before the shift starts'}"
                                               FontSize="12"
                                               TextColor="{AppThemeBinding Light=#15803D, Dark=#86EFAC}"/>
                                        <Label Text="Notification will respect your quiet hours settings"
                                               FontSize="11"
                                               FontAttributes="Italic"
                                               TextColor="{AppThemeBinding Light=#6B7280, Dark=#9CA3AF}"/>
                                    </VerticalStackLayout>
                                </Frame>

                            </VerticalStackLayout>
                        </VerticalStackLayout>
                    </Frame>

                    <!-- Notes -->
                    <VerticalStackLayout Spacing="8">
                        <Label Text="Notes (Optional)"
                               FontAttributes="Bold"
                               TextColor="{AppThemeBinding Light=#374151, Dark=#D1D5DB}"/>
                        <Editor Text="{Binding Notes}"
                                Placeholder="Add shift notes..."
                                HeightRequest="80"
                                BackgroundColor="{AppThemeBinding Light=White, Dark=#374151}"
                                TextColor="{AppThemeBinding Light=#1F2937, Dark=#F9FAFB}"/>
                    </VerticalStackLayout>

                    <!-- Action buttons -->
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="16">
                        <Button Grid.Column="0"
                                Text="Cancel"
                                BackgroundColor="{AppThemeBinding Light=#6B7280, Dark=#4B5563}"
                                TextColor="White"
                                Command="{Binding CancelCommand}"/>

                        <Button Grid.Column="1"
                                Text="Create Shift"
                                BackgroundColor="#3B82F6"
                                TextColor="White"
                                Command="{Binding CreateShiftCommand}"
                                IsEnabled="{Binding CanCreateShift}"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>

            <!-- Conflict warning display -->
            <Frame IsVisible="{Binding HasConflicts}"
                   BackgroundColor="{AppThemeBinding Light=#FEF2F2, Dark=#7F1D1D}"
                   BorderColor="#EF4444"
                   HasShadow="False"
                   CornerRadius="8"
                   Padding="12">
                <VerticalStackLayout Spacing="8">
                    <Label Text="⚠️ Time Conflicts Detected"
                           FontAttributes="Bold"
                           TextColor="#EF4444"/>
                    <CollectionView ItemsSource="{Binding ConflictingShifts}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <Grid ColumnDefinitions="*,Auto" Padding="4">
                                    <Label Grid.Column="0"
                                           Text="{Binding ConflictDescription}"
                                           FontSize="12"
                                           TextColor="#DC2626"/>
                                    <Button Grid.Column="1"
                                            Text="Resolve"
                                            FontSize="10"
                                            BackgroundColor="#EF4444"
                                            TextColor="White"
                                            Command="{Binding ResolveConflictCommand}"/>
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
